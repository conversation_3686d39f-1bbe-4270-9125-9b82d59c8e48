<?php

namespace Tests\Unit;

use App\Livewire\MarketSizingAnalysisDisplay;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Livewire\Livewire;
use Mockery;
use Tests\TestCase;

class MarketSizingAnalysisDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected Project $project;

    protected ContentGenerationService $mockContentService;

    protected function setUp(): void
    {
        parent::setUp();

        // Fake the queue to prevent actual job dispatch
        Queue::fake();

        // Create project
        $this->project = Project::factory()->create([
            'input_prompt' => 'A revolutionary app for tracking carbon footprint',
        ]);

        $this->mockContentService = Mockery::mock(ContentGenerationService::class);
        $this->app->instance(ContentGenerationService::class, $this->mockContentService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_component_mounts_with_project(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->assertSet('project', $this->project);
        $component->assertSet('analyses', []);
    }

    public function test_component_loads_existing_analyses(): void
    {
        $tamAnalysis = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'market_sizing_tam',
            'content_data' => [
                'market_size_value' => '$50 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Top-down approach using industry reports',
                'confidence_level' => 'High',
                'growth_rate' => '15% annually',
                'time_frame' => '2024-2029',
                'key_insights' => ['Growing environmental awareness', 'Regulatory support'],
                'market_description' => 'Global carbon tracking market',
            ],
        ]);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['tam' => $tamAnalysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->assertSet('analyses', ['tam' => $tamAnalysis]);
    }

    public function test_generate_all_analyses_success(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->call('generateAllAnalyses');

        // Check that the job was dispatched
        Queue::assertPushed(\App\Jobs\GenerateMarketSizingAnalysisJob::class, function ($job) {
            return $job->project->id === $this->project->id &&
                   $job->analysisType === null &&
                   $job->isRegeneration === false;
        });

        // Check that the loading state was properly reset
        $component->assertSet('loadingStates.all', false);
        // The success message should be set since the job dispatch should succeed
        $component->assertSet('showSuccessMessage', true);
        $component->assertSee('Market sizing analysis generation started!');
    }

    public function test_generate_single_analysis_success(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->call('generateAnalysis', 'tam');

        // Check that the job was dispatched
        Queue::assertPushed(\App\Jobs\GenerateMarketSizingAnalysisJob::class, function ($job) {
            return $job->project->id === $this->project->id &&
                   $job->analysisType === 'tam' &&
                   $job->isRegeneration === false;
        });

        $component->assertSet('showSuccessMessage', true);
        $component->assertSee('TAM analysis generation started!');
        $component->assertSet('loadingStates.tam', false);
    }

    public function test_generate_single_analysis_validates_type(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->call('generateAnalysis', 'invalid_type');

        $component->assertSet('errorMessages.invalid_type', 'Invalid analysis type');
    }

    public function test_regenerate_analysis_success(): void
    {
        $existingAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_sam']);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['sam' => $existingAnalysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->call('regenerateAnalysis', 'sam');

        // Check that the job was dispatched
        Queue::assertPushed(\App\Jobs\GenerateMarketSizingAnalysisJob::class, function ($job) {
            return $job->project->id === $this->project->id &&
                   $job->analysisType === 'sam' &&
                   $job->isRegeneration === true;
        });

        $component->assertSet('showSuccessMessage', true);
        $component->assertSee('SAM analysis regeneration started!');
        $component->assertSet('loadingStates.sam', false);
    }

    public function test_has_analysis_returns_correct_boolean(): void
    {
        $analysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_som']);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['som' => $analysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasAnalysis('som'));
        $this->assertFalse($component->instance()->hasAnalysis('tam'));
    }

    public function test_has_all_analyses_returns_correct_boolean(): void
    {
        $tamAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_tam']);
        $samAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_sam']);
        $somAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_som']);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['tam' => $tamAnalysis, 'sam' => $samAnalysis, 'som' => $somAnalysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasAllAnalyses());
    }

    public function test_get_analysis_data_returns_content_data(): void
    {
        $contentData = [
            'market_size_value' => '$25 billion',
            'market_size_currency' => 'USD',
            'calculation_method' => 'Bottom-up approach',
            'confidence_level' => 'Medium',
        ];

        $analysis = GeneratedContent::factory()->make([
            'content_type' => 'market_sizing_tam',
            'content_data' => $contentData,
        ]);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['tam' => $analysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertEquals($contentData, $component->instance()->getAnalysisData('tam'));
        $this->assertNull($component->instance()->getAnalysisData('sam'));
    }

    public function test_get_analysis_title_returns_correct_title(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertEquals('Total Addressable Market', $component->instance()->getAnalysisTitle('tam'));
        $this->assertEquals('Serviceable Addressable Market', $component->instance()->getAnalysisTitle('sam'));
        $this->assertEquals('Serviceable Obtainable Market', $component->instance()->getAnalysisTitle('som'));
    }

    public function test_get_confidence_level_color_returns_correct_classes(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertEquals('text-green-600 dark:text-green-400', $component->instance()->getConfidenceLevelColor('High'));
        $this->assertEquals('text-yellow-600 dark:text-yellow-400', $component->instance()->getConfidenceLevelColor('Medium'));
        $this->assertEquals('text-red-600 dark:text-red-400', $component->instance()->getConfidenceLevelColor('Low'));
    }

    public function test_format_market_size_handles_different_formats(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $this->assertEquals('$50B', $component->instance()->formatMarketSize('$50 billion'));
        $this->assertEquals('$25M', $component->instance()->formatMarketSize('25 million', 'USD'));
        $this->assertEquals('100K EUR', $component->instance()->formatMarketSize('100 thousand', 'EUR'));
        $this->assertEquals('Custom Value', $component->instance()->formatMarketSize('Custom Value'));
    }

    public function test_hide_success_message_clears_message(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        // Set success message first
        $component->set('showSuccessMessage', true);
        $component->set('successMessage', 'Test message');

        $component->call('hideSuccessMessage');

        $component->assertSet('showSuccessMessage', false);
        $component->assertSet('successMessage', '');
    }

    public function test_poll_for_updates_loads_existing_analyses(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->twice() // Once for mount, once for pollForUpdates
            ->with(Mockery::type(Project::class))
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->call('pollForUpdates');

        // Should have called the service again
        $this->assertTrue(true); // Test passes if no exceptions thrown
    }

    public function test_component_renders_successfully(): void
    {
        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->andReturn([]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->assertStatus(200);
        $component->assertSee('No market sizing analysis yet');
    }

    public function test_component_renders_with_existing_analyses(): void
    {
        $tamAnalysis = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'market_sizing_tam',
            'content_data' => [
                'market_size_value' => '$50 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Top-down approach',
                'confidence_level' => 'High',
                'growth_rate' => '15% annually',
                'time_frame' => '2024-2029',
                'key_insights' => ['Growing market', 'Strong demand'],
                'market_description' => 'Global carbon tracking market',
            ],
        ]);

        $this->mockContentService
            ->shouldReceive('getMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn(['tam' => $tamAnalysis]);

        $component = Livewire::test(MarketSizingAnalysisDisplay::class, ['project' => $this->project]);

        $component->assertStatus(200);
        $component->assertSee('Generated');
        $component->assertSee('TAM');
        $component->assertSee('$50B');
        $component->assertSee('High');
    }
} 