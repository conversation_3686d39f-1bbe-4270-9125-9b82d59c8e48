@props([
    'sectionKey',
    'config',
    'content' => null,
    'isLoading' => false,
    'error' => null,
    'generatedAt' => null,
    'isHighlighted' => false,
    'centerContent' => false,
    'showEarlyAdopters' => false
])

<div class="relative border border-gray-300 dark:border-gray-600 rounded-lg h-80 overflow-hidden
    {{ $isHighlighted ? 'bg-blue-50 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600' : 'bg-white dark:bg-gray-800' }}
    {{ $content && !$centerContent ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors' : '' }}"
    @if($content && !$centerContent) wire:click="showFullText('{{ $sectionKey }}')" @endif>
    
    <!-- Content Area -->
    <div class="p-3 h-full flex flex-col">
        <!-- Section Header -->
        <div class="flex items-start justify-between mb-2 flex-shrink-0">
            <div class="flex-1">
                <div class="flex items-center space-x-2 mb-1">
                    <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-gray-600 dark:bg-gray-500 rounded">
                        {{ $config['key'] }}
                    </span>
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white">{{ $config['title'] }}</h4>
                </div>
                @if(!$centerContent)
                    <p class="text-xs text-gray-600 dark:text-gray-300 mt-1 leading-relaxed">{{ $config['description'] }}</p>
                @endif
            </div>
        </div>

        <!-- Section Content -->
        <div class="flex-1 flex flex-col {{ $centerContent ? 'justify-center text-center' : '' }} min-h-0">
            @if($error)
                <div class="text-xs text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/20 p-2 rounded">
                    {{ $error }}
                </div>
            @elseif($isLoading)
                <div class="flex items-center justify-center py-4">
                    <svg class="animate-spin w-4 h-4 text-gray-500 dark:text-gray-300" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="ml-2 text-xs text-gray-600 dark:text-gray-300">Generating...</span>
                </div>
            @elseif($content)
                <div class="flex-1 overflow-hidden relative">
                    <div class="text-xs text-gray-700 dark:text-gray-200 leading-relaxed prose prose-xs dark:prose-invert max-w-none h-full overflow-y-auto {{ $centerContent ? 'flex items-center justify-center' : '' }}">
                    @if($sectionKey === 'unique_value_proposition' && $centerContent)
                        <div class="font-medium text-sm text-gray-900 dark:text-white leading-relaxed prose prose-sm dark:prose-invert max-w-none">{!! \Illuminate\Support\Str::markdown($content) !!}</div>
                    @elseif($sectionKey === 'customer_segments' && $showEarlyAdopters)
                        <!-- Customer Segments with Early Adopters -->
                        <div class="space-y-1">
                            @php
                                $lines = explode("\n", $content);
                                $isEarlyAdopters = false;
                            @endphp
                            @foreach($lines as $line)
                                @if(str_contains(strtolower($line), 'early adopter'))
                                    @php $isEarlyAdopters = true; @endphp
                                    <div class="border-t border-gray-300 dark:border-gray-600 pt-1 mt-1">
                                        <h5 class="font-medium text-xs text-gray-900 dark:text-gray-100 mb-1">Early Adopters</h5>
                                    </div>
                                @endif
                                @if(trim($line))
                                    <div class="{{ $isEarlyAdopters ? 'text-xs' : '' }}">{!! \Illuminate\Support\Str::markdown($line) !!}</div>
                                @endif
                            @endforeach
                        </div>
                    @elseif($sectionKey === 'existing_alternatives')
                        <!-- Existing Alternatives as list -->
                        @php
                            $alternatives = explode("\n", $content);
                            $alternatives = array_filter(array_map('trim', $alternatives));
                        @endphp
                        <ul class="space-y-0.5">
                            @foreach($alternatives as $alternative)
                                @if($alternative && !str_starts_with($alternative, '•'))
                                    <li class="flex items-start">
                                        <span class="w-1 h-1 bg-gray-500 dark:bg-gray-400 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                                        <span>{!! \Illuminate\Support\Str::markdown($alternative) !!}</span>
                                    </li>
                                @else
                                    <li>{!! \Illuminate\Support\Str::markdown(ltrim($alternative, '• ')) !!}</li>
                                @endif
                            @endforeach
                        </ul>
                    @else
                        <!-- Regular content -->
                        {!! \Illuminate\Support\Str::markdown($content) !!}
                    @endif
                </div>
                </div>
            @else
                <!-- Empty state -->
                <div class="flex-1 flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-gray-400 dark:text-gray-500 mb-2">
                            <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Click generate to create content</p>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Fixed Bottom Section with Overlay Shadow -->
    <div class="absolute bottom-0 left-0 right-0">
        <!-- Gradient overlay for better contrast -->
        <div class="h-8 bg-gradient-to-t from-white dark:from-gray-800 to-transparent pointer-events-none"></div>
        
        <!-- Bottom bar with buttons and timestamp -->
        <div class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-3 py-2 flex items-center justify-between">
            <!-- Action Buttons -->
            <div class="flex items-center space-x-2">
                @if(!$content && !$isLoading && !$error)
                    <button 
                        wire:click="generateSection('{{ $sectionKey }}')" 
                        wire:loading.attr="disabled"
                        wire:target="generateSection('{{ $sectionKey }}')"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors"
                        title="Generate section"
                    >
                        <svg wire:loading.remove wire:target="generateSection('{{ $sectionKey }}')" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <svg wire:loading wire:target="generateSection('{{ $sectionKey }}')" class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span wire:loading.remove wire:target="generateSection('{{ $sectionKey }}')">Generate</span>
                        <span wire:loading wire:target="generateSection('{{ $sectionKey }}')">Generating...</span>
                    </button>
                @endif
                
                @if($content && $generatedAt)
                    <button 
                        wire:click="regenerateSection('{{ $sectionKey }}')" 
                        wire:loading.attr="disabled"
                        wire:target="regenerateSection('{{ $sectionKey }}')"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                        title="Regenerate section"
                    >
                        <svg wire:loading.remove wire:target="regenerateSection('{{ $sectionKey }}')" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <svg wire:loading wire:target="regenerateSection('{{ $sectionKey }}')" class="animate-spin w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span wire:loading.remove wire:target="regenerateSection('{{ $sectionKey }}')">Regenerate</span>
                        <span wire:loading wire:target="regenerateSection('{{ $sectionKey }}')">Regenerating...</span>
                    </button>
                @endif
            </div>

            <!-- Timestamp and Status -->
            <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                @if($generatedAt)
                    <span>{{ $generatedAt }}</span>
                @endif
                @if($content && !$centerContent)
                    <span class="text-gray-400 dark:text-gray-500">•</span>
                    <span class="text-gray-400 dark:text-gray-500">Click to expand</span>
                @endif
            </div>
        </div>
    </div>
</div> 