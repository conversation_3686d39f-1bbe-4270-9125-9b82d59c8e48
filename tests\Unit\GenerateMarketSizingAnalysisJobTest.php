<?php

namespace Tests\Unit;

use App\Jobs\GenerateMarketSizingAnalysisJob;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class GenerateMarketSizingAnalysisJobTest extends TestCase
{
    use RefreshDatabase;

    protected Project $project;

    protected ContentGenerationService $mockContentService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create project
        $this->project = Project::factory()->create([
            'input_prompt' => 'A revolutionary app for tracking carbon footprint',
        ]);

        $this->mockContentService = Mockery::mock(ContentGenerationService::class);
        $this->app->instance(ContentGenerationService::class, $this->mockContentService);

        // Mock Log facade to prevent actual logging during tests
        Log::spy();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_job_generates_all_analyses_successfully(): void
    {
        $expectedAnalyses = [
            'tam' => GeneratedContent::factory()->make(['content_type' => 'market_sizing_tam']),
            'sam' => GeneratedContent::factory()->make(['content_type' => 'market_sizing_sam']),
            'som' => GeneratedContent::factory()->make(['content_type' => 'market_sizing_som']),
        ];

        $this->mockContentService
            ->shouldReceive('generateMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn($expectedAnalyses);

        $job = new GenerateMarketSizingAnalysisJob($this->project);
        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('info')
            ->with('Successfully generated all market sizing analyses', [
                'project_id' => $this->project->id,
                'generated_count' => 3,
            ]);
    }

    public function test_job_generates_single_analysis_successfully(): void
    {
        $expectedAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_tam']);

        $this->mockContentService
            ->shouldReceive('generateMarketSizingAnalysisType')
            ->once()
            ->with(Mockery::type(Project::class), 'tam')
            ->andReturn($expectedAnalysis);

        $job = new GenerateMarketSizingAnalysisJob($this->project, 'tam', false);
        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('info')
            ->with('Successfully generated market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => 'tam',
            ]);
    }

    public function test_job_regenerates_analysis_successfully(): void
    {
        $expectedAnalysis = GeneratedContent::factory()->make(['content_type' => 'market_sizing_sam']);

        $this->mockContentService
            ->shouldReceive('regenerateMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class), 'sam')
            ->andReturn($expectedAnalysis);

        $job = new GenerateMarketSizingAnalysisJob($this->project, 'sam', true);
        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('info')
            ->with('Successfully regenerated market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => 'sam',
            ]);
    }

    public function test_job_handles_generation_failure(): void
    {
        $exception = new Exception('API rate limit exceeded');

        $this->mockContentService
            ->shouldReceive('generateMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andThrow($exception);

        $job = new GenerateMarketSizingAnalysisJob($this->project);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API rate limit exceeded');

        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('error')
            ->with('Failed to generate market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => null,
                'is_regeneration' => false,
                'error' => 'API rate limit exceeded',
            ]);
    }

    public function test_job_handles_single_analysis_generation_failure(): void
    {
        $exception = new Exception('Invalid analysis type provided');

        $this->mockContentService
            ->shouldReceive('generateMarketSizingAnalysisType')
            ->once()
            ->with(Mockery::type(Project::class), 'som')
            ->andThrow($exception);

        $job = new GenerateMarketSizingAnalysisJob($this->project, 'som', false);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid analysis type provided');

        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('error')
            ->with('Failed to generate market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => 'som',
                'is_regeneration' => false,
                'error' => 'Invalid analysis type provided',
            ]);
    }

    public function test_job_handles_regeneration_failure(): void
    {
        $exception = new Exception('Content not found for regeneration');

        $this->mockContentService
            ->shouldReceive('regenerateMarketSizingAnalysis')
            ->once()
            ->with(Mockery::type(Project::class), 'tam')
            ->andThrow($exception);

        $job = new GenerateMarketSizingAnalysisJob($this->project, 'tam', true);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Content not found for regeneration');

        $job->handle($this->mockContentService);

        Log::shouldHaveReceived('error')
            ->with('Failed to generate market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => 'tam',
                'is_regeneration' => true,
                'error' => 'Content not found for regeneration',
            ]);
    }

    public function test_job_failed_method_logs_error(): void
    {
        $exception = new Exception('Database connection failed');

        $job = new GenerateMarketSizingAnalysisJob($this->project, 'sam', false);
        $job->failed($exception);

        Log::shouldHaveReceived('error')
            ->with('Market sizing analysis generation job failed', [
                'project_id' => $this->project->id,
                'analysis_type' => 'sam',
                'is_regeneration' => false,
                'error' => 'Database connection failed',
            ]);
    }

    public function test_job_constructor_sets_properties_correctly(): void
    {
        $job = new GenerateMarketSizingAnalysisJob($this->project, 'tam', true);

        $this->assertEquals($this->project->id, $job->project->id);
        $this->assertEquals('tam', $job->analysisType);
        $this->assertTrue($job->isRegeneration);
    }

    public function test_job_constructor_with_default_values(): void
    {
        $job = new GenerateMarketSizingAnalysisJob($this->project);

        $this->assertEquals($this->project->id, $job->project->id);
        $this->assertNull($job->analysisType);
        $this->assertFalse($job->isRegeneration);
    }

    public function test_job_is_queued_on_default_queue(): void
    {
        $job = new GenerateMarketSizingAnalysisJob($this->project);

        // The job should be configured to use the default queue
        // This is set in the constructor with $this->onQueue('default')
        $this->assertTrue(true); // Test passes if constructor doesn't throw
    }
} 