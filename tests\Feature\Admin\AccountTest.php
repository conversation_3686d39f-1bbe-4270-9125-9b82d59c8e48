<?php

use App\Filament\Resources\AccountResource;
use App\Filament\Resources\AccountResource\Pages;
use App\Models\Account;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Features\SupportTesting\Testable;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\assertSoftDeleted;
use function Pest\Laravel\get;
use function Pest\Livewire\livewire;

uses(RefreshDatabase::class);

beforeEach(function () {
    $user = User::factory()->create();
    actingAs($user);

    // Mount the Filament panel
    Filament::setCurrentPanel(Filament::getPanel('admin'));
    Filament::auth()->login($user);
});

test('can render account resource', function () {
    get(AccountResource::getUrl())
        ->assertSuccessful();
});

test('can list accounts', function () {
    Account::query()->delete();
    /** @var Account[] $accounts */
    $accounts = Account::factory()->count(10)->create();

    /** @var Testable $component */
    $component = livewire(Pages\ListAccounts::class);
    $component->assertSuccessful();

    foreach ($accounts as $account) {
        $component->assertSee($account->name);
        $component->assertSee($account->email);
    }
});

test('can render account columns', function () {
    Account::factory()->count(10)->create();

    /** @var Testable $component */
    $component = livewire(Pages\ListAccounts::class);
    $component->assertSuccessful();

    // Verify that the columns exist in the table
    $component->assertSee('Name');
    $component->assertSee('Email');
    $component->assertSee('Phone');
    $component->assertSee('Status');
});

test('can create account', function () {
    /** @var Account $newData */
    $newData = Account::factory()->make();

    /** @var Testable $component */
    $component = livewire(Pages\CreateAccount::class);
    $component->set('data', [
        'name' => $newData->name,
        'email' => $newData->email,
        'phone' => $newData->phone,
        'password' => 'password123',
        'passwordConfirmation' => 'password123',
        'status' => 'active',
    ]);
    $component->call('create');
    $component->assertSet('data.name', $newData->name);

    assertDatabaseHas(Account::class, [
        'name' => $newData->name,
        'email' => $newData->email,
        'phone' => $newData->phone,
        'status' => 'active',
    ]);
});

test('validates account input', function () {
    /** @var Testable $component */
    $component = livewire(Pages\CreateAccount::class);
    $component->set('data', [
        'name' => null,
        'email' => null,
        'phone' => null,
        'password' => null,
        'status' => null,
    ]);
    $component->call('create');
    $component->assertSet('data.name', null);
    $component->assertSet('data.email', null);
    $component->assertSet('data.phone', null);
    $component->assertSet('data.password', null);
    $component->assertSet('data.status', null);
});

test('can edit account', function () {
    /** @var Account $account */
    $account = Account::factory()->create();
    /** @var Account $newData */
    $newData = Account::factory()->make();

    /** @var Testable $component */
    $component = livewire(Pages\EditAccount::class, [
        'record' => $account->getKey(),
    ]);
    $component->fillForm([
        'name' => $newData->name,
        'email' => $newData->email,
        'phone' => $newData->phone,
        'status' => 'active',
    ]);
    $component->call('save');
    $component->assertHasNoFormErrors();

    assertDatabaseHas(Account::class, [
        'id' => $account->id,
        'name' => $newData->name,
        'email' => $newData->email,
        'phone' => $newData->phone,
        'status' => 'active',
    ]);
});

test('can block account', function () {
    /** @var Account $account */
    $account = Account::factory()->create(['status' => 'active']);
    $blockReason = 'Violation of terms';

    // Test the block functionality directly on the model
    $account->block($blockReason);

    assertDatabaseHas(Account::class, [
        'id' => $account->id,
        'status' => 'blocked',
        'block_reason' => $blockReason,
    ]);

    assertDatabaseHas('account_histories', [
        'account_id' => $account->id,
        'action' => 'blocked',
        'description' => $blockReason,
    ]);
});

test('can unblock account', function () {
    /** @var Account $account */
    $account = Account::factory()->create([
        'status' => 'blocked',
        'block_reason' => 'Previous violation',
    ]);

    // Test the unblock functionality directly on the model
    $account->unblock();

    assertDatabaseHas(Account::class, [
        'id' => $account->id,
        'status' => 'active',
        'block_reason' => null,
    ]);

    assertDatabaseHas('account_histories', [
        'account_id' => $account->id,
        'action' => 'unblocked',
    ]);
});

test('can soft delete account', function () {
    /** @var Account $account */
    $account = Account::factory()->create();

    // Test soft delete directly on the model
    $account->delete();

    assertSoftDeleted($account);
});

test('can restore soft deleted account', function () {
    /** @var Account $account */
    $account = Account::factory()->create();
    $account->delete();

    // Restore using the model directly
    $account->restore();

    // Verify the record is restored
    /** @var Account|null $freshAccount */
    $freshAccount = Account::query()->find($account->id);
    expect($freshAccount)->not->toBeNull();
    expect($freshAccount->deleted_at)->toBeNull();
    expect($freshAccount->status)->toBe('active');
});

test('can force delete account', function () {
    /** @var Account $account */
    $account = Account::factory()->create();
    $account->delete(); // Soft delete first

    // Force delete using the model directly
    $account->forceDelete();

    // Verify the record is completely gone
    assertDatabaseMissing('accounts', [
        'id' => $account->id,
    ]);
});

test('validates unique email', function () {
    /** @var Account $existingAccount */
    $existingAccount = Account::factory()->create();

    /** @var Testable $component */
    $component = livewire(Pages\CreateAccount::class);
    $component->set('data', [
        'name' => 'Test Account',
        'email' => $existingAccount->email,
        'phone' => '**********',
        'password' => 'password123',
        'passwordConfirmation' => 'password123',
        'status' => 'active',
    ]);
    $component->call('create');
    $component->assertSet('data.email', $existingAccount->email);
});

test('requires block reason when blocking', function () {
    /** @var Account $account */
    $account = Account::factory()->create(['status' => 'active']);

    // Test that blocking without a reason throws an exception or fails
    try {
        $account->block(''); // Empty reason should fail
        expect(false)->toBeTrue('Expected blocking with empty reason to fail');
    } catch (\Exception $e) {
        // Expected behavior - blocking with empty reason should fail
        expect(true)->toBeTrue();
    }

    // Verify account is still active
    assertDatabaseHas(Account::class, [
        'id' => $account->id,
        'status' => 'active',
        'block_reason' => null,
    ]);
});
