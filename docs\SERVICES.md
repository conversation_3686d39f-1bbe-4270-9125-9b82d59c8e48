# Services Documentation

## Overview

The Venture Discovery Platform uses a service-oriented architecture to handle business logic, AI integrations, and content generation. Services are organized by domain and responsibility.

## Core Services

### OpenAI Service (`app/Services/OpenAiService.php`)

Handles all interactions with OpenAI's API for content generation.

#### Key Methods

```php
class OpenAiService
{
    public function generateCompletion(string $prompt, array $options = []): string
    public function generateChatCompletion(array $messages, array $options = []): string
    public function generateStructuredContent(string $prompt, array $schema): array
    public function estimateTokens(string $text): int
}
```

#### Usage Examples

```php
// Basic text generation
$service = app(OpenAiService::class);
$result = $service->generateCompletion('Generate a business problem statement for a food delivery app');

// Chat-based generation with context
$messages = [
    ['role' => 'system', 'content' => 'You are a business consultant.'],
    ['role' => 'user', 'content' => 'Help me identify key problems in the food delivery market.']
];
$result = $service->generateChatCompletion($messages);

// Structured content generation
$schema = [
    'problems' => 'array of 3-5 key problems',
    'target_audience' => 'primary target audience',
    'market_size' => 'estimated market size'
];
$result = $service->generateStructuredContent($prompt, $schema);
```

#### Configuration

```php
// config/services.php
'openai' => [
    'api_key' => env('OPENAI_API_KEY'),
    'model' => env('OPENAI_MODEL', 'gpt-4'),
    'max_tokens' => env('OPENAI_MAX_TOKENS', 2000),
    'temperature' => env('OPENAI_TEMPERATURE', 0.7),
]
```

### Content Generation Service (`app/Services/ContentGenerationService.php`)

Orchestrates AI content generation for different business model components.

#### Key Methods

```php
class ContentGenerationService
{
    public function generateLeanCanvasSection(Project $project, string $section, ?string $context = null): GeneratedContent
    public function generateCriticalHypotheses(Project $project, ?string $context = null): GeneratedContent
    public function generateInterviewQuestionnaire(Project $project, array $options = []): GeneratedContent
    public function generateStorytellingContent(Project $project, string $type, ?string $context = null): GeneratedContent
}
```

#### Lean Canvas Sections

```php
// Available sections
const LEAN_CANVAS_SECTIONS = [
    'problem',
    'solution',
    'key_metrics',
    'unique_value_proposition',
    'unfair_advantage',
    'channels',
    'customer_segments',
    'cost_structure',
    'revenue_streams'
];

// Generate specific section
$content = $service->generateLeanCanvasSection($project, 'problem', 'Focus on urban millennials');
```

#### Critical Hypotheses Generation

```php
// Generate testable business hypotheses
$hypotheses = $service->generateCriticalHypotheses($project, 'B2B SaaS targeting small businesses');

// Expected output structure
[
    'customer_hypotheses' => [...],
    'problem_hypotheses' => [...],
    'solution_hypotheses' => [...],
    'market_hypotheses' => [...],
    'business_model_hypotheses' => [...]
]
```

#### Interview Questionnaire Generation

```php
// Generate customer interview questions
$questionnaire = $service->generateInterviewQuestionnaire($project, [
    'target_audience' => 'Early adopters',
    'focus_areas' => ['problem', 'solution'],
    'interview_length' => 30 // minutes
]);

// Expected output structure
[
    'introduction' => 'Interview introduction script',
    'demographic_questions' => [...],
    'problem_discovery' => [...],
    'solution_validation' => [...],
    'wrap_up' => [...]
]
```

#### Storytelling Content Generation

```php
// Generate brand and messaging content
$storytelling = $service->generateStorytellingContent($project, 'brand_wheel');

// Available types
const STORYTELLING_TYPES = [
    'brand_wheel',
    'elevator_pitch',
    'value_proposition',
    'brand_names',
    'taglines'
];
```

### Prompt Engineering Service (`app/Services/PromptEngineeringService.php`)

Manages AI prompts and ensures consistent, high-quality outputs.

#### Key Methods

```php
class PromptEngineeringService
{
    public function buildLeanCanvasPrompt(string $section, Project $project, ?string $context = null): string
    public function buildHypothesesPrompt(Project $project, ?string $context = null): string
    public function buildQuestionnairePrompt(Project $project, array $options = []): string
    public function buildStorytellingPrompt(string $type, Project $project, ?string $context = null): string
}
```

#### Prompt Templates

```php
// Lean Canvas Problem Section Prompt
$prompt = $service->buildLeanCanvasPrompt('problem', $project);

// Example output:
"You are an expert business consultant helping entrepreneurs validate their startup ideas.

Project: {$project->name}
Description: {$project->description}

Generate 3-5 key problems that your target customers face related to this business idea.
For each problem:
1. Describe the problem clearly
2. Explain why it matters to customers
3. Estimate how frequently customers encounter this problem
4. Rate the problem's severity (1-10)

Context: {$context}

Format your response as a structured JSON object..."
```

#### Prompt Optimization

```php
// Dynamic prompt building with context
public function buildContextualPrompt(string $basePrompt, array $context): string
{
    $contextString = $this->formatContext($context);
    return $basePrompt . "\n\nAdditional Context:\n" . $contextString;
}

// Token optimization
public function optimizePromptLength(string $prompt, int $maxTokens): string
{
    // Truncate or summarize prompt to fit token limits
}
```

## Background Jobs

### Generate Lean Canvas Section Job (`app/Jobs/GenerateLeanCanvasSection.php`)

Handles asynchronous generation of lean canvas sections.

```php
class GenerateLeanCanvasSection implements ShouldQueue
{
    public function handle(ContentGenerationService $service): void
    {
        try {
            $content = $service->generateLeanCanvasSection(
                $this->project,
                $this->section,
                $this->context
            );
            
            // Broadcast completion event
            broadcast(new LeanCanvasSectionGenerated($content));
            
        } catch (Exception $e) {
            // Handle failure and retry logic
            $this->fail($e);
        }
    }
}
```

### Generate Critical Hypotheses Job (`app/Jobs/GenerateCriticalHypotheses.php`)

```php
class GenerateCriticalHypotheses implements ShouldQueue
{
    public function handle(ContentGenerationService $service): void
    {
        $hypotheses = $service->generateCriticalHypotheses($this->project, $this->context);
        
        // Store and broadcast results
        broadcast(new HypothesesGenerated($hypotheses));
    }
}
```

### Generate Interview Questionnaire Job (`app/Jobs/GenerateInterviewQuestionnaireJob.php`)

```php
class GenerateInterviewQuestionnaireJob implements ShouldQueue
{
    public function handle(ContentGenerationService $service): void
    {
        $questionnaire = $service->generateInterviewQuestionnaire($this->project, $this->options);
        
        broadcast(new QuestionnaireGenerated($questionnaire));
    }
}
```

### Generate Storytelling Content Job (`app/Jobs/GenerateStorytellingContentJob.php`)

```php
class GenerateStorytellingContentJob implements ShouldQueue
{
    public function handle(ContentGenerationService $service): void
    {
        $content = $service->generateStorytellingContent($this->project, $this->type, $this->context);
        
        broadcast(new StorytellingContentGenerated($content));
    }
}
```

## Service Providers

### OpenAI Service Provider (`app/Providers/OpenAiServiceProvider.php`)

Registers OpenAI service with dependency injection.

```php
class OpenAiServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(OpenAiService::class, function ($app) {
            return new OpenAiService(
                config('services.openai.api_key'),
                config('services.openai.model'),
                config('services.openai.max_tokens'),
                config('services.openai.temperature')
            );
        });
    }
}
```

## Events and Listeners

### Content Generation Events

```php
// Event: LeanCanvasSectionGenerated
class LeanCanvasSectionGenerated
{
    public function __construct(
        public GeneratedContent $content
    ) {}
}

// Event: HypothesesGenerated
class HypothesesGenerated
{
    public function __construct(
        public GeneratedContent $hypotheses
    ) {}
}
```

### Event Broadcasting

```php
// Real-time updates via WebSockets
broadcast(new LeanCanvasSectionGenerated($content))->toOthers();

// Listen in frontend
Echo.private(`project.${projectId}`)
    .listen('LeanCanvasSectionGenerated', (e) => {
        // Update UI with new content
        updateLeanCanvasSection(e.content);
    });
```

## Error Handling and Retry Logic

### Service-Level Error Handling

```php
class ContentGenerationService
{
    public function generateLeanCanvasSection(Project $project, string $section, ?string $context = null): GeneratedContent
    {
        try {
            $prompt = $this->promptService->buildLeanCanvasPrompt($section, $project, $context);
            $response = $this->openAiService->generateStructuredContent($prompt, $this->getSchema($section));
            
            return $this->storeGeneratedContent($project, 'lean_canvas', $section, $response);
            
        } catch (OpenAiException $e) {
            Log::error('OpenAI API error', ['error' => $e->getMessage(), 'project' => $project->id]);
            throw new ContentGenerationException('Failed to generate content: ' . $e->getMessage());
            
        } catch (Exception $e) {
            Log::error('Unexpected error in content generation', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
```

### Job Retry Configuration

```php
class GenerateLeanCanvasSection implements ShouldQueue
{
    public $tries = 3;
    public $backoff = [60, 120, 300]; // Exponential backoff
    public $timeout = 300; // 5 minutes
    
    public function retryUntil(): DateTime
    {
        return now()->addMinutes(30);
    }
}
```

## Performance Optimization

### Caching Strategies

```php
// Cache frequently used prompts
Cache::remember("prompt.lean_canvas.{$section}", 3600, function () use ($section, $project) {
    return $this->promptService->buildLeanCanvasPrompt($section, $project);
});

// Cache OpenAI responses for similar requests
$cacheKey = "openai.response." . md5($prompt);
Cache::remember($cacheKey, 1800, function () use ($prompt) {
    return $this->openAiService->generateCompletion($prompt);
});
```

### Queue Optimization

```php
// Priority queues for different content types
dispatch(new GenerateLeanCanvasSection($project, $section))->onQueue('high-priority');
dispatch(new GenerateStorytellingContent($project, $type))->onQueue('low-priority');

// Batch processing for multiple sections
Bus::batch([
    new GenerateLeanCanvasSection($project, 'problem'),
    new GenerateLeanCanvasSection($project, 'solution'),
    new GenerateLeanCanvasSection($project, 'customer_segments'),
])->dispatch();
```

## Testing Services

### Unit Testing

```php
class ContentGenerationServiceTest extends TestCase
{
    public function test_generates_lean_canvas_problem_section()
    {
        $project = Project::factory()->create();
        $service = app(ContentGenerationService::class);
        
        $content = $service->generateLeanCanvasSection($project, 'problem');
        
        $this->assertInstanceOf(GeneratedContent::class, $content);
        $this->assertEquals('lean_canvas', $content->type);
        $this->assertEquals('problem', $content->section);
        $this->assertNotEmpty($content->content);
    }
}
```

### Mocking External Services

```php
public function test_handles_openai_api_failure()
{
    $this->mock(OpenAiService::class, function ($mock) {
        $mock->shouldReceive('generateStructuredContent')
             ->andThrow(new OpenAiException('API rate limit exceeded'));
    });
    
    $service = app(ContentGenerationService::class);
    
    $this->expectException(ContentGenerationException::class);
    $service->generateLeanCanvasSection($project, 'problem');
}
``` 