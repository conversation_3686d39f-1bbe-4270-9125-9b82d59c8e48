we need to build a website like "gozigzag.com" with the same workflow

1- the customer can register and login 
2- on the main screen he will find the command textarea to input his prompt
3- when the customer input the prompt we will use OpenAI APIs to generate some reports and data the data will be a group of tasks 3 groups and under the 3 we have 3 tasks 
a- Business Prototype
- Lean Canvas → Customer Segments
- Lean Canvas → Existing Alternatives
- Lean Canvas
b- Validation
- Critical Hypotheses
- Experiments → Customer Interview
- Experiments → Landing Page
c- Storytelling Central
- Brand Wheel
- Startup Naming
- Elevator Pitch

after generate the tasks we need to get:

Business Prototype
Transform your idea into a tangible blueprint ensuring a solid foundation for your startup journey.

Lean Canvas 
A streamlined one-page business plan template that succinctly captures your startup's vision.

will be a grid cards with details like img1

Validation
Navigate your startup's viability by identifying critical hypotheses and conducting experiments, ensuring you meet market needs.

will be list like img2

Validation Experiments 
Design and execute strategic tests to validate or refute your startup's hypotheses, ensuring data-driven decisions in your entrepreneurial journey.

will be like img3

Storytelling Central
Craft a compelling narrative for your brand, select your startup's name, and captivate audiences with an unforgettable pitch.

will be like img4

a page for Interview Questionnaire
Create a tailored questionnaire for customer discovery and problem exploration.

will be like img5

buil pdr for this app with this tech stack
- Laravel 12
- FilamentPHP 3
- Livewire
- PEST
- PINT
- PHPStan

- we have ready to use project of Laravel 12 and FilamentPHP and PEST, PINT, PHPStan installed and ready to generate to skip the setup step
- build "composer full" command it will be a validation command to make sure that the app match the requests and to clean up the format and style so you need to run it after every task it will contain "composer test, composer format, composer check" 
- composer test: will be for PEST testing
- composer format: will be for PINT formating
- composer check: will be for PHPStan check

make sure that the PRD follow this template 

"<context>
# Overview  
[Provide a high-level overview of your product here. Explain what problem it solves, who it's for, and why it's valuable.]

# Core Features  
[List and describe the main features of your product. For each feature, include:
- What it does
- Why it's important
- How it works at a high level]

# User Experience  
[Describe the user journey and experience. Include:
- User personas
- Key user flows
- UI/UX considerations]
</context>
<PRD>
# Technical Architecture  
[Outline the technical implementation details:
- System components
- Data models
- APIs and integrations
- Infrastructure requirements]

# Development Roadmap  
[Break down the development process into phases:
- MVP requirements
- Future enhancements
- Do not think about timelines whatsoever -- all that matters is scope and detailing exactly what needs to be build in each phase so it can later be cut up into tasks]

# Logical Dependency Chain
[Define the logical order of development:
- Which features need to be built first (foundation)
- Getting as quickly as possible to something usable/visible front end that works
- Properly pacing and scoping each feature so it is atomic but can also be built upon and improved as development approaches]

# Risks and Mitigations  
[Identify potential risks and how they'll be addressed:
- Technical challenges
- Figuring out the MVP that we can build upon
- Resource constraints]

# Appendix  
[Include any additional information:
- Research findings
- Technical specifications]
</PRD>"