<?php

namespace Tests\Unit;

use App\Jobs\GenerateStorytellingContentJob;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class GenerateStorytellingContentJobTest extends TestCase
{
    use RefreshDatabase;

    protected Project $project;

    protected ContentGenerationService $mockContentService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->project = Project::factory()->create([
            'input_prompt' => 'EcoTrack - A mobile app that helps individuals and families track their carbon footprint',
        ]);

        $this->mockContentService = Mockery::mock(ContentGenerationService::class);
        $this->app->instance(ContentGenerationService::class, $this->mockContentService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_job_generates_all_content_when_no_content_type_specified(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);
        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
        ]);
        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
        ]);

        $this->mockContentService
            ->shouldReceive('getStorytellingContent')
            ->once()
            ->with($this->project)
            ->andReturn([]);

        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->with($this->project)
            ->andReturn($brandWheel);

        $this->mockContentService
            ->shouldReceive('generateStartupNaming')
            ->once()
            ->with($this->project)
            ->andReturn($startupNaming);

        $this->mockContentService
            ->shouldReceive('generateElevatorPitch')
            ->once()
            ->with($this->project)
            ->andReturn($elevatorPitch);

        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_regenerates_all_content_when_regenerate_is_true(): void
    {
        $brandWheel = GeneratedContent::factory()->create(['content_type' => 'brand_wheel']);
        $startupNaming = GeneratedContent::factory()->create(['content_type' => 'startup_naming']);
        $elevatorPitch = GeneratedContent::factory()->create(['content_type' => 'elevator_pitch']);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with($this->project, 'brand_wheel')
            ->andReturn($brandWheel);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with($this->project, 'startup_naming')
            ->andReturn($startupNaming);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with($this->project, 'elevator_pitch')
            ->andReturn($elevatorPitch);

        $job = new GenerateStorytellingContentJob($this->project, true, null);
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_generates_specific_brand_wheel_content(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);

        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->with($this->project)
            ->andReturn($brandWheel);

        $job = new GenerateStorytellingContentJob($this->project, false, 'brand_wheel');
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_generates_specific_startup_naming_content(): void
    {
        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
        ]);

        $this->mockContentService
            ->shouldReceive('generateStartupNaming')
            ->once()
            ->with($this->project)
            ->andReturn($startupNaming);

        $job = new GenerateStorytellingContentJob($this->project, false, 'startup_naming');
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_generates_specific_elevator_pitch_content(): void
    {
        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
        ]);

        $this->mockContentService
            ->shouldReceive('generateElevatorPitch')
            ->once()
            ->with($this->project)
            ->andReturn($elevatorPitch);

        $job = new GenerateStorytellingContentJob($this->project, false, 'elevator_pitch');
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_regenerates_specific_content_type(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with($this->project, 'brand_wheel')
            ->andReturn($brandWheel);

        $job = new GenerateStorytellingContentJob($this->project, true, 'brand_wheel');
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_throws_exception_for_invalid_content_type(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid content type: invalid_type');

        $job = new GenerateStorytellingContentJob($this->project, false, 'invalid_type');
        $job->handle($this->mockContentService);
    }

    public function test_job_skips_existing_content_when_not_regenerating(): void
    {
        // Mock existing content
        $this->mockContentService
            ->shouldReceive('getStorytellingContent')
            ->once()
            ->with($this->project)
            ->andReturn([
                'brand_wheel' => GeneratedContent::factory()->create(['content_type' => 'brand_wheel']),
                'startup_naming' => GeneratedContent::factory()->create(['content_type' => 'startup_naming']),
            ]);

        // Should only generate missing elevator pitch
        $this->mockContentService
            ->shouldReceive('generateElevatorPitch')
            ->once()
            ->with($this->project)
            ->andReturn(GeneratedContent::factory()->create(['content_type' => 'elevator_pitch']));

        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_handles_generation_errors_gracefully(): void
    {
        Log::shouldReceive('info')->twice();
        Log::shouldReceive('warning')->once();

        $this->mockContentService
            ->shouldReceive('getStorytellingContent')
            ->once()
            ->with($this->project)
            ->andReturn([]);

        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->with($this->project)
            ->andThrow(new \Exception('Generation failed'));

        $this->mockContentService
            ->shouldReceive('generateStartupNaming')
            ->once()
            ->with($this->project)
            ->andReturn(GeneratedContent::factory()->create(['content_type' => 'startup_naming']));

        $this->mockContentService
            ->shouldReceive('generateElevatorPitch')
            ->once()
            ->with($this->project)
            ->andReturn(GeneratedContent::factory()->create(['content_type' => 'elevator_pitch']));

        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_logs_start_and_completion(): void
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Starting storytelling content generation job', [
                'project_id' => $this->project->id,
                'regenerate' => false,
                'content_type' => 'brand_wheel',
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Storytelling content generation job completed successfully', [
                'project_id' => $this->project->id,
                'content_type' => 'brand_wheel',
            ]);

        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->with($this->project)
            ->andReturn(GeneratedContent::factory()->create(['content_type' => 'brand_wheel']));

        $job = new GenerateStorytellingContentJob($this->project, false, 'brand_wheel');
        $job->handle($this->mockContentService);

        // Assert that the job completed without throwing exceptions
        $this->assertTrue(true);
    }

    public function test_job_logs_and_rethrows_exceptions(): void
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')
            ->once()
            ->with('Storytelling content generation job failed', Mockery::type('array'));

        // Mock the regenerateStorytellingContent method to throw an exception
        // This will trigger the main exception handling since regeneration doesn't use generateIfNotExists
        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with($this->project, 'brand_wheel')
            ->andThrow(new \Exception('Service failure'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Service failure');

        $job = new GenerateStorytellingContentJob($this->project, true, 'brand_wheel');
        $job->handle($this->mockContentService);
    }

    public function test_job_has_correct_tags(): void
    {
        $job = new GenerateStorytellingContentJob($this->project, false, 'brand_wheel');
        $tags = $job->tags();

        $this->assertContains('storytelling-content', $tags);
        $this->assertContains('project:'.$this->project->id, $tags);
        $this->assertContains('content-type:brand_wheel', $tags);
    }

    public function test_job_has_correct_tags_for_all_content(): void
    {
        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $tags = $job->tags();

        $this->assertContains('storytelling-content', $tags);
        $this->assertContains('project:'.$this->project->id, $tags);
        $this->assertContains('all-content', $tags);
    }

    public function test_job_has_backoff_strategy(): void
    {
        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $backoff = $job->backoff();

        $this->assertEquals([30, 60, 120], $backoff);
    }

    public function test_job_has_retry_until_timeout(): void
    {
        $job = new GenerateStorytellingContentJob($this->project, false, null);
        $retryUntil = $job->retryUntil();

        $this->assertInstanceOf(\DateTime::class, $retryUntil);
        $this->assertGreaterThan(now(), $retryUntil);
    }
}
