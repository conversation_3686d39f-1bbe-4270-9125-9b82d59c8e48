# Frontend Documentation

## Overview

The Venture Discovery Platform frontend is built with Filament 3.3, Livewire 3, and TailwindCSS. It provides a modern, reactive user interface for managing startup validation projects.

## Architecture

### Technology Stack
- **Filament 3.3** - Admin panel framework and UI components
- **Livewire 3** - Full-stack reactive components
- **TailwindCSS** - Utility-first CSS framework
- **Alpine.js** - Lightweight JavaScript framework (included with Livewire)
- **Laravel Reverb** - Real-time WebSocket connections

### Component Structure
```
resources/views/
├── components/           # Blade components
│   └── layouts/         # Layout components
├── filament/           # Filament customizations
│   └── app/           # App panel views
└── livewire/          # Livewire component views
    └── partials/      # Reusable partials
```

## Filament Resources

### Project Resource (`app/Filament/App/Resources/ProjectResource.php`)

Main resource for managing startup projects.

#### Resource Configuration
```php
class ProjectResource extends Resource
{
    protected static ?string $model = Project::class;
    protected static ?string $navigationIcon = 'heroicon-o-light-bulb';
    protected static ?string $navigationGroup = 'Projects';
    
    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->required()
                ->maxLength(255),
            Textarea::make('description')
                ->rows(3),
            Select::make('status')
                ->options([
                    'draft' => 'Draft',
                    'active' => 'Active',
                    'completed' => 'Completed',
                    'archived' => 'Archived',
                ])
                ->default('draft'),
        ]);
    }
    
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable(),
                TextColumn::make('status')->badge(),
                TextColumn::make('created_at')->dateTime(),
            ])
            ->filters([
                SelectFilter::make('status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }
}
```

#### Custom Pages
- **CreateProject** - Project creation with wizard
- **EditProject** - Project editing with tabs
- **ViewProject** - Project dashboard with generated content
- **ListProjects** - Project listing with filters

### Account Resource (`app/Filament/Resources/AccountResource.php`)

Admin resource for managing accounts (multi-tenancy).

```php
class AccountResource extends Resource
{
    protected static ?string $model = Account::class;
    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    
    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')->required(),
            TextInput::make('slug')->required(),
            Textarea::make('description'),
            Repeater::make('users')
                ->relationship()
                ->schema([
                    Select::make('user_id')
                        ->relationship('user', 'name'),
                    Select::make('role')
                        ->options(['admin', 'member']),
                ]),
        ]);
    }
}
```

## Livewire Components

### Lean Canvas Display (`app/Livewire/LeanCanvasDisplay.php`)

Interactive lean canvas component with real-time updates.

```php
class LeanCanvasDisplay extends Component
{
    public Project $project;
    public array $sections = [];
    public bool $isGenerating = false;
    
    protected $listeners = [
        'echo:project.{project.id},LeanCanvasSectionGenerated' => 'onSectionGenerated',
    ];
    
    public function mount(Project $project): void
    {
        $this->project = $project;
        $this->loadSections();
    }
    
    public function generateSection(string $section): void
    {
        $this->isGenerating = true;
        
        dispatch(new GenerateLeanCanvasSection($this->project, $section));
        
        $this->dispatch('section-generation-started', section: $section);
    }
    
    public function onSectionGenerated($event): void
    {
        $this->loadSections();
        $this->isGenerating = false;
        
        $this->dispatch('section-generated', content: $event['content']);
    }
    
    private function loadSections(): void
    {
        $this->sections = $this->project
            ->generatedContent()
            ->where('type', 'lean_canvas')
            ->get()
            ->keyBy('section')
            ->toArray();
    }
    
    public function render(): View
    {
        return view('livewire.lean-canvas-display');
    }
}
```

#### Component View
```blade
{{-- resources/views/livewire/lean-canvas-display.blade.php --}}
<div class="grid grid-cols-3 gap-6">
    @foreach($this->getLeanCanvasSections() as $section => $title)
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">{{ $title }}</h3>
                
                @if(!isset($sections[$section]))
                    <x-filament::button 
                        wire:click="generateSection('{{ $section }}')"
                        :disabled="$isGenerating"
                        size="sm"
                    >
                        Generate
                    </x-filament::button>
                @endif
            </div>
            
            @if(isset($sections[$section]))
                <div class="prose prose-sm">
                    {!! $this->formatContent($sections[$section]['content']) !!}
                </div>
            @else
                <div class="text-gray-500 italic">
                    Click generate to create this section
                </div>
            @endif
            
            @if($isGenerating)
                <div class="mt-4">
                    <x-filament::loading-indicator class="h-5 w-5" />
                    <span class="ml-2 text-sm text-gray-600">Generating...</span>
                </div>
            @endif
        </div>
    @endforeach
</div>
```

### Critical Hypotheses Display (`app/Livewire/CriticalHypothesesDisplay.php`)

Component for displaying and managing business hypotheses.

```php
class CriticalHypothesesDisplay extends Component
{
    public Project $project;
    public ?GeneratedContent $hypotheses = null;
    public bool $isGenerating = false;
    
    protected $listeners = [
        'echo:project.{project.id},HypothesesGenerated' => 'onHypothesesGenerated',
    ];
    
    public function generateHypotheses(): void
    {
        $this->isGenerating = true;
        
        dispatch(new GenerateCriticalHypotheses($this->project));
    }
    
    public function onHypothesesGenerated($event): void
    {
        $this->hypotheses = GeneratedContent::find($event['content']['id']);
        $this->isGenerating = false;
    }
    
    public function render(): View
    {
        return view('livewire.critical-hypotheses-display');
    }
}
```

### Interview Questionnaire Display (`app/Livewire/InterviewQuestionnaireDisplay.php`)

Component for customer interview questionnaires.

```php
class InterviewQuestionnaireDisplay extends Component
{
    public Project $project;
    public ?GeneratedContent $questionnaire = null;
    public array $options = [
        'target_audience' => 'Early adopters',
        'focus_areas' => ['problem', 'solution'],
        'interview_length' => 30,
    ];
    
    public function generateQuestionnaire(): void
    {
        dispatch(new GenerateInterviewQuestionnaireJob($this->project, $this->options));
    }
    
    public function render(): View
    {
        return view('livewire.interview-questionnaire-display');
    }
}
```

### Storytelling Central Display (`app/Livewire/StorytellingCentralDisplay.php`)

Component for brand and messaging content.

```php
class StorytellingCentralDisplay extends Component
{
    public Project $project;
    public array $storytellingContent = [];
    public string $selectedType = 'brand_wheel';
    
    public function generateContent(string $type): void
    {
        $this->selectedType = $type;
        
        dispatch(new GenerateStorytellingContentJob($this->project, $type));
    }
    
    public function render(): View
    {
        return view('livewire.storytelling-central-display');
    }
}
```

## Filament Pages

### Dashboard (`app/Filament/App/Pages/Dashboard.php`)

Main dashboard with project overview and statistics.

```php
class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    public function getWidgets(): array
    {
        return [
            ProjectStatsOverview::class,
            RecentProjectsTableWidget::class,
        ];
    }
}
```

### Custom Authentication Pages

#### Login (`app/Filament/App/Pages/Auth/Login.php`)
```php
class Login extends BaseLogin
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getRememberFormComponent(),
            ])
            ->statePath('data');
    }
}
```

#### Register (`app/Filament/App/Pages/Auth/Register.php`)
```php
class Register extends BaseRegister
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getNameFormComponent(),
                $this->getEmailFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getPasswordConfirmationFormComponent(),
            ]);
    }
}
```

## Widgets

### Project Stats Overview (`app/Filament/App/Widgets/ProjectStatsOverview.php`)

Dashboard statistics widget.

```php
class ProjectStatsOverview extends BaseWidget
{
    protected static string $view = 'filament.app.widgets.project-stats-overview';
    
    protected function getStats(): array
    {
        $account = Filament::getTenant();
        
        return [
            Stat::make('Total Projects', $account->projects()->count())
                ->description('All projects in your account')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),
                
            Stat::make('Active Projects', $account->projects()->where('status', 'active')->count())
                ->description('Currently active projects')
                ->descriptionIcon('heroicon-m-play')
                ->color('info'),
                
            Stat::make('Generated Content', $this->getGeneratedContentCount())
                ->description('AI-generated content pieces')
                ->descriptionIcon('heroicon-m-sparkles')
                ->color('warning'),
        ];
    }
    
    private function getGeneratedContentCount(): int
    {
        return GeneratedContent::whereHas('project', function ($query) {
            $query->where('account_id', Filament::getTenant()->id);
        })->count();
    }
}
```

### Recent Projects Table Widget (`app/Filament/App/Widgets/RecentProjectsTableWidget.php`)

Recent projects table for dashboard.

```php
class RecentProjectsTableWidget extends BaseWidget
{
    protected static string $view = 'filament.app.widgets.recent-projects-table';
    
    public function getTableQuery(): Builder
    {
        return Project::query()
            ->where('account_id', Filament::getTenant()->id)
            ->latest()
            ->limit(5);
    }
    
    public function getTableColumns(): array
    {
        return [
            TextColumn::make('name')
                ->searchable()
                ->sortable(),
            TextColumn::make('status')
                ->badge(),
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable(),
        ];
    }
}
```

## Real-time Features

### WebSocket Integration

#### Frontend JavaScript
```javascript
// resources/js/app.js
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT,
    wssPort: import.meta.env.VITE_REVERB_PORT,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});

// Listen for content generation events
Echo.private(`project.${projectId}`)
    .listen('LeanCanvasSectionGenerated', (e) => {
        Livewire.dispatch('section-generated', { content: e.content });
    })
    .listen('HypothesesGenerated', (e) => {
        Livewire.dispatch('hypotheses-generated', { content: e.content });
    });
```

#### Livewire Event Handling
```php
// In Livewire components
protected $listeners = [
    'echo:project.{project.id},LeanCanvasSectionGenerated' => 'handleSectionGenerated',
    'section-generated' => 'refreshSection',
];

public function handleSectionGenerated($event): void
{
    $this->loadSections();
    $this->dispatch('notify', message: 'Section generated successfully!');
}
```

## Styling and Theming

### TailwindCSS Configuration
```javascript
// tailwind.config.js
module.exports = {
    content: [
        './app/Filament/**/*.php',
        './resources/views/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#eff6ff',
                    500: '#3b82f6',
                    600: '#2563eb',
                    700: '#1d4ed8',
                    800: '#1e40af',
                    900: '#1e3a8a',
                },
            },
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
    ],
};
```

### Custom CSS
```css
/* resources/css/filament/app/theme.css */
@import '/vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

/* Custom component styles */
.lean-canvas-grid {
    @apply grid grid-cols-3 gap-6;
}

.canvas-section {
    @apply bg-white rounded-lg shadow-sm border p-6;
}

.generation-loading {
    @apply flex items-center space-x-2 text-gray-600;
}
```

## Form Components

### Custom Form Fields
```php
// Custom form components for project creation
public static function getProjectFormSchema(): array
{
    return [
        Section::make('Project Details')
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn ($state, callable $set) => 
                        $set('slug', Str::slug($state))
                    ),
                    
                TextInput::make('slug')
                    ->required()
                    ->unique(ignoreRecord: true),
                    
                Textarea::make('description')
                    ->rows(3)
                    ->columnSpanFull(),
            ]),
            
        Section::make('Project Settings')
            ->schema([
                Select::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'active' => 'Active',
                        'completed' => 'Completed',
                    ])
                    ->default('draft'),
                    
                TagsInput::make('tags')
                    ->placeholder('Add tags...'),
            ]),
    ];
}
```

## Performance Optimization

### Lazy Loading
```php
// Lazy load heavy components
public function render(): View
{
    return view('livewire.lean-canvas-display', [
        'sections' => $this->sections,
        'isGenerating' => $this->isGenerating,
    ])->lazy();
}
```

### Caching
```php
// Cache expensive computations
public function getGeneratedContentProperty(): Collection
{
    return Cache::remember(
        "project.{$this->project->id}.content",
        300,
        fn () => $this->project->generatedContent
    );
}
```

## Testing Frontend Components

### Livewire Testing
```php
class LeanCanvasDisplayTest extends TestCase
{
    public function test_can_generate_section(): void
    {
        $project = Project::factory()->create();
        
        Livewire::test(LeanCanvasDisplay::class, ['project' => $project])
            ->call('generateSection', 'problem')
            ->assertSet('isGenerating', true)
            ->assertDispatched('section-generation-started');
    }
    
    public function test_handles_section_generated_event(): void
    {
        $project = Project::factory()->create();
        $content = GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'type' => 'lean_canvas',
            'section' => 'problem',
        ]);
        
        Livewire::test(LeanCanvasDisplay::class, ['project' => $project])
            ->call('onSectionGenerated', ['content' => $content->toArray()])
            ->assertSet('isGenerating', false);
    }
}
```

### Browser Testing
```php
class ProjectManagementTest extends DuskTestCase
{
    public function test_can_create_project(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs(User::factory()->create())
                ->visit('/app/projects/create')
                ->type('name', 'Test Project')
                ->type('description', 'Test Description')
                ->press('Create')
                ->assertPathIs('/app/projects/1')
                ->assertSee('Test Project');
        });
    }
}
``` 