<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        // Seed in order of dependencies
        $this->call([
            UserSeeder::class,          // Admin users first
            AccountSeeder::class,       // App users
            ProjectSeeder::class,       // Projects
            GeneratedContentSeeder::class, // Generated content
        ]);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📊 Summary:');
        $this->command->info('   • 7 admin users created (for admin panel access)');
        $this->command->info('   • 21 accounts created (app users including entrepreneurs)');
        $this->command->info('   • 20 projects created with realistic startup ideas');
        $this->command->info('   • Generated content created for applicable projects');
        $this->command->info('');
        $this->command->info('🔑 Login credentials:');
        $this->command->info('   🔐 Admin Panel: <EMAIL> / password');
        $this->command->info('   🔐 Super Admin: <EMAIL> / password');
        $this->command->info('   🔐 Demo Admin: <EMAIL> / password');
        $this->command->info('   👤 App Users: <EMAIL> / password');
        $this->command->info('               <EMAIL> / password');
        $this->command->info('               <EMAIL> / password');
        $this->command->info('               <EMAIL> / password');
    }
}
