<?php

namespace App\Livewire;

use App\Jobs\GenerateInterviewQuestionnaireJob;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class InterviewQuestionnaireDisplay extends Component
{
    public ?Project $project = null;

    public ?GeneratedContent $questionnaire = null;

    public bool $isLoading = false;

    public bool $isRegenerating = false;

    public ?string $error = null;

    public bool $showFullQuestionnaire = false;

    protected ContentGenerationService $contentService;

    public function boot(ContentGenerationService $contentService): void
    {
        $this->contentService = $contentService;
    }

    public function mount(?Project $project = null): void
    {
        if ($project) {
            $this->project = $project;
        }
        $this->loadQuestionnaire();
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            // Try to get from Filament context if available
            if (method_exists($this, 'getOwnerRecord')) {
                $this->project = $this->getOwnerRecord();
            } else {
                // Fallback to first project for testing
                /** @var Project|null $project */
                $project = Project::query()->first();
                $this->project = $project;
            }
        }

        return $this->project;
    }

    public function loadQuestionnaire(): void
    {
        try {
            $this->questionnaire = $this->contentService->getInterviewQuestionnaire($this->getProject());
            $this->error = null;
        } catch (Exception $e) {
            Log::error('Failed to load interview questionnaire', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to load questionnaire: '.$e->getMessage();
        }
    }

    public function generateQuestionnaire(): void
    {
        try {
            $this->isLoading = true;
            $this->error = null;

            // Dispatch job for background generation
            GenerateInterviewQuestionnaireJob::dispatch($this->getProject(), false);

            // Try immediate generation for better UX
            $this->questionnaire = $this->contentService->generateInterviewQuestionnaire($this->getProject());

            Log::info('Interview questionnaire generated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->questionnaire->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate interview questionnaire', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to generate questionnaire. Please try again.';
        } finally {
            $this->isLoading = false;
        }
    }

    public function regenerateQuestionnaire(): void
    {
        try {
            $this->isRegenerating = true;
            $this->error = null;

            // Dispatch job for background regeneration
            GenerateInterviewQuestionnaireJob::dispatch($this->getProject(), true);

            // Try immediate regeneration for better UX
            $this->questionnaire = $this->contentService->regenerateInterviewQuestionnaire($this->getProject());

            Log::info('Interview questionnaire regenerated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->questionnaire->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to regenerate interview questionnaire', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to regenerate questionnaire. Please try again.';
        } finally {
            $this->isRegenerating = false;
        }
    }

    public function toggleFullView(): void
    {
        $this->showFullQuestionnaire = ! $this->showFullQuestionnaire;
    }

    public function downloadQuestionnaire(): void
    {
        if (! $this->questionnaire) {
            $this->error = 'No questionnaire available to download.';

            return;
        }

        try {
            $questionnaireData = $this->questionnaire->content_data;
            $content = $this->formatQuestionnaireForDownload($questionnaireData);
            $filename = "interview-questionnaire-{$this->getProject()->id}-".now()->format('Y-m-d').'.txt';

            $this->dispatch('download-file', [
                'content' => $content,
                'filename' => $filename,
                'mimeType' => 'text/plain',
            ]);

        } catch (Exception $e) {
            Log::error('Failed to download interview questionnaire', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to download questionnaire.';
        }
    }

    protected function formatQuestionnaireForDownload(array $questionnaireData): string
    {
        $content = "CUSTOMER INTERVIEW QUESTIONNAIRE\n";
        $content .= 'Project: '.substr($this->getProject()->input_prompt, 0, 100)."\n";
        $content .= 'Generated: '.now()->format('F j, Y')."\n";
        $content .= str_repeat('=', 50)."\n\n";

        // Introduction
        if (isset($questionnaireData['introduction'])) {
            $content .= "INTRODUCTION\n";
            $content .= str_repeat('-', 20)."\n";
            $content .= $questionnaireData['introduction']."\n\n";
        }

        // Questions
        if (isset($questionnaireData['questions']) && is_array($questionnaireData['questions'])) {
            $content .= "INTERVIEW QUESTIONS\n";
            $content .= str_repeat('-', 20)."\n";

            $questionNumber = 1;

            foreach ($questionnaireData['questions'] as $section) {
                if (isset($section['section_title'])) {
                    $content .= "\n".strtoupper($section['section_title'])."\n";
                    $content .= str_repeat('-', strlen($section['section_title']))."\n";
                }

                if (isset($section['questions']) && is_array($section['questions'])) {
                    foreach ($section['questions'] as $question) {
                        $content .= "{$questionNumber}. {$question}\n\n";
                        $questionNumber++;
                    }
                } elseif (is_string($section)) {
                    $content .= "{$questionNumber}. {$section}\n\n";
                    $questionNumber++;
                }
            }
        }

        // Conclusion
        if (isset($questionnaireData['conclusion'])) {
            $content .= "\nCONCLUSION\n";
            $content .= str_repeat('-', 20)."\n";
            $content .= $questionnaireData['conclusion']."\n\n";
        }

        $content .= str_repeat('=', 50)."\n";
        $content .= "Generated by Venture Discovery Platform\n";

        return $content;
    }

    public function hasQuestionnaire(): bool
    {
        return $this->questionnaire !== null;
    }

    public function getQuestionCount(): int
    {
        if (! $this->questionnaire) {
            return 0;
        }

        return $this->questionnaire->content_data['question_count'] ?? 0;
    }

    public function getGeneratedAt(): ?string
    {
        if (! $this->questionnaire) {
            return null;
        }

        return $this->questionnaire->created_at->format('M j, Y g:i A');
    }

    /**
     * Poll for updates to check if content generation is complete
     */
    public function pollForUpdates()
    {
        $this->loadQuestionnaire();

        // Check if content has been generated
        if ($this->hasQuestionnaire()) {
            $this->dispatch('stop-polling');
        }
    }

    public function render()
    {
        return view('livewire.interview-questionnaire-display');
    }
}
