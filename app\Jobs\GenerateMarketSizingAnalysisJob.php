<?php

namespace App\Jobs;

use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateMarketSizingAnalysisJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Project $project,
        public ?string $analysisType = null,
        public bool $isRegeneration = false
    ) {
        $this->onQueue('default');
    }

    public function handle(ContentGenerationService $contentService): void
    {
        Log::info('Starting market sizing analysis job', [
            'project_id' => $this->project->id,
            'analysis_type' => $this->analysisType,
            'is_regeneration' => $this->isRegeneration,
            'job_id' => $this->job->getJobId() ?? 'unknown',
        ]);

        try {
            if ($this->analysisType) {
                Log::info('Generating single analysis type', [
                    'project_id' => $this->project->id,
                    'analysis_type' => $this->analysisType,
                    'is_regeneration' => $this->isRegeneration,
                ]);

                // Generate single analysis (TAM, SAM, or SOM)
                if ($this->isRegeneration) {
                    Log::info('Starting regeneration process', [
                        'project_id' => $this->project->id,
                        'analysis_type' => $this->analysisType,
                    ]);

                    $analysis = $contentService->regenerateMarketSizingAnalysis($this->project, $this->analysisType);
                    
                    Log::info('Successfully regenerated market sizing analysis', [
                        'project_id' => $this->project->id,
                        'analysis_type' => $this->analysisType,
                        'analysis_id' => $analysis?->id,
                        'content_length' => $analysis ? strlen(json_encode($analysis->content_data)) : 0,
                    ]);
                } else {
                    Log::info('Starting generation process', [
                        'project_id' => $this->project->id,
                        'analysis_type' => $this->analysisType,
                    ]);

                    $analysis = $contentService->generateMarketSizingAnalysisType($this->project, $this->analysisType);
                    
                    Log::info('Successfully generated market sizing analysis', [
                        'project_id' => $this->project->id,
                        'analysis_type' => $this->analysisType,
                        'analysis_id' => $analysis?->id,
                        'content_length' => $analysis ? strlen(json_encode($analysis->content_data)) : 0,
                    ]);
                }

                // Verify the analysis was created/updated
                $this->verifyAnalysisResult($analysis, $this->analysisType);

            } else {
                Log::info('Generating all market sizing analyses', [
                    'project_id' => $this->project->id,
                ]);

                // Generate all market sizing analyses (TAM, SAM, SOM)
                $analyses = $contentService->generateMarketSizingAnalysis($this->project);
                
                Log::info('Successfully generated all market sizing analyses', [
                    'project_id' => $this->project->id,
                    'generated_count' => count($analyses),
                    'analysis_types' => array_keys($analyses),
                ]);

                // Verify all analyses were created
                foreach ($analyses as $type => $analysis) {
                    $this->verifyAnalysisResult($analysis, $type);
                }
            }

            Log::info('Market sizing analysis job completed successfully', [
                'project_id' => $this->project->id,
                'analysis_type' => $this->analysisType,
                'is_regeneration' => $this->isRegeneration,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate market sizing analysis', [
                'project_id' => $this->project->id,
                'analysis_type' => $this->analysisType,
                'is_regeneration' => $this->isRegeneration,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    private function verifyAnalysisResult($analysis, string $type): void
    {
        if (!$analysis) {
            Log::warning('Analysis result is null', [
                'project_id' => $this->project->id,
                'analysis_type' => $type,
            ]);
            return;
        }

        Log::info('Verifying analysis result', [
            'project_id' => $this->project->id,
            'analysis_type' => $type,
            'analysis_id' => $analysis->id,
            'content_type' => $analysis->content_type,
            'has_content_data' => !empty($analysis->content_data),
            'content_data_keys' => array_keys($analysis->content_data ?? []),
        ]);

        // Check if content has required fields
        $requiredFields = ['market_size_value', 'market_description'];
        $contentData = $analysis->content_data ?? [];
        
        foreach ($requiredFields as $field) {
            if (empty($contentData[$field])) {
                Log::warning('Missing required field in analysis content', [
                    'project_id' => $this->project->id,
                    'analysis_type' => $type,
                    'missing_field' => $field,
                ]);
            }
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Market sizing analysis generation job failed', [
            'project_id' => $this->project->id,
            'analysis_type' => $this->analysisType,
            'is_regeneration' => $this->isRegeneration,
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'stack_trace' => $exception->getTraceAsString(),
            'job_id' => $this->job?->getJobId() ?? 'unknown',
        ]);
    }
} 