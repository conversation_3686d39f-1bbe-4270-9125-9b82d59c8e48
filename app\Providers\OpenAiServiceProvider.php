<?php

namespace App\Providers;

use App\Services\OpenAiService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class OpenAiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(OpenAiService::class, function ($app) {
            return new OpenAiService;
        });

        // Register alias for easier access
        $this->app->alias(OpenAiService::class, 'openai');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->validateConfiguration();
    }

    /**
     * Validate OpenAI configuration
     */
    protected function validateConfiguration(): void
    {
        $config = config('openai');

        // Only validate in production or when explicitly requested
        if (! app()->environment('production') && ! config('openai.validate_on_boot', false)) {
            return;
        }

        if (empty($config['api_key'])) {
            Log::warning('OpenAI API key is not configured. Some features may not work properly.');

            return;
        }

        // Validate required configuration keys
        $requiredKeys = [
            'models.chat',
            'limits.max_tokens',
            'limits.temperature',
            'retry.max_attempts',
        ];

        foreach ($requiredKeys as $key) {
            if (! data_get($config, $key)) {
                Log::warning("OpenAI configuration missing required key: {$key}");
            }
        }

        // Validate model names
        $supportedModels = [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-4',
            'gpt-3.5-turbo',
            'gpt-3.5-turbo-instruct',
        ];

        $configuredModel = $config['models']['chat'] ?? '';
        if (! in_array($configuredModel, $supportedModels)) {
            Log::warning("Configured OpenAI model '{$configuredModel}' may not be supported.");
        }

        // Validate numeric limits
        $limits = $config['limits'] ?? [];

        if (isset($limits['max_tokens']) && ($limits['max_tokens'] < 1 || $limits['max_tokens'] > 4096)) {
            Log::warning('OpenAI max_tokens should be between 1 and 4096.');
        }

        if (isset($limits['temperature']) && ($limits['temperature'] < 0 || $limits['temperature'] > 2)) {
            Log::warning('OpenAI temperature should be between 0 and 2.');
        }

        Log::info('OpenAI configuration validated successfully.');
    }
}
