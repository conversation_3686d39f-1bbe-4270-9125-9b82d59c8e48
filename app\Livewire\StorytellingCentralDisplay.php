<?php

namespace App\Livewire;

use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class StorytellingCentralDisplay extends Component
{
    public ?Project $project = null;

    public ?GeneratedContent $brandWheel = null;

    public ?GeneratedContent $startupNaming = null;

    public ?GeneratedContent $elevatorPitch = null;

    public bool $isLoadingBrandWheel = false;

    public bool $isLoadingStartupNaming = false;

    public bool $isLoadingElevatorPitch = false;

    public bool $isRegeneratingBrandWheel = false;

    public bool $isRegeneratingStartupNaming = false;

    public bool $isRegeneratingElevatorPitch = false;

    public ?string $error = null;

    public bool $showFullElevatorPitch = false;

    protected ContentGenerationService $contentService;

    public function boot(ContentGenerationService $contentService): void
    {
        $this->contentService = $contentService;
    }

    public function mount(?Project $project = null): void
    {
        if ($project) {
            $this->project = $project;
        }
        $this->loadStorytellingContent();
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            // Try to get from Filament context if available
            if (method_exists($this, 'getOwnerRecord')) {
                $this->project = $this->getOwnerRecord();
            } else {
                // Fallback to first project for testing
                /** @var Project|null $project */
                $project = Project::query()->first();
                $this->project = $project;
            }
        }

        return $this->project;
    }

    public function loadStorytellingContent(): void
    {
        try {
            $project = $this->getProject();
            $this->brandWheel = $project->getGeneratedContent('brand_wheel');
            $this->startupNaming = $project->getGeneratedContent('startup_naming');
            $this->elevatorPitch = $project->getGeneratedContent('elevator_pitch');
            $this->error = null;
        } catch (Exception $e) {
            Log::error('Failed to load storytelling content', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to load storytelling content: '.$e->getMessage();
        }
    }

    public function generateBrandWheel(): void
    {
        try {
            $this->isLoadingBrandWheel = true;
            $this->error = null;

            $this->brandWheel = $this->contentService->generateBrandWheel($this->getProject());

            Log::info('Brand wheel generated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->brandWheel->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate brand wheel', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to generate brand wheel. Please try again.';
        } finally {
            $this->isLoadingBrandWheel = false;
        }
    }

    public function generateStartupNaming(): void
    {
        try {
            $this->isLoadingStartupNaming = true;
            $this->error = null;

            $this->startupNaming = $this->contentService->generateStartupNaming($this->getProject());

            Log::info('Startup naming generated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->startupNaming->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate startup naming', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to generate startup naming. Please try again.';
        } finally {
            $this->isLoadingStartupNaming = false;
        }
    }

    public function generateElevatorPitch(): void
    {
        try {
            $this->isLoadingElevatorPitch = true;
            $this->error = null;

            $this->elevatorPitch = $this->contentService->generateElevatorPitch($this->getProject());

            Log::info('Elevator pitch generated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->elevatorPitch->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate elevator pitch', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to generate elevator pitch. Please try again.';
        } finally {
            $this->isLoadingElevatorPitch = false;
        }
    }

    public function regenerateBrandWheel(): void
    {
        try {
            $this->isRegeneratingBrandWheel = true;
            $this->error = null;

            $this->brandWheel = $this->contentService->regenerateStorytellingContent($this->getProject(), 'brand_wheel');

            Log::info('Brand wheel regenerated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->brandWheel->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to regenerate brand wheel', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to regenerate brand wheel. Please try again.';
        } finally {
            $this->isRegeneratingBrandWheel = false;
        }
    }

    public function regenerateStartupNaming(): void
    {
        try {
            $this->isRegeneratingStartupNaming = true;
            $this->error = null;

            $this->startupNaming = $this->contentService->regenerateStorytellingContent($this->getProject(), 'startup_naming');

            Log::info('Startup naming regenerated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->startupNaming->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to regenerate startup naming', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to regenerate startup naming. Please try again.';
        } finally {
            $this->isRegeneratingStartupNaming = false;
        }
    }

    public function regenerateElevatorPitch(): void
    {
        try {
            $this->isRegeneratingElevatorPitch = true;
            $this->error = null;

            $this->elevatorPitch = $this->contentService->regenerateStorytellingContent($this->getProject(), 'elevator_pitch');

            Log::info('Elevator pitch regenerated successfully', [
                'project_id' => $this->getProject()->id,
                'content_id' => $this->elevatorPitch->id,
            ]);

        } catch (Exception $e) {
            Log::error('Failed to regenerate elevator pitch', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to regenerate elevator pitch. Please try again.';
        } finally {
            $this->isRegeneratingElevatorPitch = false;
        }
    }

    public function generateAllContent(): void
    {
        try {
            $this->isLoadingBrandWheel = true;
            $this->isLoadingStartupNaming = true;
            $this->isLoadingElevatorPitch = true;
            $this->error = null;

            $content = $this->contentService->generateAllStorytellingContent($this->getProject());

            $this->brandWheel = $content['brand_wheel'];
            $this->startupNaming = $content['startup_naming'];
            $this->elevatorPitch = $content['elevator_pitch'];

            Log::info('All storytelling content generated successfully', [
                'project_id' => $this->getProject()->id,
                'content_types' => array_keys($content),
            ]);

        } catch (Exception $e) {
            Log::error('Failed to generate all storytelling content', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to generate storytelling content. Please try again.';
        } finally {
            $this->isLoadingBrandWheel = false;
            $this->isLoadingStartupNaming = false;
            $this->isLoadingElevatorPitch = false;
        }
    }

    public function toggleFullElevatorPitch(): void
    {
        $this->showFullElevatorPitch = ! $this->showFullElevatorPitch;
    }

    public function downloadStorytellingContent(): void
    {
        if (! $this->brandWheel && ! $this->startupNaming && ! $this->elevatorPitch) {
            $this->error = 'No storytelling content available to download.';

            return;
        }

        try {
            $content = $this->formatStorytellingContentForDownload();
            $filename = "storytelling-central-{$this->getProject()->id}-".now()->format('Y-m-d').'.txt';

            $this->dispatch('download-file', [
                'content' => $content,
                'filename' => $filename,
                'mimeType' => 'text/plain',
            ]);

        } catch (Exception $e) {
            Log::error('Failed to download storytelling content', [
                'project_id' => $this->getProject()->id,
                'error' => $e->getMessage(),
            ]);
            $this->error = 'Failed to download storytelling content.';
        }
    }

    protected function formatStorytellingContentForDownload(): string
    {
        $content = "STORYTELLING CENTRAL\n";
        $content .= 'Project: '.substr($this->getProject()->input_prompt, 0, 100)."\n";
        $content .= 'Generated: '.now()->format('F j, Y')."\n";
        $content .= str_repeat('=', 50)."\n\n";

        // Brand Wheel
        if ($this->brandWheel) {
            $brandData = $this->brandWheel->content_data;
            $content .= "BRAND WHEEL\n";
            $content .= str_repeat('-', 20)."\n";

            if (isset($brandData['mission'])) {
                $content .= "Mission: {$brandData['mission']}\n\n";
            }
            if (isset($brandData['vision'])) {
                $content .= "Vision: {$brandData['vision']}\n\n";
            }
            if (isset($brandData['values']) && is_array($brandData['values'])) {
                $content .= "Values:\n";
                foreach ($brandData['values'] as $value) {
                    $content .= "- {$value}\n";
                }
                $content .= "\n";
            }
            if (isset($brandData['personality'])) {
                $content .= "Brand Personality: {$brandData['personality']}\n\n";
            }
            if (isset($brandData['tone_of_voice'])) {
                $content .= "Tone of Voice: {$brandData['tone_of_voice']}\n\n";
            }
            if (isset($brandData['brand_promise'])) {
                $content .= "Brand Promise: {$brandData['brand_promise']}\n\n";
            }
        }

        // Startup Naming
        if ($this->startupNaming) {
            $namingData = $this->startupNaming->content_data;
            $content .= "STARTUP NAMING SUGGESTIONS\n";
            $content .= str_repeat('-', 30)."\n";

            if (isset($namingData['suggestions']) && is_array($namingData['suggestions'])) {
                foreach ($namingData['suggestions'] as $index => $name) {
                    $content .= ($index + 1).". {$name}\n";
                    if (isset($namingData['rationales'][$index])) {
                        $content .= "   Rationale: {$namingData['rationales'][$index]}\n";
                    }
                    if (isset($namingData['domain_availability'][$index])) {
                        $content .= "   Domain: {$namingData['domain_availability'][$index]}\n";
                    }
                    if (isset($namingData['trademark_considerations'][$index])) {
                        $content .= "   Trademark Risk: {$namingData['trademark_considerations'][$index]}\n";
                    }
                    $content .= "\n";
                }
            }
        }

        // Elevator Pitch
        if ($this->elevatorPitch) {
            $pitchData = $this->elevatorPitch->content_data;
            $content .= "ELEVATOR PITCHES\n";
            $content .= str_repeat('-', 20)."\n";

            if (isset($pitchData['pitch_30_seconds'])) {
                $content .= "30-Second Pitch:\n{$pitchData['pitch_30_seconds']}\n\n";
            }
            if (isset($pitchData['pitch_60_seconds'])) {
                $content .= "60-Second Pitch:\n{$pitchData['pitch_60_seconds']}\n\n";
            }
            if (isset($pitchData['pitch_90_seconds'])) {
                $content .= "90-Second Pitch:\n{$pitchData['pitch_90_seconds']}\n\n";
            }
            if (isset($pitchData['key_points']) && is_array($pitchData['key_points'])) {
                $content .= "Key Points:\n";
                foreach ($pitchData['key_points'] as $point) {
                    $content .= "- {$point}\n";
                }
                $content .= "\n";
            }
            if (isset($pitchData['call_to_action'])) {
                $content .= "Call to Action: {$pitchData['call_to_action']}\n\n";
            }
        }

        $content .= str_repeat('=', 50)."\n";
        $content .= "Generated by Venture Discovery Platform\n";

        return $content;
    }

    // Helper methods for checking content existence
    public function hasBrandWheel(): bool
    {
        return $this->brandWheel !== null;
    }

    public function hasStartupNaming(): bool
    {
        return $this->startupNaming !== null;
    }

    public function hasElevatorPitch(): bool
    {
        return $this->elevatorPitch !== null;
    }

    public function hasAnyContent(): bool
    {
        return $this->hasBrandWheel() || $this->hasStartupNaming() || $this->hasElevatorPitch();
    }

    public function getGeneratedAt(string $contentType): ?string
    {
        $content = match ($contentType) {
            'brand_wheel' => $this->brandWheel,
            'startup_naming' => $this->startupNaming,
            'elevator_pitch' => $this->elevatorPitch,
            default => null,
        };

        if (! $content) {
            return null;
        }

        return $content->created_at->format('M j, Y g:i A');
    }

    /**
     * Poll for updates to check if content generation is complete
     */
    public function pollForUpdates()
    {
        $this->loadStorytellingContent();

        // Check if all content has been generated
        if ($this->hasAllContent()) {
            $this->dispatch('stop-polling');
        }
    }

    /**
     * Check if all storytelling content has been generated
     */
    public function hasAllContent(): bool
    {
        return $this->hasBrandWheel() && $this->hasStartupNaming() && $this->hasElevatorPitch();
    }

    public function render()
    {
        return view('livewire.storytelling-central-display');
    }
}
