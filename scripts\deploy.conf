# Venture Discovery Platform - Deployment Configuration
# This file contains customizable settings for the deployment scripts
# Copy this file and modify the values as needed for your environment

# Project Configuration
PROJECT_NAME="venture-discovery"
PROJECT_DOMAIN="venture-discovery.local"
PROJECT_EMAIL="<EMAIL>"

# Software Versions
PHP_VERSION="8.2"
NODE_VERSION="20"
MYSQL_VERSION="8.0"

# Database Configuration
DB_NAME="venture_discovery"
DB_USER="venture_user"
DB_PASSWORD=""  # Will be generated if empty

# Repository Configuration
REPO_URL="https://github.com/yourusername/venture-discovery.git"
REPO_BRANCH="main"

# SSL Configuration
ENABLE_SSL="false"  # Set to "true" for production
SSL_EMAIL=""  # Required for Let's Encrypt

# Development Options
INSTALL_DEV_TOOLS="true"  # Install development tools (mkcert, etc.)
SEED_DATABASE="true"      # Run database seeders
BUILD_ASSETS="true"       # Build frontend assets

# Security Options (Ubuntu only)
ENABLE_FIREWALL="true"
ENABLE_FAIL2BAN="true"
ENABLE_BACKUPS="true"

# Service Configuration
ENABLE_QUEUE_WORKERS="true"
QUEUE_WORKERS_COUNT="3"

# Paths (will be set automatically based on OS if empty)
PROJECT_PATH=""
NGINX_CONFIG_PATH=""
PHP_CONFIG_PATH=""

# Custom Environment Variables
# Add any additional environment variables here
# Format: CUSTOM_ENV_VAR="value"
CUSTOM_APP_ENV="production"
CUSTOM_APP_DEBUG="false"
CUSTOM_LOG_LEVEL="error" 