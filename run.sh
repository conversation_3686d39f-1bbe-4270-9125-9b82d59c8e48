#!/bin/bash
# deployment.sh - Simple deployment script for rydo

# Pull latest changes
git pull

# Install dependencies
composer install --optimize-autoloader

# Run migrations (without prompting)
php artisan migrate --force
php artisan db:seed

# Clear caches
php artisan optimize:clear
php artisan optimize

npm i
npm run build

# Restart queue worker if using queues
# supervisorctl restart laravel-worker:*

composer full

composer dev

echo "Deployment completed successfully!"


