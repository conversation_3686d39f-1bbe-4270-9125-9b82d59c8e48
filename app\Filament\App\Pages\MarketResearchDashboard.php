<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;

class MarketResearchDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    
    protected static string $view = 'filament.app.pages.market-research-dashboard';
    
    protected static ?string $title = 'Market Research Dashboard';
    
    protected static ?string $navigationLabel = 'Market Research';
    
    protected static ?string $navigationGroup = 'Research Tools';
    
    protected static ?int $navigationSort = 1;
    
    public function getTitle(): string
    {
        return 'Market Research Dashboard';
    }
    
    public function getHeading(): string
    {
        return 'AI-Powered Market Research';
    }
    
    public function getSubheading(): ?string
    {
        return 'Generate comprehensive market insights using AI analysis';
    }
} 