<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\OpenAiService;
use App\Models\MarketResearchSession;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class MarketResearchDashboard extends Component
{
    // Form inputs
    public $industry = '';
    public $region = '';
    
    // UI state
    public $isLoading = false;
    public $errorMessage = '';
    public $successMessage = '';
    
    // Dashboard data properties
    public $marketAttractiveness = null;
    public $marketSize = null;
    public $opportunityZones = [];
    public $researchScope = [];
    public $strategicImplications = [];
    public $customerPainPoints = [];
    public $competitiveLandscape = [];
    public $enablers = [];
    public $barriers = [];
    public $swotAnalysis = null;
    public $hasData = false;
    
    // Modal properties
    public $showModal = false;
    public $modalTitle = '';
    public $modalContent = '';
    public $isModalLoading = false;
    public $modalType = ''; // 'opportunity', 'painpoint', 'swot'
    
    // Session management
    public $currentSessionId = null;
    public $previousSessions = [];
    public $showResetConfirmation = false;
    
    protected $rules = [
        'industry' => 'required|string|max:100|min:2',
        'region' => 'required|string|max:100|min:2',
    ];
    
    protected $messages = [
        'industry.required' => 'Please enter a target industry.',
        'industry.min' => 'Industry must be at least 2 characters.',
        'region.required' => 'Please enter a target region.',
        'region.min' => 'Region must be at least 2 characters.',
    ];

    public function mount()
    {
        // Initialize component
        $this->resetDashboardData();
        $this->loadPreviousSessions();
    }

    /**
     * Main method to research market and update dashboard
     */
    public function researchMarket()
    {
        // Clear previous messages
        $this->errorMessage = '';
        $this->successMessage = '';
        
        // Validate inputs
        $this->validate();
        
        // Set loading state
        $this->isLoading = true;
        
        try {
            Log::info('Starting market research', [
                'industry' => $this->industry,
                'region' => $this->region,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
            
            // Call OpenAI service
            $openAiService = app(OpenAiService::class);
            $data = $this->getDashboardCoreUpdate($openAiService, $this->industry, $this->region);
            
            if (!$data) {
                throw new \Exception('Failed to generate market research data. Please try again.');
            }
            
            // Update dashboard properties with API response
            $this->updateDashboardData($data);
            
            // Save research session to database if user is authenticated
            if (Auth::check()) {
                $this->saveResearchSession($data);
                $this->loadPreviousSessions(); // Refresh the sessions list
            }
            
            $this->hasData = true;
            $this->successMessage = 'Market research completed successfully!';
            
            Log::info('Market research completed successfully', [
                'industry' => $this->industry,
                'region' => $this->region,
                'session_id' => $this->currentSessionId
            ]);
            
        } catch (\Exception $e) {
            $this->errorMessage = 'Failed to generate market research: ' . $e->getMessage();
            
            Log::error('Market research failed', [
                'error' => $e->getMessage(),
                'industry' => $this->industry,
                'region' => $this->region,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        } finally {
            $this->isLoading = false;
        }
    }
    
    /**
     * Show reset confirmation dialog
     */
    public function confirmReset()
    {
        if ($this->hasData) {
            $this->showResetConfirmation = true;
        } else {
            $this->resetDashboard();
        }
    }
    
    /**
     * Cancel reset operation
     */
    public function cancelReset()
    {
        $this->showResetConfirmation = false;
    }
    
    /**
     * Reset dashboard to initial state
     */
    public function resetDashboard()
    {
        $this->industry = '';
        $this->region = '';
        $this->errorMessage = '';
        $this->successMessage = '';
        $this->currentSessionId = null;
        $this->hasData = false;
        $this->showResetConfirmation = false;
        
        $this->resetDashboardData();
        
        Log::info('Dashboard reset by user', ['user_id' => Auth::check() ? Auth::id() : 'guest']);
    }
    
    /**
     * Load a previous research session
     */
    public function loadSession($sessionId)
    {
        if (!Auth::check()) {
            $this->errorMessage = 'Please log in to load previous sessions.';
            return;
        }
        
        try {
            $session = MarketResearchSession::where('id', $sessionId)
                ->where('user_id', Auth::id())
                ->first();
                
            if (!$session) {
                $this->errorMessage = 'Session not found or access denied.';
                return;
            }
            
            // Load session data
            $this->industry = $session->industry;
            $this->region = $session->region;
            $this->currentSessionId = $session->id;
            
            // Load dashboard data from session
            $this->marketAttractiveness = $session->market_attractiveness;
            $this->marketSize = $session->market_size;
            $this->opportunityZones = $session->opportunity_zones ?? [];
            $this->researchScope = $session->research_scope ?? [];
            
            // Process strategic implications from database - convert to simple array for display
            $strategicImplicationsData = $session->strategic_implications ?? [];
            $this->strategicImplications = [];
            
            foreach ($strategicImplicationsData as $implication) {
                if (is_array($implication) && isset($implication['title'], $implication['description'])) {
                    $this->strategicImplications[] = $implication['title'] . ': ' . $implication['description'];
                } else {
                    $this->strategicImplications[] = (string) $implication;
                }
            }
            
            $this->customerPainPoints = $session->customer_pain_points ?? [];
            
            // Process competitive landscape from database - ensure consistent structure
            $competitiveLandscapeData = $session->competitive_landscape ?? [];
            $this->competitiveLandscape = [];
            
            foreach ($competitiveLandscapeData as $competitor) {
                if (is_array($competitor)) {
                    // Handle enhanced structure with new fields
                    $name = $competitor['name'] ?? $competitor['company'] ?? $competitor['competitor'] ?? 'Unknown Company';
                    $description = $competitor['description'] ?? 'No description available';
                    $marketPosition = $competitor['marketPosition'] ?? 'Market participant';
                    $marketShare = $competitor['marketShare'] ?? 'N/A';
                    $companyDomain = $competitor['companyDomain'] ?? '';
                    
                    // Handle arrays for differentiators and strengths
                    $differentiators = [];
                    if (isset($competitor['keyDifferentiators']) && is_array($competitor['keyDifferentiators'])) {
                        $differentiators = array_map('strval', $competitor['keyDifferentiators']);
                    }
                    
                    $strengths = [];
                    if (isset($competitor['strengths']) && is_array($competitor['strengths'])) {
                        $strengths = array_map('strval', $competitor['strengths']);
                    }
                    
                    $this->competitiveLandscape[] = [
                        'name' => (string) $name,
                        'description' => (string) $description,
                        'marketPosition' => (string) $marketPosition,
                        'marketShare' => (string) $marketShare,
                        'companyDomain' => (string) $companyDomain,
                        'keyDifferentiators' => $differentiators,
                        'strengths' => $strengths
                    ];
                } else {
                    // If it's a string, create a basic structure with fallbacks
                    $this->competitiveLandscape[] = [
                        'name' => (string) $competitor,
                        'description' => 'Competitor in the market',
                        'marketPosition' => 'Market participant',
                        'marketShare' => 'N/A',
                        'companyDomain' => '',
                        'keyDifferentiators' => [],
                        'strengths' => []
                    ];
                }
            }
            
            // Handle enablers and barriers
            $enablersBarriers = $session->enablers_barriers ?? [];
            $this->enablers = $enablersBarriers['enablers'] ?? [];
            $this->barriers = $enablersBarriers['barriers'] ?? [];
            
            $this->swotAnalysis = $session->swot_analysis;
            $this->hasData = true;
            
            $this->successMessage = 'Previous session loaded successfully!';
            
            Log::info('Session loaded', [
                'session_id' => $sessionId,
                'user_id' => Auth::id()
            ]);
            
        } catch (\Exception $e) {
            $this->errorMessage = 'Failed to load session: ' . $e->getMessage();
            Log::error('Failed to load session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        }
    }
    
    /**
     * Delete a research session
     */
    public function deleteSession($sessionId)
    {
        if (!Auth::check()) {
            $this->errorMessage = 'Please log in to manage sessions.';
            return;
        }
        
        try {
            $session = MarketResearchSession::where('id', $sessionId)
                ->where('user_id', Auth::id())
                ->first();
                
            if (!$session) {
                $this->errorMessage = 'Session not found or access denied.';
                return;
            }
            
            $session->delete();
            $this->loadPreviousSessions(); // Refresh the sessions list
            
            // If the deleted session was the current one, reset the dashboard
            if ($this->currentSessionId == $sessionId) {
                $this->resetDashboard();
            }
            
            $this->successMessage = 'Session deleted successfully!';
            
            Log::info('Session deleted', [
                'session_id' => $sessionId,
                'user_id' => Auth::id()
            ]);
            
        } catch (\Exception $e) {
            $this->errorMessage = 'Failed to delete session: ' . $e->getMessage();
            Log::error('Failed to delete session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        }
    }
    
    /**
     * Load previous research sessions for the authenticated user
     */
    protected function loadPreviousSessions()
    {
        if (Auth::check()) {
            $this->previousSessions = MarketResearchSession::where('user_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($session) {
                    return [
                        'id' => $session->id,
                        'industry' => $session->industry,
                        'region' => $session->region,
                        'created_at' => $session->created_at->format('M j, Y g:i A'),
                        'completion_percentage' => $session->getCompletionPercentage(),
                    ];
                })
                ->toArray();
        }
    }
    
    /**
     * Update dashboard data properties from API response
     */
    protected function updateDashboardData(array $data)
    {
        // Log the raw data structure for debugging
        Log::info('Raw API response structure', [
            'data_keys' => array_keys($data),
            'competitive_landscape_sample' => isset($data['competitiveLandscape']) 
                ? (count($data['competitiveLandscape']) > 0 ? $data['competitiveLandscape'][0] : 'empty') 
                : 'not_set'
        ]);

        // Market Attractiveness
        $this->marketAttractiveness = $data['marketAttractiveness'] ?? null;
        
        // Market Size
        $this->marketSize = $data['marketSize'] ?? null;
        
        // Opportunity Zones - ensure proper structure
        $this->opportunityZones = [];
        if (isset($data['opportunityZones']) && is_array($data['opportunityZones'])) {
            foreach ($data['opportunityZones'] as $zone) {
                if (is_array($zone)) {
                    $this->opportunityZones[] = [
                        'title' => (string) ($zone['title'] ?? 'Untitled Opportunity'),
                        'description' => (string) ($zone['description'] ?? 'No description available')
                    ];
                } else {
                    $this->opportunityZones[] = [
                        'title' => (string) $zone,
                        'description' => 'Opportunity zone'
                    ];
                }
            }
        }
        
        // Research Scope
        $this->researchScope = $data['researchScope'] ?? [];
        
        // Strategic Implications - convert to simple array for display
        $this->strategicImplications = [];
        if (isset($data['strategicImplications']) && is_array($data['strategicImplications'])) {
            foreach ($data['strategicImplications'] as $implication) {
                if (is_array($implication) && isset($implication['title'], $implication['description'])) {
                    $this->strategicImplications[] = $implication['title'] . ': ' . $implication['description'];
                } else {
                    $this->strategicImplications[] = (string) $implication;
                }
            }
        }
        
        // Customer Pain Points - ensure proper structure
        $this->customerPainPoints = [];
        if (isset($data['customerPainPoints']) && is_array($data['customerPainPoints'])) {
            foreach ($data['customerPainPoints'] as $painPoint) {
                if (is_array($painPoint)) {
                    $this->customerPainPoints[] = [
                        'title' => (string) ($painPoint['title'] ?? 'Untitled Pain Point'),
                        'description' => (string) ($painPoint['description'] ?? 'No description available')
                    ];
                } else {
                    $this->customerPainPoints[] = [
                        'title' => (string) $painPoint,
                        'description' => 'Customer pain point'
                    ];
                }
            }
        }
        
        // Competitive Landscape - ensure consistent structure with fallbacks
        $this->competitiveLandscape = [];
        if (isset($data['competitiveLandscape']) && is_array($data['competitiveLandscape'])) {
            foreach ($data['competitiveLandscape'] as $competitor) {
                if (is_array($competitor)) {
                    // Handle enhanced structure with new fields
                    $name = $competitor['name'] ?? $competitor['company'] ?? $competitor['competitor'] ?? 'Unknown Company';
                    $description = $competitor['description'] ?? 'No description available';
                    $marketPosition = $competitor['marketPosition'] ?? 'Market participant';
                    $marketShare = $competitor['marketShare'] ?? 'N/A';
                    $companyDomain = $competitor['companyDomain'] ?? '';
                    
                    // Handle arrays for differentiators and strengths
                    $differentiators = [];
                    if (isset($competitor['keyDifferentiators']) && is_array($competitor['keyDifferentiators'])) {
                        $differentiators = array_map('strval', $competitor['keyDifferentiators']);
                    }
                    
                    $strengths = [];
                    if (isset($competitor['strengths']) && is_array($competitor['strengths'])) {
                        $strengths = array_map('strval', $competitor['strengths']);
                    }
                    
                    $this->competitiveLandscape[] = [
                        'name' => (string) $name,
                        'description' => (string) $description,
                        'marketPosition' => (string) $marketPosition,
                        'marketShare' => (string) $marketShare,
                        'companyDomain' => (string) $companyDomain,
                        'keyDifferentiators' => $differentiators,
                        'strengths' => $strengths
                    ];
                } else {
                    // If it's a string, create a basic structure with fallbacks
                    $this->competitiveLandscape[] = [
                        'name' => (string) $competitor,
                        'description' => 'Competitor in the market',
                        'marketPosition' => 'Market participant',
                        'marketShare' => 'N/A',
                        'companyDomain' => '',
                        'keyDifferentiators' => [],
                        'strengths' => []
                    ];
                }
            }
        }
        
        // Handle enablers and barriers
        $enablersBarriers = $data['keyEnablersAndBarriers'] ?? [];
        $this->enablers = [];
        $this->barriers = [];
        
        if (is_array($enablersBarriers)) {
            if (isset($enablersBarriers['enablers']) && is_array($enablersBarriers['enablers'])) {
                foreach ($enablersBarriers['enablers'] as $enabler) {
                    $this->enablers[] = (string) $enabler;
                }
            }
            
            if (isset($enablersBarriers['barriers']) && is_array($enablersBarriers['barriers'])) {
                foreach ($enablersBarriers['barriers'] as $barrier) {
                    $this->barriers[] = (string) $barrier;
                }
            }
        }
        
        // SWOT Analysis - ensure all elements are strings
        $swotData = $data['marketSWOT'] ?? null;
        $this->swotAnalysis = null;
        
        if (is_array($swotData)) {
            $this->swotAnalysis = [];
            
            foreach (['strengths', 'weaknesses', 'opportunities', 'threats'] as $category) {
                if (isset($swotData[$category]) && is_array($swotData[$category])) {
                    $this->swotAnalysis[$category] = [];
                    foreach ($swotData[$category] as $item) {
                        $this->swotAnalysis[$category][] = (string) $item;
                    }
                }
            }
        }
    }
    
    /**
     * Reset all dashboard data to initial state
     */
    protected function resetDashboardData()
    {
        $this->marketAttractiveness = null;
        $this->marketSize = null;
        $this->opportunityZones = [];
        $this->researchScope = [];
        $this->strategicImplications = [];
        $this->customerPainPoints = [];
        $this->competitiveLandscape = [];
        $this->enablers = [];
        $this->barriers = [];
        $this->swotAnalysis = null;
    }
    
    /**
     * Save research session to database
     */
    protected function saveResearchSession(array $data)
    {
        if (!Auth::check()) {
            Log::info('Skipping session save - user not authenticated');
            return;
        }
        
        try {
            $session = MarketResearchSession::create([
                'user_id' => Auth::id(),
                'industry' => $this->industry,
                'region' => $this->region,
                'market_attractiveness' => $data['marketAttractiveness'] ?? null,
                'market_size' => $data['marketSize'] ?? null,
                'opportunity_zones' => $data['opportunityZones'] ?? null,
                'research_scope' => $data['researchScope'] ?? null,
                'strategic_implications' => $data['strategicImplications'] ?? null,
                'customer_pain_points' => $data['customerPainPoints'] ?? null,
                'competitive_landscape' => $data['competitiveLandscape'] ?? null,
                'enablers_barriers' => $data['keyEnablersAndBarriers'] ?? null,
                'swot_analysis' => $data['marketSWOT'] ?? null,
                'generated_at' => now(),
            ]);
            
            $this->currentSessionId = $session->id;
            
            Log::info('Research session saved', [
                'session_id' => $session->id,
                'user_id' => Auth::id()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to save research session', [
                'error' => $e->getMessage(),
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
            // Don't throw exception here as the main functionality worked
        }
    }
    
    /**
     * Get market attractiveness score for display
     */
    public function getMarketAttractivenessScoreProperty()
    {
        return $this->marketAttractiveness['score'] ?? 0;
    }
    
    /**
     * Get market attractiveness statement for display
     */
    public function getMarketAttractivenessStatementProperty()
    {
        return $this->marketAttractiveness['statement'] ?? 'No assessment available';
    }
    
    /**
     * Get opportunity zones count for display
     */
    public function getOpportunityZonesCountProperty()
    {
        return count($this->opportunityZones);
    }
    
    /**
     * Check if competitive landscape has data
     */
    public function getHasCompetitiveLandscapeProperty()
    {
        return !empty($this->competitiveLandscape);
    }
    
    /**
     * Check if SWOT analysis has data
     */
    public function getHasSwotAnalysisProperty()
    {
        return !empty($this->swotAnalysis);
    }
    
    /**
     * Format market size for display
     */
    public function getFormattedMarketSizeProperty()
    {
        if (!$this->marketSize) {
            return [
                'tam' => 'N/A',
                'sam' => 'N/A',
                'som' => 'N/A',
                'cagr' => 'N/A'
            ];
        }
        
        return [
            'tam' => $this->marketSize['tam'] ?? 'N/A',
            'sam' => $this->marketSize['sam'] ?? 'N/A',
            'som' => $this->marketSize['som'] ?? 'N/A',
            'cagr' => $this->marketSize['cagr'] ?? 'N/A'
        ];
    }
    
    /**
     * Elaborate on an opportunity zone using OpenAI API
     */
    public function elaborateOpportunityZone($zoneIndex)
    {
        // Validate index
        if (!isset($this->opportunityZones[$zoneIndex])) {
            $this->modalContent = ['error' => 'Opportunity zone not found.'];
            $this->showModal = true;
            return;
        }
        
        $zone = $this->opportunityZones[$zoneIndex];
        $zoneTitle = $zone['title'] ?? 'Opportunity Zone';
        $zoneDescription = $zone['description'] ?? 'No description available';
        
        $this->modalTitle = "💡 Elaborating on: {$zoneTitle}";
        $this->modalType = 'opportunity';
        $this->modalContent = '';
        $this->isModalLoading = true;
        $this->showModal = true;
        
        try {
            Log::info('Elaborating opportunity zone', [
                'title' => $zoneTitle,
                'index' => $zoneIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
            
            $openAiService = app(OpenAiService::class);
            
            $prompt = "As a market research analyst, provide detailed elaboration on this specific opportunity zone for {$this->industry} in {$this->region}:

**Opportunity Zone:** {$zoneTitle}
**Description:** {$zoneDescription}

Please provide detailed analysis focusing on:
1. Market dynamics and trends driving this opportunity
2. Key opportunities within this zone
3. Implementation considerations and potential challenges

Be specific and actionable in your recommendations.";

            $schema = [
                'marketDynamics' => 'Analysis of market dynamics and trends',
                'keyOpportunities' => 'Array of key opportunities',
                'implementationConsiderations' => 'Array of implementation considerations and challenges'
            ];
            
            $result = $openAiService->generateStructuredContent($prompt, $schema);
            
            if ($result) {
                $this->modalContent = $result;
            } else {
                $this->modalContent = ['error' => 'Failed to generate elaboration. Please try again.'];
            }
            
        } catch (\Exception $e) {
            $this->modalContent = ['error' => 'Failed to generate elaboration: ' . $e->getMessage()];
            Log::error('Opportunity zone elaboration failed', [
                'error' => $e->getMessage(),
                'title' => $zoneTitle,
                'index' => $zoneIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        } finally {
            $this->isModalLoading = false;
        }
    }
    
    /**
     * Brainstorm solutions for a pain point using OpenAI API
     */
    public function brainstormPainPointSolutions($painPointIndex)
    {
        // Validate index
        if (!isset($this->customerPainPoints[$painPointIndex])) {
            $this->modalContent = ['error' => 'Pain point not found.'];
            $this->showModal = true;
            return;
        }
        
        $painPoint = $this->customerPainPoints[$painPointIndex];
        $painTitle = $painPoint['title'] ?? 'Pain Point';
        $painDescription = $painPoint['description'] ?? 'No description available';
        
        $this->modalTitle = "💡 Brainstorming Solutions for: {$painTitle}";
        $this->modalType = 'painpoint';
        $this->modalContent = '';
        $this->isModalLoading = true;
        $this->showModal = true;
        
        try {
            Log::info('Brainstorming pain point solutions', [
                'title' => $painTitle,
                'index' => $painPointIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
            
            $openAiService = app(OpenAiService::class);
            
            $prompt = "As a market research analyst and innovation consultant, brainstorm solution categories for this customer pain point in {$this->industry} in {$this->region}:

**Pain Point:** {$painTitle}
**Description:** {$painDescription}

Provide 2-3 solution categories with specific solutions for each category, and implementation priorities.

Focus on practical, implementable solutions that could realistically address this market need.";

            $schema = [
                'solutionCategories' => 'Array of objects with category and solutions array',
                'implementationPriority' => 'Array of priority order for implementation'
            ];
            
            $result = $openAiService->generateStructuredContent($prompt, $schema);
            
            if ($result) {
                $this->modalContent = $result;
            } else {
                $this->modalContent = ['error' => 'Failed to generate solutions. Please try again.'];
            }
            
        } catch (\Exception $e) {
            $this->modalContent = ['error' => 'Failed to generate solutions: ' . $e->getMessage()];
            Log::error('Pain point solutions failed', [
                'error' => $e->getMessage(),
                'title' => $painTitle,
                'index' => $painPointIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        } finally {
            $this->isModalLoading = false;
        }
    }
    
    /**
     * Suggest actions for a SWOT item using OpenAI API
     */
    public function suggestSWOTActions($swotCategory, $itemIndex)
    {
        // Validate SWOT data and index
        if (!isset($this->swotAnalysis[$swotCategory]) || !isset($this->swotAnalysis[$swotCategory][$itemIndex])) {
            $this->modalContent = ['error' => 'SWOT item not found.'];
            $this->showModal = true;
            return;
        }
        
        $swotItem = $this->swotAnalysis[$swotCategory][$itemIndex];
        $categoryLabel = ucfirst(rtrim($swotCategory, 's')); // Convert 'strengths' to 'Strength'
        
        $this->modalTitle = "💡 Action Suggestions for {$categoryLabel}: {$swotItem}";
        $this->modalType = 'swot';
        $this->modalContent = '';
        $this->isModalLoading = true;
        $this->showModal = true;
        
        try {
            Log::info('Suggesting SWOT actions', [
                'category' => $swotCategory,
                'item' => $swotItem,
                'index' => $itemIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
            
            $openAiService = app(OpenAiService::class);
            
            $prompt = "As a strategic business consultant, provide actionable strategies for this {$categoryLabel} in the {$this->industry} industry in {$this->region}:

**{$categoryLabel}:** {$swotItem}

Provide:
1. Action strategies to leverage/address this item
2. Implementation steps
3. Success metrics to track progress

Focus on practical, actionable recommendations.";

            $schema = [
                'actionStrategies' => 'Array of actionable strategies',
                'implementationSteps' => 'Array of step-by-step implementation guide',
                'successMetrics' => 'Array of metrics to measure success'
            ];
            
            $result = $openAiService->generateStructuredContent($prompt, $schema);
            
            if ($result) {
                $this->modalContent = $result;
            } else {
                $this->modalContent = ['error' => 'Failed to generate action suggestions. Please try again.'];
            }
            
        } catch (\Exception $e) {
            $this->modalContent = ['error' => 'Failed to generate action suggestions: ' . $e->getMessage()];
            Log::error('SWOT action suggestions failed', [
                'error' => $e->getMessage(),
                'category' => $swotCategory,
                'item' => $swotItem,
                'index' => $itemIndex,
                'user_id' => Auth::check() ? Auth::id() : 'guest'
            ]);
        } finally {
            $this->isModalLoading = false;
        }
    }
    
    /**
     * Close the modal
     */
    public function closeModal()
    {
        $this->showModal = false;
        $this->modalTitle = '';
        $this->modalContent = '';
        $this->modalType = '';
        $this->isModalLoading = false;
    }

    /**
     * Get comprehensive market research data using OpenAI service
     */
    protected function getDashboardCoreUpdate(OpenAiService $openAiService, string $industry, string $region): ?array
    {
        $cacheKey = "market_research_{$industry}_{$region}";
        
        // Check cache first (cache for 1 hour)
        if (cache()->has($cacheKey)) {
            Log::info('Returning cached market research data', ['industry' => $industry, 'region' => $region]);
            return cache()->get($cacheKey);
        }

        $prompt = $this->buildDashboardPrompt($industry, $region);
        $schema = $this->getDashboardSchema();
        
        try {
            $result = $openAiService->generateStructuredContent($prompt, $schema);

            if ($result) {
                // Cache the result for 1 hour
                cache()->put($cacheKey, $result, 3600);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('OpenAI API call failed for market research', [
                'error' => $e->getMessage(),
                'industry' => $industry,
                'region' => $region
            ]);
            return null;
        }
    }

    /**
     * Build the comprehensive prompt for dashboard data
     */
    protected function buildDashboardPrompt(string $industry, string $region): string
    {
        return "Act as a senior market research analyst. Provide a comprehensive market research overview for the {$industry} industry in {$region}. 

Your analysis should be thorough, data-informed, and actionable. Focus on providing insights that would be valuable for entrepreneurs, investors, and business strategists looking to enter or expand in this market.

Please provide detailed analysis across all the following areas:

1. **Market Attractiveness**: Assess the overall attractiveness of this market (score 0-100) with qualitative reasoning and key considerations.

2. **Market Size**: Provide conceptual estimates for TAM (Total Addressable Market), SAM (Serviceable Addressable Market), SOM (Serviceable Obtainable Market), and projected CAGR.

3. **Opportunity Zones**: Identify 3-5 specific opportunity areas within this market that show particular promise.

4. **Research Scope**: Define the primary sub-sectors, geographic focus areas, and key inquiry themes for this market.

5. **Strategic Implications**: Highlight the most important strategic considerations for businesses in this space.

6. **Customer Pain Points**: Identify the top unmet needs and pain points that customers face in this market.

7. **Competitive Landscape**: Provide a detailed overview of key competitors including their market position, key differentiators, strengths, estimated market share, and company domain (website) if known.

8. **Enablers & Barriers**: List the key factors that enable success and the main barriers to entry/growth.

9. **SWOT Analysis**: Conduct a comprehensive SWOT analysis for this market.

Be specific to the {$industry} industry and {$region} region. Provide actionable insights rather than generic statements.

Return the response as a JSON object with this exact structure:
{
  \"marketAttractiveness\": {
    \"score\": 85,
    \"statement\": \"Highly attractive market with strong growth potential\",
    \"keyConsiderations\": [
      {\"consideration\": \"Growing consumer demand\", \"type\": \"positive\"},
      {\"consideration\": \"Regulatory challenges\", \"type\": \"negative\"}
    ]
  },
  \"marketSize\": {
    \"tam\": \"$50B\",
    \"sam\": \"$15B\", 
    \"som\": \"$2.5B\",
    \"cagr\": \"12.5%\"
  },
  \"opportunityZones\": [
    {\"title\": \"Zone Title\", \"description\": \"Zone description\"}
  ],
  \"researchScope\": {
    \"primarySubSectors\": [\"Sub-sector 1\", \"Sub-sector 2\"],
    \"coreGeographicFocus\": [\"Region 1\", \"Region 2\"],
    \"keyInquiryThemes\": [\"Theme 1\", \"Theme 2\"]
  },
  \"strategicImplications\": [
    {\"title\": \"Implication Title\", \"description\": \"Implication description\"}
  ],
  \"customerPainPoints\": [
    {\"title\": \"Pain Point Title\", \"description\": \"Pain point description\"}
  ],
  \"competitiveLandscape\": [
    {
      \"name\": \"Competitor Name\", 
      \"marketPosition\": \"Market leader/challenger/follower\",
      \"keyDifferentiators\": [\"Differentiator 1\", \"Differentiator 2\"],
      \"strengths\": [\"Strength 1\", \"Strength 2\"],
      \"marketShare\": \"15%\",
      \"companyDomain\": \"company.com\",
      \"description\": \"Brief company overview\"
    }
  ],
  \"keyEnablersAndBarriers\": {
    \"enablers\": [\"Enabler 1\", \"Enabler 2\"],
    \"barriers\": [\"Barrier 1\", \"Barrier 2\"]
  },
  \"marketSWOT\": {
    \"strengths\": [\"Strength 1\", \"Strength 2\"],
    \"weaknesses\": [\"Weakness 1\", \"Weakness 2\"],
    \"opportunities\": [\"Opportunity 1\", \"Opportunity 2\"],
    \"threats\": [\"Threat 1\", \"Threat 2\"]
  }
}";
    }

    /**
     * Get the schema structure for dashboard data
     */
    protected function getDashboardSchema(): array
    {
        return [
            'marketAttractiveness' => [
                'score' => 'integer between 0-100',
                'statement' => 'qualitative assessment statement',
                'keyConsiderations' => 'array of objects with consideration and type (positive/neutral/negative)'
            ],
            'marketSize' => [
                'tam' => 'Total Addressable Market estimate',
                'sam' => 'Serviceable Addressable Market estimate', 
                'som' => 'Serviceable Obtainable Market estimate',
                'cagr' => 'Compound Annual Growth Rate estimate'
            ],
            'opportunityZones' => 'array of objects with title and description',
            'researchScope' => [
                'primarySubSectors' => 'array of primary sub-sectors',
                'coreGeographicFocus' => 'array of geographic focus areas',
                'keyInquiryThemes' => 'array of key inquiry themes'
            ],
            'strategicImplications' => 'array of objects with title and description',
            'customerPainPoints' => 'array of objects with title and description',
            'competitiveLandscape' => 'array of objects with name, marketPosition, keyDifferentiators (array), strengths (array), marketShare, companyDomain, and description',
            'keyEnablersAndBarriers' => [
                'enablers' => 'array of market enablers',
                'barriers' => 'array of market barriers'
            ],
            'marketSWOT' => [
                'strengths' => 'array of market strengths',
                'weaknesses' => 'array of market weaknesses', 
                'opportunities' => 'array of market opportunities',
                'threats' => 'array of market threats'
            ]
        ];
    }

    public function render()
    {
        return view('livewire.market-research-dashboard');
    }
}
