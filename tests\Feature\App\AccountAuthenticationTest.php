<?php

use App\Filament\App\Pages\Auth\Login;
use App\Filament\App\Pages\Auth\Register;
use App\Models\Account;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Livewire\livewire;

uses()->group('feature', 'app', 'authentication');

uses(RefreshDatabase::class);

test('login screen can be rendered', function () {
    livewire(Login::class, [], 'app')
        ->assertSuccessful();
});

test('registration screen can be rendered', function () {
    livewire(Register::class, [], 'app')
        ->assertSuccessful();
});

test('registration requires all fields', function () {
    livewire(Register::class, [], 'app')
        ->fillForm([
            'name' => '',
            'email' => '',
            'phone' => '',
            'password' => '',
            'passwordConfirmation' => '',
        ])
        ->call('register')
        ->assertHasFormErrors(['name', 'email', 'phone', 'password']);
});

test('registration requires valid email', function () {
    livewire(Register::class, [], 'app')
        ->fillForm([
            'name' => 'Test Account',
            'email' => 'invalid-email',
            'phone' => '**********',
            'password' => 'password',
            'passwordConfirmation' => 'password',
        ])
        ->call('register')
        ->assertHasFormErrors(['email']);
});

test('registration requires unique email', function () {
    $existingAccount = Account::factory()->create(['email' => '<EMAIL>']);

    livewire(Register::class, [], 'app')
        ->fillForm([
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'phone' => '**********',
            'password' => 'password',
            'passwordConfirmation' => 'password',
        ])
        ->call('register')
        ->assertHasFormErrors(['email']);
});

test('registration requires password confirmation', function () {
    livewire(Register::class, [], 'app')
        ->fillForm([
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'phone' => '**********',
            'password' => 'password',
            'passwordConfirmation' => 'different-password',
        ])
        ->call('register')
        ->assertHasFormErrors(['password']);
});

test('can create account via registration form', function () {
    livewire(Register::class, [], 'app')
        ->fillForm([
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'phone' => '**********',
            'password' => 'password123',
            'passwordConfirmation' => 'password123',
        ])
        ->call('register')
        ->assertHasNoFormErrors();

    $this->assertDatabaseHas('accounts', [
        'name' => 'Test Account',
        'email' => '<EMAIL>',
        'phone' => '**********',
        'status' => 'active',
    ]);
});

test('can authenticate with valid credentials', function () {
    $account = Account::factory()->create([
        'email' => '<EMAIL>',
    ]);

    // Test that the login form accepts valid credentials without errors
    livewire(Login::class, [], 'app')
        ->fillForm([
            'email' => '<EMAIL>',
            'password' => 'password',
        ])
        ->assertHasNoFormErrors(['email', 'password']);

    // Test authentication using Laravel's Auth facade directly
    $this->assertTrue(
        \Illuminate\Support\Facades\Auth::guard('account')->attempt([
            'email' => '<EMAIL>',
            'password' => 'password',
        ])
    );

    // Verify the authenticated user is the correct account
    $this->assertEquals($account->id, \Illuminate\Support\Facades\Auth::guard('account')->id());
});

test('cannot authenticate with invalid credentials', function () {
    $account = Account::factory()->create([
        'email' => '<EMAIL>',
    ]);

    livewire(Login::class, [], 'app')
        ->fillForm([
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ])
        ->call('authenticate')
        ->assertHasFormErrors(['email']);
});

test('blocked accounts cannot authenticate', function () {
    $account = Account::factory()->blocked()->create([
        'email' => '<EMAIL>',
    ]);

    livewire(Login::class, [], 'app')
        ->fillForm([
            'email' => '<EMAIL>',
            'password' => 'password',
        ])
        ->call('authenticate')
        ->assertHasFormErrors(['email']);
});
