<?php

namespace Tests\Feature\App;

use App\Jobs\GenerateCriticalHypotheses;
use App\Jobs\GenerateInterviewQuestionnaireJob;
use App\Jobs\GenerateLeanCanvasSection;
use App\Jobs\GenerateMarketSizingAnalysisJob;
use App\Jobs\GenerateStorytellingContentJob;
use App\Models\Account;
use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CreateProjectTest extends TestCase
{
    use RefreshDatabase;

    protected Account $account;

    protected function setUp(): void
    {
        parent::setUp();

        $this->account = Account::factory()->create();
        $this->actingAs($this->account, 'account');
    }

    public function test_project_creation_dispatches_all_content_generation_jobs(): void
    {
        Queue::fake();

        $projectData = [
            'input_prompt' => 'EcoTrack - A mobile app that helps individuals and families track their carbon footprint and make sustainable choices in their daily lives.',
        ];

        // Create project via Filament resource
        $project = Project::create([
            'account_id' => $this->account->id,
            'input_prompt' => $projectData['input_prompt'],
            'status' => 'pending',
        ]);

        // Manually trigger job dispatching to test the functionality
        $createPage = new \App\Filament\App\Resources\ProjectResource\Pages\CreateProject;
        $createPage->dispatchContentGenerationJobs($project);

        // Assert all Lean Canvas section jobs were dispatched
        $leanCanvasSections = [
            'problem',
            'solution',
            'unique_value_proposition',
            'customer_segments',
            'existing_alternatives',
            'key_metrics',
            'channels',
            'unfair_advantage',
            'cost_structure',
            'revenue_streams',
        ];

        foreach ($leanCanvasSections as $section) {
            Queue::assertPushed(GenerateLeanCanvasSection::class, function ($job) use ($project, $section) {
                return $job->project->id === $project->id
                    && $job->sectionKey === $section
                    && $job->isRegeneration === false;
            });
        }

        // Assert Critical Hypotheses job was dispatched
        Queue::assertPushed(GenerateCriticalHypotheses::class, function ($job) use ($project) {
            return $job->project->id === $project->id
                && $job->hypothesisType === null
                && $job->isRegeneration === false;
        });

        // Assert Interview Questionnaire job was dispatched
        Queue::assertPushed(GenerateInterviewQuestionnaireJob::class, function ($job) use ($project) {
            return $job->project->id === $project->id
                && $job->regenerate === false;
        });

        // Assert Storytelling Content job was dispatched
        Queue::assertPushed(GenerateStorytellingContentJob::class, function ($job) use ($project) {
            return $job->project->id === $project->id
                && $job->regenerate === false
                && $job->contentType === null;
        });

        // Assert Market Sizing Analysis job was dispatched
        Queue::assertPushed(GenerateMarketSizingAnalysisJob::class, function ($job) use ($project) {
            return $job->project->id === $project->id
                && $job->analysisType === null
                && $job->isRegeneration === false;
        });

        // Assert total number of jobs dispatched (10 Lean Canvas + 1 Critical Hypotheses + 1 Interview + 1 Storytelling + 1 Market Sizing = 14)
        Queue::assertPushed(GenerateLeanCanvasSection::class, 10);
        Queue::assertPushed(GenerateCriticalHypotheses::class, 1);
        Queue::assertPushed(GenerateInterviewQuestionnaireJob::class, 1);
        Queue::assertPushed(GenerateStorytellingContentJob::class, 1);
        Queue::assertPushed(GenerateMarketSizingAnalysisJob::class, 1);
    }

    public function test_project_creation_handles_job_dispatch_failures_gracefully(): void
    {
        Queue::fake();

        // Mock a scenario where one job dispatch fails
        Queue::shouldReceive('push')
            ->andThrow(new \Exception('Queue service unavailable'));

        $projectData = [
            'input_prompt' => 'Test project for error handling',
        ];

        $project = Project::create([
            'account_id' => $this->account->id,
            'input_prompt' => $projectData['input_prompt'],
            'status' => 'pending',
        ]);

        // This should not throw an exception even if job dispatch fails
        $createPage = new \App\Filament\App\Resources\ProjectResource\Pages\CreateProject;

        // The method should complete without throwing exceptions
        $this->expectNotToPerformAssertions();
        $createPage->dispatchContentGenerationJobs($project);
    }

    public function test_project_creation_sets_correct_attributes(): void
    {
        $projectData = [
            'input_prompt' => 'Test startup idea for attribute validation',
        ];

        $project = Project::create([
            'account_id' => $this->account->id,
            'input_prompt' => $projectData['input_prompt'],
            'status' => 'pending',
        ]);

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'account_id' => $this->account->id,
            'input_prompt' => $projectData['input_prompt'],
            'status' => 'pending',
        ]);

        $this->assertEquals($this->account->id, $project->account_id);
        $this->assertEquals('pending', $project->status);
        $this->assertEquals($projectData['input_prompt'], $project->input_prompt);
    }

    public function test_dispatch_jobs_handles_invalid_record_type(): void
    {
        $createPage = new \App\Filament\App\Resources\ProjectResource\Pages\CreateProject;

        // Should handle gracefully and not throw exceptions
        $this->expectNotToPerformAssertions();
        $createPage->dispatchContentGenerationJobs(new \stdClass); // Invalid record type
    }
}
