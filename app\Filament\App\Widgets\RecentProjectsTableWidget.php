<?php

namespace App\Filament\App\Widgets;

use App\Filament\App\Resources\ProjectResource;
use App\Models\Project;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class RecentProjectsTableWidget extends BaseWidget
{
    protected static ?string $heading = 'Recent Projects';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 2;

    public function table(Table $table): Table
    {
        return $table
            ->query(
                fn (): Builder => Project::query()
                    ->where('account_id', auth('account')->id() ?? 0)
                    ->latest()
            )
            ->defaultPaginationPageOption(5)
            ->paginationPageOptions([5, 10, 25])
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Project #')
                    ->sortable(),

                Tables\Columns\TextColumn::make('input_prompt')
                    ->label('Description')
                    ->limit(80)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 80) {
                            return null;
                        }

                        return $state;
                    }),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'warning' => 'pending',
                        'primary' => 'processing',
                        'success' => 'completed',
                        'danger' => 'failed',
                    ])
                    ->icons([
                        'heroicon-o-clock' => 'pending',
                        'heroicon-o-arrow-path' => 'processing',
                        'heroicon-o-check-circle' => 'completed',
                        'heroicon-o-x-circle' => 'failed',
                    ]),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->since()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Project $record): string => ProjectResource::getUrl('view', ['record' => $record])),

                Tables\Actions\Action::make('edit')
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->url(fn (Project $record): string => ProjectResource::getUrl('edit', ['record' => $record])),
            ])
            ->emptyStateHeading('No projects yet')
            ->emptyStateDescription('Get started by creating your first startup project.')
            ->emptyStateActions([
                Tables\Actions\Action::make('create')
                    ->label('Create Your First Project')
                    ->icon('heroicon-o-plus')
                    ->url(ProjectResource::getUrl('create')),
            ])
            ->headerActions([
                Tables\Actions\Action::make('view_all')
                    ->label('View All Projects')
                    ->icon('heroicon-o-arrow-right')
                    ->url(ProjectResource::getUrl('index'))
                    ->color('gray'),
            ]);
    }
}
