<?php

namespace App\Services;

use App\Models\Project;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Log;

class LandingPageTemplateEngine
{
    protected array $availableThemes = [
        'modern' => [
            'name' => 'Modern',
            'description' => 'Clean, contemporary design with gradients and modern typography',
            'template' => 'landing-pages.modern',
        ],
        'minimal' => [
            'name' => 'Minimal',
            'description' => 'Simple, clean design focusing on content and readability',
            'template' => 'landing-pages.minimal',
        ],
        'tech' => [
            'name' => 'Tech',
            'description' => 'Technology-focused design with dark theme and neon accents',
            'template' => 'landing-pages.tech',
        ],
        'creative' => [
            'name' => 'Creative',
            'description' => 'Bold, colorful design for creative and artistic ventures',
            'template' => 'landing-pages.creative',
        ],
    ];

    public function __construct(
        protected ContentGenerationService $contentService
    ) {}

    /**
     * Get all available themes
     */
    public function getAvailableThemes(): array
    {
        return $this->availableThemes;
    }

    /**
     * Generate landing page HTML for a project with specified theme
     */
    public function generateLandingPage(Project $project, string $theme = 'modern'): string
    {
        if (!isset($this->availableThemes[$theme])) {
            throw new \InvalidArgumentException("Theme '{$theme}' not found");
        }

        try {
            // Assemble all content data
            $contentData = $this->assembleContentData($project);
            
            // Prepare template data
            $templateData = [
                'project' => $project,
                'content' => $contentData,
                'theme' => $theme,
                'themeConfig' => $this->availableThemes[$theme],
                'meta' => $this->generateMetaData($project, $contentData),
            ];

            // Render the template
            $template = $this->availableThemes[$theme]['template'];
            $html = View::make($template, $templateData)->render();

            Log::info('Landing page generated successfully', [
                'project_id' => $project->id,
                'theme' => $theme,
                'content_sections' => count($contentData),
            ]);

            return $html;

        } catch (\Exception $e) {
            Log::error('Failed to generate landing page', [
                'project_id' => $project->id,
                'theme' => $theme,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Assemble all generated content for the landing page
     */
    protected function assembleContentData(Project $project): array
    {
        $content = [];

        // Get Lean Canvas sections
        $leanCanvasSections = [
            'problem', 'solution', 'unique_value_proposition', 'customer_segments',
            'existing_alternatives', 'key_metrics', 'channels', 'unfair_advantage',
            'cost_structure', 'revenue_streams'
        ];

        foreach ($leanCanvasSections as $section) {
            $contentType = "lean_canvas_{$section}";
            $generatedContent = $project->getGeneratedContent($contentType);
            if ($generatedContent) {
                $content['lean_canvas'][$section] = $this->extractContentData($generatedContent);
            }
        }

        // Get Critical Hypotheses
        $hypothesesTypes = ['desirability', 'viability', 'feasibility'];
        foreach ($hypothesesTypes as $type) {
            $contentType = "critical_hypothesis_{$type}";
            $generatedContent = $project->getGeneratedContent($contentType);
            if ($generatedContent) {
                $content['critical_hypotheses'][$type] = $this->extractContentData($generatedContent);
            }
        }

        // Get Interview Questionnaire
        $interviewContent = $project->getGeneratedContent('interview_questionnaire');
        if ($interviewContent) {
            $content['interview_questionnaire'] = $this->extractContentData($interviewContent);
        }

        // Get Storytelling Content
        $storytellingTypes = ['brand_wheel', 'startup_naming', 'elevator_pitch'];
        foreach ($storytellingTypes as $type) {
            $generatedContent = $project->getGeneratedContent($type);
            if ($generatedContent) {
                $content['storytelling'][$type] = $this->extractContentData($generatedContent);
            }
        }

        return $content;
    }

    /**
     * Extract usable content from GeneratedContent model
     */
    protected function extractContentData($generatedContent): array
    {
        if (!$generatedContent || !$generatedContent->content_data) {
            return [];
        }

        $data = $generatedContent->content_data;
        
        // If it's already an array, return it
        if (is_array($data)) {
            return $data;
        }

        // If it's JSON string, decode it
        if (is_string($data)) {
            $decoded = json_decode($data, true);
            return $decoded ?: ['content' => $data];
        }

        return [];
    }

    /**
     * Generate meta data for the landing page
     */
    protected function generateMetaData(Project $project, array $contentData): array
    {
        // Extract title from startup naming or use fallback
        $title = $contentData['storytelling']['startup_naming']['business_name'] ?? 
                 $contentData['storytelling']['startup_naming']['name'] ?? 
                 'Startup Landing Page';

        // Extract description from elevator pitch or value proposition
        $description = $contentData['storytelling']['elevator_pitch']['pitch'] ?? 
                      $contentData['lean_canvas']['unique_value_proposition']['content'] ?? 
                      substr($project->input_prompt, 0, 160);

        // Extract keywords from various content sections
        $keywords = [];
        if (isset($contentData['lean_canvas']['customer_segments'])) {
            $keywords[] = 'startup';
            $keywords[] = 'business';
        }

        return [
            'title' => $title,
            'description' => $description,
            'keywords' => implode(', ', array_unique($keywords)),
            'author' => $title,
            'viewport' => 'width=device-width, initial-scale=1.0',
        ];
    }

    /**
     * Get embedded CSS for a specific theme
     */
    public function getThemeCSS(string $theme): string
    {
        if (!isset($this->availableThemes[$theme])) {
            throw new \InvalidArgumentException("Theme '{$theme}' not found");
        }

        $cssFile = resource_path("css/landing-pages/{$theme}.css");
        
        if (file_exists($cssFile)) {
            return file_get_contents($cssFile);
        }

        // Return default CSS if theme-specific CSS doesn't exist
        return $this->getDefaultCSS();
    }

    /**
     * Get default CSS for landing pages
     */
    protected function getDefaultCSS(): string
    {
        return "
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .hero {
            padding: 100px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .section {
            padding: 80px 0;
        }
        
        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .section h2 {
                font-size: 2rem;
            }
        }
        ";
    }

    /**
     * Validate that all required content exists for landing page generation
     */
    public function validateProjectContent(Project $project): array
    {
        $validationResults = [
            'isValid' => true,
            'errors' => [],
            'warnings' => [],
            'missingContent' => [],
        ];

        // Check for essential content
        $essentialContent = [
            'lean_canvas_unique_value_proposition' => 'Value Proposition',
            'lean_canvas_problem' => 'Problem Statement',
            'lean_canvas_solution' => 'Solution',
        ];

        foreach ($essentialContent as $contentType => $displayName) {
            if (!$project->hasGeneratedContent($contentType)) {
                $validationResults['errors'][] = "Missing essential content: {$displayName}";
                $validationResults['missingContent'][] = $contentType;
                $validationResults['isValid'] = false;
            }
        }

        // Check for recommended content
        $recommendedContent = [
            'elevator_pitch' => 'Elevator Pitch',
            'brand_wheel' => 'Brand Wheel',
            'startup_naming' => 'Startup Naming',
        ];

        foreach ($recommendedContent as $contentType => $displayName) {
            if (!$project->hasGeneratedContent($contentType)) {
                $validationResults['warnings'][] = "Missing recommended content: {$displayName}";
                $validationResults['missingContent'][] = $contentType;
            }
        }

        return $validationResults;
    }
} 