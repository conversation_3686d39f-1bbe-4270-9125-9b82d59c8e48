# Database Documentation

## Overview

The Venture Discovery Platform uses a relational database design with <PERSON><PERSON>'s Eloquent ORM. The database supports multi-tenant architecture with account-based project management and AI-generated content storage.

## Database Schema

### Core Tables

#### Users Table
```sql
- id (bigint, primary key)
- name (varchar)
- email (varchar, unique)
- email_verified_at (timestamp, nullable)
- password (varchar)
- remember_token (varchar, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

#### Accounts Table
```sql
- id (bigint, primary key)
- name (varchar)
- slug (varchar, unique)
- description (text, nullable)
- settings (json, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

#### Projects Table
```sql
- id (bigint, primary key)
- account_id (bigint, foreign key)
- name (varchar)
- description (text, nullable)
- status (enum: draft, active, completed, archived)
- settings (json, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

#### Generated Content Table
```sql
- id (bigint, primary key)
- project_id (bigint, foreign key)
- type (varchar) // lean_canvas, hypotheses, questionnaire, storytelling
- section (varchar, nullable) // for lean canvas sections
- content (json)
- status (enum: pending, generating, completed, failed)
- metadata (json, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

#### Account History Table
```sql
- id (bigint, primary key)
- account_id (bigint, foreign key)
- user_id (bigint, foreign key, nullable)
- action (varchar)
- description (text, nullable)
- metadata (json, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

### Relationships

#### User Model Relationships
- `belongsToMany(Account::class)` - Users can belong to multiple accounts
- `hasMany(AccountHistory::class)` - Track user actions

#### Account Model Relationships
- `belongsToMany(User::class)` - Accounts can have multiple users
- `hasMany(Project::class)` - Accounts can have multiple projects
- `hasMany(AccountHistory::class)` - Track account activities

#### Project Model Relationships
- `belongsTo(Account::class)` - Each project belongs to an account
- `hasMany(GeneratedContent::class)` - Projects can have multiple generated content pieces

#### Generated Content Model Relationships
- `belongsTo(Project::class)` - Each content belongs to a project

## Model Definitions

### User Model
```php
class User extends Authenticatable
{
    protected $fillable = ['name', 'email', 'password'];
    protected $hidden = ['password', 'remember_token'];
    protected $casts = ['email_verified_at' => 'datetime'];
    
    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class);
    }
}
```

### Account Model
```php
class Account extends Model
{
    protected $fillable = ['name', 'slug', 'description', 'settings'];
    protected $casts = ['settings' => 'array'];
    
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
    
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }
}
```

### Project Model
```php
class Project extends Model
{
    protected $fillable = ['account_id', 'name', 'description', 'status', 'settings'];
    protected $casts = ['settings' => 'array'];
    
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }
    
    public function generatedContent(): HasMany
    {
        return $this->hasMany(GeneratedContent::class);
    }
}
```

### Generated Content Model
```php
class GeneratedContent extends Model
{
    protected $fillable = ['project_id', 'type', 'section', 'content', 'status', 'metadata'];
    protected $casts = ['content' => 'array', 'metadata' => 'array'];
    
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
```

## Database Migrations

### Key Migration Files
- `create_users_table.php` - Base user authentication
- `create_accounts_table.php` - Multi-tenant account structure
- `create_projects_table.php` - Project management
- `create_generated_content_table.php` - AI-generated content storage
- `create_account_history_table.php` - Activity tracking

### Migration Commands
```bash
# Run all migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Reset and re-run migrations
php artisan migrate:fresh

# Seed database with sample data
php artisan db:seed
```

## Database Seeding

### Seeders Available
- `DatabaseSeeder.php` - Main seeder orchestrator
- `UserSeeder.php` - Create sample users
- `AccountSeeder.php` - Create sample accounts
- `ProjectSeeder.php` - Create sample projects

### Seeding Commands
```bash
# Run all seeders
php artisan db:seed

# Run specific seeder
php artisan db:seed --class=UserSeeder

# Reseed database (fresh migration + seed)
php artisan migrate:fresh --seed
```

## Query Optimization

### Indexes
- Primary keys on all tables
- Foreign key indexes for relationships
- Unique indexes on email, slug fields
- Composite indexes for frequently queried combinations

### Eager Loading
```php
// Load projects with their generated content
$projects = Project::with('generatedContent')->get();

// Load account with users and projects
$account = Account::with(['users', 'projects'])->find($id);
```

### Query Scopes
```php
// Project scopes
Project::active()->get();
Project::forAccount($accountId)->get();

// Generated content scopes
GeneratedContent::completed()->get();
GeneratedContent::ofType('lean_canvas')->get();
```

## Database Configuration

### Environment Variables
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=venture_discovery
DB_USERNAME=root
DB_PASSWORD=
```

### Connection Configuration
Located in `config/database.php` with support for:
- MySQL (primary)
- PostgreSQL
- SQLite (testing)
- Redis (caching/sessions)

## Backup and Maintenance

### Backup Commands
```bash
# Create database backup
php artisan backup:run

# Clean old backups
php artisan backup:clean
```

### Maintenance Commands
```bash
# Optimize database
php artisan optimize

# Clear application cache
php artisan cache:clear

# Rebuild configuration cache
php artisan config:cache
``` 