#!/bin/bash

# Venture Discovery Platform - macOS Deployment Script
# This script automates the deployment process on macOS systems

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="venture-discovery"
PHP_VERSION="8.2"
NODE_VERSION="20"
MYSQL_VERSION="8.0"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Homebrew is installed
check_homebrew() {
    if ! command_exists brew; then
        print_status "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        # Add Homebrew to PATH for Apple Silicon Macs
        if [[ $(uname -m) == "arm64" ]]; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
            eval "$(/opt/homebrew/bin/brew shellenv)"
        fi
    else
        print_success "Homebrew is already installed"
    fi
}

# Function to install PHP
install_php() {
    if ! command_exists php || [[ $(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1-2) != "$PHP_VERSION" ]]; then
        print_status "Installing PHP $PHP_VERSION..."
        brew install php@$PHP_VERSION
        brew link php@$PHP_VERSION --force
        
        # Add PHP to PATH
        echo "export PATH=\"/opt/homebrew/bin:\$PATH\"" >> ~/.zshrc
        export PATH="/opt/homebrew/bin:$PATH"
    else
        print_success "PHP $PHP_VERSION is already installed"
    fi
}

# Function to install Composer
install_composer() {
    if ! command_exists composer; then
        print_status "Installing Composer..."
        curl -sS https://getcomposer.org/installer | php
        sudo mv composer.phar /usr/local/bin/composer
        chmod +x /usr/local/bin/composer
    else
        print_success "Composer is already installed"
    fi
}

# Function to install Node.js
install_node() {
    if ! command_exists node || [[ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt $NODE_VERSION ]]; then
        print_status "Installing Node.js $NODE_VERSION..."
        brew install node@$NODE_VERSION
        brew link node@$NODE_VERSION --force
    else
        print_success "Node.js $NODE_VERSION is already installed"
    fi
}

# Function to install MySQL
install_mysql() {
    if ! command_exists mysql; then
        print_status "Installing MySQL $MYSQL_VERSION..."
        brew install mysql@$MYSQL_VERSION
        brew link mysql@$MYSQL_VERSION --force
        
        print_status "Starting MySQL service..."
        brew services start mysql@$MYSQL_VERSION
        
        print_warning "Please run 'mysql_secure_installation' after deployment to secure your MySQL installation"
    else
        print_success "MySQL is already installed"
    fi
}

# Function to install Redis
install_redis() {
    if ! command_exists redis-server; then
        print_status "Installing Redis..."
        brew install redis
        brew services start redis
    else
        print_success "Redis is already installed"
    fi
}

# Function to install additional tools
install_tools() {
    print_status "Installing additional development tools..."
    
    # Git (usually pre-installed on macOS)
    if ! command_exists git; then
        brew install git
    fi
    
    # Supervisor for queue management
    if ! command_exists supervisord; then
        brew install supervisor
    fi
    
    # Nginx (optional web server)
    if ! command_exists nginx; then
        print_status "Installing Nginx..."
        brew install nginx
    fi
}

# Function to setup project
setup_project() {
    print_status "Setting up Venture Discovery Platform..."
    
    # Create project directory if it doesn't exist
    if [ ! -d "/usr/local/var/www/$PROJECT_NAME" ]; then
        sudo mkdir -p /usr/local/var/www/$PROJECT_NAME
        sudo chown $(whoami):staff /usr/local/var/www/$PROJECT_NAME
    fi
    
    cd /usr/local/var/www/$PROJECT_NAME
    
    # Clone or update repository
    if [ ! -d ".git" ]; then
        print_status "Cloning repository..."
        git clone https://github.com/your-username/$PROJECT_NAME.git .
    else
        print_status "Updating repository..."
        git pull origin main
    fi
    
    # Install PHP dependencies
    print_status "Installing PHP dependencies..."
    composer install --optimize-autoloader --no-dev
    
    # Install Node.js dependencies
    print_status "Installing Node.js dependencies..."
    npm ci
    
    # Build assets
    print_status "Building frontend assets..."
    npm run build
}

# Function to configure environment
configure_environment() {
    print_status "Configuring environment..."
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Please update .env file with your configuration"
    fi
    
    # Generate application key
    php artisan key:generate
    
    # Create storage symlink
    php artisan storage:link
    
    # Set permissions
    chmod -R 755 storage bootstrap/cache
    chmod -R 775 storage/logs
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Create database
    read -p "Enter MySQL root password: " -s mysql_password
    echo
    
    mysql -u root -p$mysql_password -e "CREATE DATABASE IF NOT EXISTS venture_discovery;"
    mysql -u root -p$mysql_password -e "CREATE USER IF NOT EXISTS 'venture_user'@'localhost' IDENTIFIED BY 'secure_password';"
    mysql -u root -p$mysql_password -e "GRANT ALL PRIVILEGES ON venture_discovery.* TO 'venture_user'@'localhost';"
    mysql -u root -p$mysql_password -e "FLUSH PRIVILEGES;"
    
    # Run migrations
    php artisan migrate --force
    
    # Seed database (optional)
    read -p "Do you want to seed the database with sample data? (y/n): " seed_db
    if [[ $seed_db == "y" || $seed_db == "Y" ]]; then
        php artisan db:seed
    fi
}

# Function to configure web server
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Create Nginx configuration
    sudo tee /opt/homebrew/etc/nginx/servers/$PROJECT_NAME.conf > /dev/null <<EOF
server {
    listen 80;
    server_name $PROJECT_NAME.local;
    root /usr/local/var/www/$PROJECT_NAME/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
EOF

    # Add to hosts file
    if ! grep -q "$PROJECT_NAME.local" /etc/hosts; then
        echo "127.0.0.1 $PROJECT_NAME.local" | sudo tee -a /etc/hosts
    fi
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Restart Nginx
    brew services restart nginx
}

# Function to setup queue worker
setup_queue_worker() {
    print_status "Setting up queue worker..."
    
    # Create supervisor configuration
    sudo tee /opt/homebrew/etc/supervisor.d/$PROJECT_NAME-worker.conf > /dev/null <<EOF
[program:$PROJECT_NAME-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /usr/local/var/www/$PROJECT_NAME/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$(whoami)
numprocs=2
redirect_stderr=true
stdout_logfile=/usr/local/var/www/$PROJECT_NAME/storage/logs/worker.log
stopwaitsecs=3600
EOF

    # Start supervisor
    brew services restart supervisor
    sudo supervisorctl reread
    sudo supervisorctl update
    sudo supervisorctl start $PROJECT_NAME-worker:*
}

# Function to setup SSL (optional)
setup_ssl() {
    read -p "Do you want to setup SSL with mkcert? (y/n): " setup_ssl
    if [[ $setup_ssl == "y" || $setup_ssl == "Y" ]]; then
        if ! command_exists mkcert; then
            print_status "Installing mkcert..."
            brew install mkcert
            mkcert -install
        fi
        
        print_status "Creating SSL certificates..."
        cd /usr/local/var/www/$PROJECT_NAME
        mkcert $PROJECT_NAME.local
        
        # Update Nginx configuration for SSL
        # (SSL configuration would be added here)
        print_success "SSL certificates created. Update Nginx configuration manually for HTTPS."
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    cd /usr/local/var/www/$PROJECT_NAME
    
    # Run PHP tests
    ./vendor/bin/pest
    
    # Run code quality checks
    composer check
}

# Function to display final information
display_final_info() {
    print_success "Deployment completed successfully!"
    echo
    echo "=== Venture Discovery Platform ==="
    echo "Project Location: /usr/local/var/www/$PROJECT_NAME"
    echo "Local URL: http://$PROJECT_NAME.local"
    echo "Admin Panel: http://$PROJECT_NAME.local/admin"
    echo
    echo "=== Next Steps ==="
    echo "1. Update .env file with your OpenAI API key and other configurations"
    echo "2. Configure your email settings in .env"
    echo "3. Run 'mysql_secure_installation' to secure MySQL"
    echo "4. Access the application at http://$PROJECT_NAME.local"
    echo
    echo "=== Useful Commands ==="
    echo "Start services: brew services start nginx mysql redis supervisor"
    echo "Stop services: brew services stop nginx mysql redis supervisor"
    echo "View logs: tail -f /usr/local/var/www/$PROJECT_NAME/storage/logs/laravel.log"
    echo "Queue worker status: sudo supervisorctl status"
    echo
}

# Main deployment function
main() {
    print_status "Starting Venture Discovery Platform deployment on macOS..."
    echo
    
    # Check for macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS only!"
        exit 1
    fi
    
    # Install dependencies
    check_homebrew
    install_php
    install_composer
    install_node
    install_mysql
    install_redis
    install_tools
    
    # Setup project
    setup_project
    configure_environment
    setup_database
    configure_nginx
    setup_queue_worker
    setup_ssl
    
    # Run tests
    run_tests
    
    # Display final information
    display_final_info
}

# Run main function
main "$@" 