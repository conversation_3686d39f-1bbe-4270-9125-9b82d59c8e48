<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $account_id
 * @property string $input_prompt
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Project query()
 * @method static Project create(array $attributes = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Project where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|Project latest($column = 'created_at')
 * @method static Project|null find($id, $columns = ['*'])
 * @method static \Illuminate\Database\Eloquent\Builder|Project whereIn($column, $values, $boolean = 'and', $not = false)
 */
class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_id',
        'input_prompt',
        'status',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Get the generated contents for the project.
     */
    public function generatedContents(): HasMany
    {
        return $this->hasMany(GeneratedContent::class);
    }

    /**
     * Get generated content by type.
     */
    public function getGeneratedContent(string $contentType): ?GeneratedContent
    {
        /** @var GeneratedContent|null $content */
        $content = $this->generatedContents()
            ->where('content_type', $contentType)
            ->first();

        return $content;
    }

    /**
     * Check if content exists for a specific type.
     */
    public function hasGeneratedContent(string $contentType): bool
    {
        return $this->generatedContents()
            ->where('content_type', $contentType)
            ->exists();
    }
}
