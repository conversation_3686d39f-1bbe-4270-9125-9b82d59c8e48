<div>
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        @if($this->hasAnyContent())
            <div class="flex items-center space-x-2">
                <!-- Content Count Badge -->
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                    {{ collect([$this->hasBrandWheel(), $this->hasStartupNaming(), $this->hasElevatorPitch()])->filter()->count() }}/3 sections
                </span>
                
                <!-- Status Badge -->
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    ✓ Generated
                </span>
            </div>
        @endif
    </div>

    <!-- Error Message -->
    @if($error)
        <div class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-800 dark:text-red-200">{{ $error }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Content -->
    @if($this->hasAnyContent())
        <div class="space-y-8">
            <!-- Brand Wheel Section -->
            @if($this->hasBrandWheel())
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Brand Wheel
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-blue-600 dark:text-blue-300">
                                {{ $this->getGeneratedAt('brand_wheel') }}
                            </span>
                            <button 
                                wire:click="regenerateBrandWheel"
                                wire:loading.attr="disabled"
                                wire:target="regenerateBrandWheel"
                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 disabled:opacity-50"
                                title="Regenerate Brand Wheel"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if(isset($brandWheel->content_data['mission']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Mission</h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $brandWheel->content_data['mission'] }}</p>
                            </div>
                        @endif
                        
                        @if(isset($brandWheel->content_data['vision']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Vision</h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $brandWheel->content_data['vision'] }}</p>
                            </div>
                        @endif
                        
                        @if(isset($brandWheel->content_data['values']) && is_array($brandWheel->content_data['values']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Core Values</h4>
                                <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                    @foreach($brandWheel->content_data['values'] as $value)
                                        <li class="flex items-start">
                                            <span class="text-blue-500 mr-2">•</span>
                                            {{ $value }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        @if(isset($brandWheel->content_data['personality']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Brand Personality</h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $brandWheel->content_data['personality'] }}</p>
                            </div>
                        @endif
                        
                        @if(isset($brandWheel->content_data['tone_of_voice']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Tone of Voice</h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $brandWheel->content_data['tone_of_voice'] }}</p>
                            </div>
                        @endif
                        
                        @if(isset($brandWheel->content_data['brand_promise']))
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-100 dark:border-blue-700 md:col-span-2">
                                <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Brand Promise</h4>
                                <p class="text-sm text-gray-700 dark:text-gray-300">{{ $brandWheel->content_data['brand_promise'] }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Startup Naming Section -->
            @if($this->hasStartupNaming())
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-green-900 dark:text-green-100 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                            Startup Naming
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-green-600 dark:text-green-300">
                                {{ $this->getGeneratedAt('startup_naming') }}
                            </span>
                            <button 
                                wire:click="regenerateStartupNaming"
                                wire:loading.attr="disabled"
                                wire:target="regenerateStartupNaming"
                                class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 disabled:opacity-50"
                                title="Regenerate Startup Naming"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    @if(isset($startupNaming->content_data['suggestions']) && is_array($startupNaming->content_data['suggestions']))
                        <div class="space-y-3">
                            @foreach($startupNaming->content_data['suggestions'] as $index => $suggestion)
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-green-100 dark:border-green-700">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            @if(is_array($suggestion))
                                                {{-- New structure: suggestion is an object with name, rationale, etc. --}}
                                                <h4 class="text-base font-semibold text-green-900 dark:text-green-100 mb-2">{{ $suggestion['name'] ?? 'Unnamed' }}</h4>
                                                @if(isset($suggestion['rationale']))
                                                    <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">{{ $suggestion['rationale'] }}</p>
                                                @endif
                                                <div class="flex flex-wrap gap-2 text-xs">
                                                    @if(isset($suggestion['domain_availability']))
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                            🌐 {{ $suggestion['domain_availability'] }}
                                                        </span>
                                                    @endif
                                                    @if(isset($suggestion['trademark_considerations']))
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full 
                                                            @if(str_contains(strtolower($suggestion['trademark_considerations']), 'low'))
                                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                                            @elseif(str_contains(strtolower($suggestion['trademark_considerations']), 'medium'))
                                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                                            @else
                                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                                            @endif
                                                        ">
                                                            ⚖️ {{ $suggestion['trademark_considerations'] }}
                                                        </span>
                                                    @endif
                                                </div>
                                            @else
                                                {{-- Legacy structure: suggestion is a string, use separate arrays --}}
                                                <h4 class="text-base font-semibold text-green-900 dark:text-green-100 mb-2">{{ $suggestion }}</h4>
                                                @if(isset($startupNaming->content_data['rationales'][$index]))
                                                    <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">{{ $startupNaming->content_data['rationales'][$index] }}</p>
                                                @endif
                                                <div class="flex flex-wrap gap-2 text-xs">
                                                    @if(isset($startupNaming->content_data['domain_availability'][$index]))
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                            🌐 {{ $startupNaming->content_data['domain_availability'][$index] }}
                                                        </span>
                                                    @endif
                                                    @if(isset($startupNaming->content_data['trademark_considerations'][$index]))
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full 
                                                            @if(str_contains(strtolower($startupNaming->content_data['trademark_considerations'][$index]), 'low'))
                                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                                            @elseif(str_contains(strtolower($startupNaming->content_data['trademark_considerations'][$index]), 'medium'))
                                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                                            @else
                                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                                            @endif
                                                        ">
                                                            ⚖️ {{ $startupNaming->content_data['trademark_considerations'][$index] }}
                                                        </span>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                        <div class="ml-4 text-right">
                                            <span class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium">
                                                {{ $index + 1 }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endif

            <!-- Elevator Pitch Section -->
            @if($this->hasElevatorPitch())
                <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-purple-900 dark:text-purple-100 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                            </svg>
                            Elevator Pitch
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-purple-600 dark:text-purple-300">
                                {{ $this->getGeneratedAt('elevator_pitch') }}
                            </span>
                            <button 
                                wire:click="toggleFullElevatorPitch"
                                class="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200"
                                title="Toggle Full View"
                            >
                                @if($showFullElevatorPitch)
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </button>
                            <button 
                                wire:click="regenerateElevatorPitch"
                                wire:loading.attr="disabled"
                                wire:target="regenerateElevatorPitch"
                                class="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 disabled:opacity-50"
                                title="Regenerate Elevator Pitch"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        @if($showFullElevatorPitch)
                            <!-- Full Elevator Pitch View -->
                            @if(isset($elevatorPitch->content_data['pitch_30_seconds']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center">
                                        <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium mr-2">30s</span>
                                        30-Second Pitch
                                    </h4>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $elevatorPitch->content_data['pitch_30_seconds'] }}</p>
                                </div>
                            @endif
                            
                            @if(isset($elevatorPitch->content_data['pitch_60_seconds']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center">
                                        <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium mr-2">60s</span>
                                        60-Second Pitch
                                    </h4>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $elevatorPitch->content_data['pitch_60_seconds'] }}</p>
                                </div>
                            @endif
                            
                            @if(isset($elevatorPitch->content_data['pitch_90_seconds']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center">
                                        <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium mr-2">90s</span>
                                        90-Second Pitch
                                    </h4>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $elevatorPitch->content_data['pitch_90_seconds'] }}</p>
                                </div>
                            @endif
                            
                            @if(isset($elevatorPitch->content_data['key_points']) && is_array($elevatorPitch->content_data['key_points']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">Key Points</h4>
                                    <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                        @foreach($elevatorPitch->content_data['key_points'] as $point)
                                            <li class="flex items-start">
                                                <span class="text-purple-500 mr-2">•</span>
                                                {{ $point }}
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            
                            @if(isset($elevatorPitch->content_data['call_to_action']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">Call to Action</h4>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $elevatorPitch->content_data['call_to_action'] }}</p>
                                </div>
                            @endif
                        @else
                            <!-- Preview Mode - Show 60-second pitch -->
                            @if(isset($elevatorPitch->content_data['pitch_60_seconds']))
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-100 dark:border-purple-700">
                                    <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2 flex items-center">
                                        <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs font-medium mr-2">60s</span>
                                        60-Second Pitch Preview
                                    </h4>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $elevatorPitch->content_data['pitch_60_seconds'] }}</p>
                                    <p class="text-xs text-purple-600 dark:text-purple-400 mt-2 italic">Click the expand button to see all pitch versions and details</p>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>
            @endif

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                @if(!$this->hasAnyContent())
                    <button 
                        wire:click="generateAllContent"
                        wire:loading.attr="disabled"
                        wire:target="generateAllContent"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white text-sm font-medium rounded-md transition-colors duration-200"
                    >
                        <svg wire:loading.remove wire:target="generateAllContent" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <svg wire:loading wire:target="generateAllContent" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span wire:loading.remove wire:target="generateAllContent">Generate All Storytelling Content</span>
                        <span wire:loading wire:target="generateAllContent">Generating...</span>
                    </button>
                @else
                    @if(!$this->hasBrandWheel())
                        <button 
                            wire:click="generateBrandWheel"
                            wire:loading.attr="disabled"
                            wire:target="generateBrandWheel"
                            class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white text-sm font-medium rounded-md transition-colors duration-200"
                        >
                            <svg wire:loading.remove wire:target="generateBrandWheel" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <svg wire:loading wire:target="generateBrandWheel" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span wire:loading.remove wire:target="generateBrandWheel">Generate Brand Wheel</span>
                            <span wire:loading wire:target="generateBrandWheel">Generating...</span>
                        </button>
                    @endif

                    @if(!$this->hasStartupNaming())
                        <button 
                            wire:click="generateStartupNaming"
                            wire:loading.attr="disabled"
                            wire:target="generateStartupNaming"
                            class="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-sm font-medium rounded-md transition-colors duration-200"
                        >
                            <svg wire:loading.remove wire:target="generateStartupNaming" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <svg wire:loading wire:target="generateStartupNaming" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span wire:loading.remove wire:target="generateStartupNaming">Generate Startup Naming</span>
                            <span wire:loading wire:target="generateStartupNaming">Generating...</span>
                        </button>
                    @endif

                    @if(!$this->hasElevatorPitch())
                        <button 
                            wire:click="generateElevatorPitch"
                            wire:loading.attr="disabled"
                            wire:target="generateElevatorPitch"
                            class="inline-flex items-center px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white text-sm font-medium rounded-md transition-colors duration-200"
                        >
                            <svg wire:loading.remove wire:target="generateElevatorPitch" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <svg wire:loading wire:target="generateElevatorPitch" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span wire:loading.remove wire:target="generateElevatorPitch">Generate Elevator Pitch</span>
                            <span wire:loading wire:target="generateElevatorPitch">Generating...</span>
                        </button>
                    @endif

                    <button 
                        wire:click="downloadStorytellingContent"
                        class="inline-flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Content
                    </button>
                @endif
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No storytelling content yet</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by generating your brand wheel, startup naming suggestions, and elevator pitch.</p>
            <div class="mt-6">
                <button 
                    wire:click="generateAllContent"
                    wire:loading.attr="disabled"
                    wire:target="generateAllContent"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white text-sm font-medium rounded-md transition-colors duration-200"
                >
                    <svg wire:loading.remove wire:target="generateAllContent" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <svg wire:loading wire:target="generateAllContent" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span wire:loading.remove wire:target="generateAllContent">Generate All Storytelling Content</span>
                    <span wire:loading wire:target="generateAllContent">Generating...</span>
                </button>
            </div>
        </div>
    @endif

    <!-- Polling Script -->
    <script>
        document.addEventListener('livewire:init', () => {
            let pollingInterval;
            
            // Start polling when component loads
            function startPolling() {
                pollingInterval = setInterval(() => {
                    @this.pollForUpdates();
                }, 5000); // Poll every 5 seconds
            }
            
            // Stop polling when all content is generated
            Livewire.on('stop-polling', () => {
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = null;
                }
            });
            
            // Start polling immediately if there's any loading state or missing content
            @if($isLoadingBrandWheel || $isLoadingStartupNaming || $isLoadingElevatorPitch || $isRegeneratingBrandWheel || $isRegeneratingStartupNaming || $isRegeneratingElevatorPitch || !$this->hasAllContent())
                startPolling();
            @endif
        });
    </script>
</div>
