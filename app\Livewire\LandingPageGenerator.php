<?php

namespace App\Livewire;

use App\Models\Project;
use App\Services\LandingPageTemplateEngine;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Symfony\Component\HttpFoundation\StreamedResponse;

class LandingPageGenerator extends Component
{
    public ?Project $project = null;
    
    public string $selectedTheme = 'modern';
    
    public array $validationResults = [];
    
    protected LandingPageTemplateEngine $templateEngine;

    public function mount(Project $project)
    {
        $this->project = $project;
        $this->initializeTemplateEngine();
        $this->validateProjectContent();
    }

    /**
     * Initialize or get the template engine instance
     */
    protected function initializeTemplateEngine(): void
    {
        if (!isset($this->templateEngine)) {
            $this->templateEngine = app(LandingPageTemplateEngine::class);
        }
    }

    /**
     * Get the template engine instance, initializing if necessary
     */
    protected function getTemplateEngine(): LandingPageTemplateEngine
    {
        $this->initializeTemplateEngine();
        return $this->templateEngine;
    }

    /**
     * Get available themes
     */
    public function getAvailableThemesProperty(): array
    {
        return $this->getTemplateEngine()->getAvailableThemes();
    }

    /**
     * Validate if project has enough content for landing page generation
     */
    public function validateProjectContent(): void
    {
        $this->validationResults = $this->getTemplateEngine()->validateProjectContent($this->project);
    }

    /**
     * Change the selected theme
     */
    public function changeTheme(string $theme): void
    {
        $availableThemes = $this->getAvailableThemesProperty();
        
        if (!isset($availableThemes[$theme])) {
            return;
        }
        
        $this->selectedTheme = $theme;
        
        // Dispatch event for frontend feedback
        $this->dispatch('theme-changed', [
            'theme' => $theme,
            'themeName' => $availableThemes[$theme]['name']
        ]);
    }

    /**
     * Open preview in new tab
     */
    public function openPreviewInNewTab(): void
    {
        $previewUrl = route('landing-page.preview', [
            'project' => $this->project->id,
            'theme' => $this->selectedTheme
        ]);

        // Log the URL for debugging
        Log::info('Opening preview in new tab', [
            'url' => $previewUrl,
            'project_id' => $this->project->id,
            'theme' => $this->selectedTheme
        ]);

        // Escape the URL for JavaScript
        $escapedUrl = addslashes($previewUrl);

        // Use JavaScript to open the URL with console logging for debugging
        $this->js("
            console.log('Preview URL:', '$escapedUrl');
            console.log('About to open preview tab...');
            var newWindow = window.open('$escapedUrl', '_blank', 'noopener,noreferrer');
            console.log('Window.open result:', newWindow);
            if (!newWindow) {
                console.error('Failed to open new window - popup blocked?');
                alert('Failed to open preview. Please check if popups are blocked and try again.');
            }
        ");
        
        // Also dispatch an event as fallback
        $this->dispatch('open-preview-url', ['url' => $previewUrl]);
    }

    /**
     * Export landing page as HTML file
     */
    public function exportLandingPage(): StreamedResponse
    {
        try {
            $html = $this->getTemplateEngine()->generateLandingPage($this->project, $this->selectedTheme);
            
            // Get project name for filename
            $projectName = $this->project->getGeneratedContent('startup_naming')?->content_data['business_name'] ?? 
                          $this->project->getGeneratedContent('startup_naming')?->content_data['name'] ?? 
                          'landing-page';
            
            $filename = $this->sanitizeFilename($projectName) . '-' . $this->selectedTheme . '-landing-page.html';
            
            Log::info('Landing page exported', [
                'project_id' => $this->project->id,
                'theme' => $this->selectedTheme,
                'filename' => $filename,
            ]);
            
            return response()->streamDownload(function () use ($html) {
                echo $html;
            }, $filename, [
                'Content-Type' => 'text/html',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);
            
        } catch (\Exception $e) {
            $this->dispatch('export-error', [
                'message' => 'Failed to export landing page: ' . $e->getMessage()
            ]);
            
            Log::error('Failed to export landing page', [
                'project_id' => $this->project->id,
                'theme' => $this->selectedTheme,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Get content summary for the current project
     */
    public function getContentSummaryProperty(): array
    {
        $summary = [
            'lean_canvas' => [],
            'storytelling' => [],
            'critical_hypotheses' => [],
            'interview_questionnaire' => false,
        ];
        
        // Check Lean Canvas sections
        $leanCanvasSections = [
            'problem', 'solution', 'unique_value_proposition', 'customer_segments',
            'existing_alternatives', 'key_metrics', 'channels', 'unfair_advantage',
            'cost_structure', 'revenue_streams'
        ];
        
        foreach ($leanCanvasSections as $section) {
            $summary['lean_canvas'][$section] = $this->project->hasGeneratedContent("lean_canvas_{$section}");
        }
        
        // Check Storytelling content
        $storytellingTypes = ['brand_wheel', 'startup_naming', 'elevator_pitch'];
        foreach ($storytellingTypes as $type) {
            $summary['storytelling'][$type] = $this->project->hasGeneratedContent($type);
        }
        
        // Check Critical Hypotheses
        $hypothesesTypes = ['desirability', 'viability', 'feasibility'];
        foreach ($hypothesesTypes as $type) {
            $summary['critical_hypotheses'][$type] = $this->project->hasGeneratedContent("critical_hypothesis_{$type}");
        }
        
        // Check Interview Questionnaire
        $summary['interview_questionnaire'] = $this->project->hasGeneratedContent('interview_questionnaire');
        
        return $summary;
    }

    /**
     * Refresh validation results
     */
    public function refreshValidation(): void
    {
        $this->validateProjectContent();
        
        $this->dispatch('validation-refreshed', [
            'isValid' => $this->validationResults['isValid'],
            'errorCount' => count($this->validationResults['errors']),
            'warningCount' => count($this->validationResults['warnings']),
        ]);
    }

    /**
     * Sanitize filename for safe download
     */
    protected function sanitizeFilename(string $filename): string
    {
        // Remove or replace invalid characters
        $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '-', $filename);
        // Remove multiple consecutive dashes
        $filename = preg_replace('/-+/', '-', $filename);
        // Remove leading/trailing dashes
        $filename = trim($filename, '-');
        // Ensure it's not empty
        return empty($filename) ? 'landing-page' : $filename;
    }

    /**
     * Check if the project has enough content for landing page generation
     */
    public function getCanGenerateProperty(): bool
    {
        return $this->validationResults['isValid'] ?? false;
    }

    /**
     * Get completion percentage for landing page content
     */
    public function getCompletionPercentageProperty(): int
    {
        $summary = $this->contentSummary;
        $totalSections = 0;
        $completedSections = 0;
        
        // Count Lean Canvas sections (higher weight)
        foreach ($summary['lean_canvas'] as $hasContent) {
            $totalSections += 2; // Higher weight for lean canvas
            if ($hasContent) {
                $completedSections += 2;
            }
        }
        
        // Count Storytelling sections
        foreach ($summary['storytelling'] as $hasContent) {
            $totalSections += 1;
            if ($hasContent) {
                $completedSections += 1;
            }
        }
        
        // Count Critical Hypotheses
        foreach ($summary['critical_hypotheses'] as $hasContent) {
            $totalSections += 1;
            if ($hasContent) {
                $completedSections += 1;
            }
        }
        
        // Count Interview Questionnaire
        $totalSections += 1;
        if ($summary['interview_questionnaire']) {
            $completedSections += 1;
        }
        
        return $totalSections > 0 ? round(($completedSections / $totalSections) * 100) : 0;
    }

    public function render()
    {
        return view('livewire.landing-page-generator');
    }
} 