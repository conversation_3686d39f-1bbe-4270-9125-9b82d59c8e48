<?php

namespace Database\Seeders;

use App\Models\GeneratedContent;
use App\Models\Project;
use Illuminate\Database\Seeder;

class GeneratedContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get projects that should have generated content
        $projects = Project::whereIn('status', ['completed', 'processing'])->get();

        if ($projects->isEmpty()) {
            $this->command->warn('No projects found. Please run ProjectSeeder first.');

            return;
        }

        // Sample Lean Canvas content for different project types
        $leanCanvasContent = [
            'EcoTrack' => [
                'problem' => "Climate change awareness is growing, but individuals struggle to understand and track their personal environmental impact. Most people want to reduce their carbon footprint but don't know where to start or how to measure their progress. Existing solutions are either too complex, inaccurate, or don't provide actionable insights.",
                'solution' => 'A user-friendly mobile app that automatically tracks carbon footprint through connected devices and manual input. Provides personalized recommendations, gamification elements, and connects users with local eco-friendly alternatives. Uses AI to suggest the most impactful changes based on individual lifestyle patterns.',
                'unique_value_proposition' => 'The first carbon tracking app that makes environmental responsibility effortless and engaging through automation, personalization, and local community connections.',
                'unfair_advantage' => 'Proprietary algorithm that accurately calculates carbon footprint from minimal user input, partnerships with local green businesses, and a growing database of regional environmental impact data.',
                'customer_segments' => 'Environmentally conscious millennials and Gen Z (ages 25-40), middle to upper-middle class, tech-savvy individuals who want to make a positive environmental impact but need guidance and motivation.',
                'existing_alternatives' => 'Manual carbon calculators, general environmental apps like JouleBug, corporate sustainability platforms, and government environmental websites. Most are either too complex or lack personalization.',
                'key_metrics' => 'Monthly active users, carbon footprint reduction per user, local business partnerships, user retention rate, and average session time. Success measured by actual environmental impact achieved.',
                'channels' => 'Social media marketing (Instagram, TikTok), partnerships with environmental organizations, app store optimization, influencer collaborations, and word-of-mouth referrals from satisfied users.',
                'cost_structure' => 'App development and maintenance, cloud infrastructure, data partnerships, marketing and user acquisition, customer support, and ongoing feature development. Focus on scalable technology solutions.',
                'revenue_streams' => 'Freemium model with premium features, partnerships with eco-friendly businesses (referral fees), sponsored content from sustainable brands, and optional carbon offset marketplace integration.',
            ],
            'SkillSwap' => [
                'problem' => "Professional development is expensive and often irrelevant to real-world needs. Traditional education doesn't keep pace with rapidly changing skill requirements. Many professionals have valuable knowledge but no platform to monetize it while learning new skills themselves.",
                'solution' => 'A peer-to-peer platform where professionals exchange skills directly. Users offer expertise in exchange for learning opportunities, creating a barter system for knowledge. Includes video sessions, progress tracking, and skill verification through project-based assessments.',
                'unique_value_proposition' => 'Learn any skill for free by teaching what you already know - the first true skill bartering platform for professionals.',
                'unfair_advantage' => 'Network effects create value - the more users, the more diverse skills available. Proprietary matching algorithm and skill verification system that builds trust and ensures quality exchanges.',
                'customer_segments' => 'Working professionals (ages 25-45) in tech, marketing, design, and business roles who want to upskill but prefer practical, peer-to-peer learning over traditional courses.',
                'existing_alternatives' => 'Online courses (Udemy, Coursera), professional coaching, mentorship platforms, and traditional education. Most require significant financial investment without guaranteed practical application.',
                'key_metrics' => 'Successful skill exchanges completed, user satisfaction scores, skill verification completion rates, platform engagement time, and career advancement outcomes for users.',
                'channels' => 'LinkedIn marketing, professional community partnerships, content marketing through skill-sharing success stories, referral programs, and integration with professional development platforms.',
                'cost_structure' => 'Platform development and maintenance, video infrastructure, user verification systems, customer support, marketing, and community management. Technology-focused with minimal physical overhead.',
                'revenue_streams' => 'Premium memberships for advanced features, skill verification certificates, corporate partnerships for team skill development, and optional paid expert sessions for specialized skills.',
            ],
            'LocalFresh' => [
                'problem' => 'Consumers want fresh, local, sustainable food but struggle to find and access local farmers. Farmers need direct market access to improve margins. Current food distribution systems are inefficient, environmentally harmful, and disconnect consumers from food sources.',
                'solution' => 'A subscription platform connecting consumers directly with local farmers and food producers. Weekly curated boxes of seasonal, local produce with farmer stories, recipes, and sustainability impact tracking. Includes flexible subscription options and add-on artisanal products.',
                'unique_value_proposition' => "The most convenient way to eat locally and sustainably while directly supporting your community's farmers and food producers.",
                'unfair_advantage' => 'Direct relationships with local farmers, proprietary logistics network for efficient local delivery, and deep understanding of regional food systems and seasonal availability patterns.',
                'customer_segments' => 'Health-conscious families and individuals (ages 30-55) with disposable income who value sustainability, local community support, and high-quality food. Primarily suburban and urban areas.',
                'existing_alternatives' => 'Grocery stores, farmers markets, other CSA programs, meal kit services like Blue Apron, and online grocery delivery. Most lack the local focus and farmer connection.',
                'key_metrics' => 'Subscriber retention rate, average order value, farmer partner satisfaction, delivery efficiency, customer acquisition cost, and measurable environmental impact (food miles reduced).',
                'channels' => 'Local community events, social media marketing, partnerships with environmental organizations, referral programs, and collaborations with local restaurants and cafes.',
                'cost_structure' => 'Logistics and delivery, platform technology, farmer relationship management, packaging materials, customer acquisition, and inventory management. Focus on efficient local distribution.',
                'revenue_streams' => 'Subscription fees, markup on artisanal add-on products, delivery fees, and potential partnerships with local restaurants for bulk orders. Premium subscription tiers with additional services.',
            ],
        ];

        $contentCreated = 0;

        foreach ($projects as $project) {
            /** @var \App\Models\Project $project */
            // Determine which content template to use based on project prompt
            $contentTemplate = null;
            if (str_contains($project->input_prompt, 'EcoTrack')) {
                $contentTemplate = $leanCanvasContent['EcoTrack'];
            } elseif (str_contains($project->input_prompt, 'SkillSwap')) {
                $contentTemplate = $leanCanvasContent['SkillSwap'];
            } elseif (str_contains($project->input_prompt, 'LocalFresh')) {
                $contentTemplate = $leanCanvasContent['LocalFresh'];
            }

            // Create Problem section for all projects with templates
            if ($contentTemplate) {
                GeneratedContent::firstOrCreate(
                    [
                        'project_id' => $project->id,
                        'content_type' => 'lean_canvas_problem'
                    ],
                    [
                        'content_data' => [
                            'problem' => $contentTemplate['problem'],
                            'generated_at' => now()->subDays(rand(1, 30))->toISOString(),
                            'model_used' => 'claude-3-sonnet',
                            'token_count' => strlen($contentTemplate['problem']) / 4, // Rough estimate
                        ],
                    ]
                );
                $contentCreated++;

                // For completed projects, add more sections
                if ($project->status === 'completed') {
                    $sections = [
                        'lean_canvas_solution' => ['solution' => $contentTemplate['solution']],
                        'lean_canvas_uvp' => ['unique_value_proposition' => $contentTemplate['unique_value_proposition']],
                        'lean_canvas_advantage' => ['unfair_advantage' => $contentTemplate['unfair_advantage']],
                        'lean_canvas_customers' => ['customer_segments' => $contentTemplate['customer_segments']],
                        'lean_canvas_alternatives' => ['existing_alternatives' => $contentTemplate['existing_alternatives']],
                        'lean_canvas_metrics' => ['key_metrics' => $contentTemplate['key_metrics']],
                        'lean_canvas_channels' => ['channels' => $contentTemplate['channels']],
                        'lean_canvas_costs' => ['cost_structure' => $contentTemplate['cost_structure']],
                        'lean_canvas_revenue' => ['revenue_streams' => $contentTemplate['revenue_streams']],
                    ];

                    foreach ($sections as $contentType => $data) {
                        GeneratedContent::firstOrCreate(
                            [
                                'project_id' => $project->id,
                                'content_type' => $contentType
                            ],
                            [
                                'content_data' => array_merge($data, [
                                    'generated_at' => now()->subDays(rand(1, 25))->toISOString(),
                                    'model_used' => 'claude-3-sonnet',
                                    'token_count' => strlen(array_values($data)[0]) / 4,
                                ]),
                            ]
                        );
                        $contentCreated++;
                    }
                }
            } else {
                // For projects without specific templates, create generic problem content
                $genericProblem = 'This startup idea addresses a significant market need that requires further analysis and validation. The problem space involves understanding customer pain points, market dynamics, and competitive landscape to develop a viable solution.';

                GeneratedContent::firstOrCreate(
                    [
                        'project_id' => $project->id,
                        'content_type' => 'lean_canvas_problem'
                    ],
                    [
                        'content_data' => [
                            'problem' => $genericProblem,
                            'generated_at' => now()->subDays(rand(1, 15))->toISOString(),
                            'model_used' => 'claude-3-sonnet',
                            'token_count' => strlen($genericProblem) / 4,
                        ],
                    ]
                );
                $contentCreated++;
            }
        }

        $this->command->info("Created {$contentCreated} generated content items for {$projects->count()} projects");
    }
}
