<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('market_research_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('industry');
            $table->string('region');
            $table->json('market_attractiveness')->nullable();
            $table->json('market_size')->nullable();
            $table->json('opportunity_zones')->nullable();
            $table->json('research_scope')->nullable();
            $table->json('strategic_implications')->nullable();
            $table->json('customer_pain_points')->nullable();
            $table->json('competitive_landscape')->nullable();
            $table->json('enablers_barriers')->nullable();
            $table->json('swot_analysis')->nullable();
            $table->timestamp('generated_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index(['industry', 'region']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('market_research_sessions');
    }
};
