<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $project_id
 * @property string $content_type
 * @property array $content_data
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @method static GeneratedContent create(array $attributes = [])
 */
class GeneratedContent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'project_id',
        'content_type',
        'content_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'content_data' => 'array',
    ];

    /**
     * Get the project that owns the generated content.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the content data for a specific key.
     */
    public function getContentValue(string $key): mixed
    {
        return $this->content_data[$key] ?? null;
    }

    /**
     * Set a specific content value.
     */
    public function setContentValue(string $key, mixed $value): void
    {
        $data = $this->content_data;
        $data[$key] = $value;
        $this->content_data = $data;
    }

    /**
     * Check if content exists for a specific key.
     */
    public function hasContentValue(string $key): bool
    {
        return isset($this->content_data[$key]);
    }
}
