# Setup Guide

This guide will walk you through setting up the Venture Discovery Platform for development and production environments.

## 📋 Prerequisites

### System Requirements
- **PHP 8.2 or higher**
- **Composer 2.0+**
- **Node.js 18+ and NPM**
- **MySQL 8.0+ or PostgreSQL 13+**
- **Redis** (optional, for caching and queues)

### Required PHP Extensions
```bash
php -m | grep -E "(openssl|pdo|mbstring|tokenizer|xml|ctype|json|bcmath|curl|fileinfo|gd)"
```

### Development Tools
- **Git** for version control
- **Docker** (optional, for containerized development)
- **IDE/Editor** with PHP support (VS Code, PhpStorm, etc.)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url> venture-discovery
cd venture-discovery
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Database Setup
```bash
# Create database (MySQL example)
mysql -u root -p -e "CREATE DATABASE venture_discovery;"

# Run migrations
php artisan migrate

# Seed database (optional)
php artisan db:seed
```

### 5. Build Assets
```bash
# Build frontend assets
npm run build

# Or for development with hot reload
npm run dev
```

### 6. Start Development Server
```bash
# Start all services (recommended)
composer dev

# Or start individually:
# php artisan serve
# php artisan queue:work
# npm run dev
```

## ⚙️ Detailed Configuration

### Environment Variables

Create and configure your `.env` file:

```env
# Application
APP_NAME="Venture Discovery"
APP_ENV=local
APP_KEY=base64:your-generated-key
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=venture_discovery
DB_USERNAME=your_username
DB_PASSWORD=your_password

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORGANIZATION=your_organization_id

# Queue Configuration
QUEUE_CONNECTION=database
# For Redis: QUEUE_CONNECTION=redis

# Cache Configuration
CACHE_STORE=database
# For Redis: CACHE_STORE=redis

# Session Configuration
SESSION_DRIVER=database
SESSION_LIFETIME=120

# Broadcasting (for real-time features)
BROADCAST_CONNECTION=reverb
REVERB_APP_ID=your_app_id
REVERB_APP_KEY=your_app_key
REVERB_APP_SECRET=your_app_secret
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http

# Mail Configuration
MAIL_MAILER=log
# For production, configure SMTP:
# MAIL_MAILER=smtp
# MAIL_HOST=your_smtp_host
# MAIL_PORT=587
# MAIL_USERNAME=your_email
# MAIL_PASSWORD=your_password
# MAIL_ENCRYPTION=tls

# Logging
LOG_CHANNEL=stack
LOG_STACK=single
LOG_LEVEL=debug
```

### Database Configuration

#### MySQL Setup
```sql
-- Create database
CREATE DATABASE venture_discovery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'venture_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON venture_discovery.* TO 'venture_user'@'localhost';
FLUSH PRIVILEGES;
```

#### PostgreSQL Setup
```sql
-- Create database
CREATE DATABASE venture_discovery;

-- Create user (optional)
CREATE USER venture_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE venture_discovery TO venture_user;
```

### OpenAI API Configuration

1. **Get API Key**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Set Environment Variable**: Add `OPENAI_API_KEY` to your `.env` file
3. **Configure Organization** (optional): Add `OPENAI_ORGANIZATION` if using organization account

### Redis Configuration (Optional)

For better performance with caching and queues:

```bash
# Install Redis
# Ubuntu/Debian:
sudo apt install redis-server

# macOS:
brew install redis

# Start Redis
redis-server
```

Update `.env`:
```env
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
CACHE_STORE=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
```

## 🐳 Docker Development Setup

### Using Laravel Sail

```bash
# Install Sail
composer require laravel/sail --dev

# Publish Sail configuration
php artisan sail:install

# Start containers
./vendor/bin/sail up -d

# Run commands through Sail
./vendor/bin/sail artisan migrate
./vendor/bin/sail npm install
./vendor/bin/sail npm run dev
```

### Custom Docker Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - .:/var/www/html
    environment:
      - APP_ENV=local
    depends_on:
      - database
      - redis

  database:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: venture_discovery
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

## 🔧 Development Tools Setup

### Code Quality Tools

```bash
# Format code
composer format

# Run static analysis
composer check

# Run tests
composer test

# Run all quality checks
composer full
```

### IDE Configuration

#### VS Code Extensions
- PHP Intelephense
- Laravel Extension Pack
- Tailwind CSS IntelliSense
- GitLens

#### PhpStorm Configuration
- Laravel Plugin
- PHP Annotations
- Tailwind CSS Support

## 🚀 Production Deployment

### Server Requirements
- **PHP 8.2+ with FPM**
- **Nginx or Apache**
- **MySQL/PostgreSQL**
- **Redis** (recommended)
- **Supervisor** (for queue workers)

### Production Environment Setup

```bash
# Clone repository
git clone <repository-url> /var/www/venture-discovery
cd /var/www/venture-discovery

# Install dependencies (production)
composer install --no-dev --optimize-autoloader

# Set permissions
sudo chown -R www-data:www-data /var/www/venture-discovery
sudo chmod -R 755 /var/www/venture-discovery
sudo chmod -R 775 /var/www/venture-discovery/storage
sudo chmod -R 775 /var/www/venture-discovery/bootstrap/cache

# Environment configuration
cp .env.example .env
# Edit .env with production values

# Generate key and cache config
php artisan key:generate
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run migrations
php artisan migrate --force

# Build assets
npm ci --production
npm run build
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/venture-discovery/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### Supervisor Configuration

Create `/etc/supervisor/conf.d/venture-discovery.conf`:

```ini
[program:venture-discovery-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/venture-discovery/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/venture-discovery/storage/logs/worker.log
stopwaitsecs=3600
```

### SSL Configuration

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔍 Troubleshooting

### Common Issues

#### Permission Errors
```bash
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

#### Database Connection Issues
```bash
# Check database service
sudo systemctl status mysql

# Test connection
php artisan tinker
>>> DB::connection()->getPdo();
```

#### Queue Not Processing
```bash
# Check queue status
php artisan queue:work --once

# Restart queue workers
sudo supervisorctl restart venture-discovery-worker:*
```

#### Asset Build Issues
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### Performance Optimization

```bash
# Optimize for production
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Clear caches if needed
php artisan optimize:clear
```

### Monitoring and Logs

```bash
# View application logs
tail -f storage/logs/laravel.log

# Monitor queue jobs
php artisan queue:monitor

# Check system resources
htop
df -h
```

## 📚 Next Steps

After successful setup:

1. **Read the [Architecture Guide](./ARCHITECTURE.md)** to understand the system design
2. **Review [Database Documentation](./DATABASE.md)** for data structure
3. **Check [API Documentation](./API.md)** for integration details
4. **Follow [Contributing Guidelines](./CONTRIBUTING.md)** for development workflow 