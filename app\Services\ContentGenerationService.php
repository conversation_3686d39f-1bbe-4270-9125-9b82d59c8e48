<?php

namespace App\Services;

use App\Models\GeneratedContent;
use App\Models\Project;
use Exception;
use Illuminate\Support\Facades\Log;

class ContentGenerationService
{
    protected OpenAiService $openAiService;

    protected PromptEngineeringService $promptService;

    public function __construct(OpenAiService $openAiService, PromptEngineeringService $promptService)
    {
        $this->openAiService = $openAiService;
        $this->promptService = $promptService;
    }

    /**
     * Generate the Problem section for a Lean Canvas
     *
     * @throws Exception
     */
    public function generateProblemSection(Project $project): GeneratedContent
    {
        // Check if content already exists
        $existingContent = $project->getGeneratedContent('lean_canvas_problem');
        if ($existingContent) {
            Log::info('Problem section already exists for project', ['project_id' => $project->id]);

            return $existingContent;
        }

        try {
            $prompt = $this->buildProblemSectionPrompt($project->input_prompt);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating problem section', [
                'project_id' => $project->id,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateContent(
                prompt: $prompt,
                systemPrompt: $systemPrompt,
                options: [
                    'max_tokens' => 500, // Limit for problem section
                    'temperature' => 0.7, // Balanced creativity
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => 'lean_canvas_problem',
                'content_data' => [
                    'problem' => $content,
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ],
            ]);

            Log::info('Problem section generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
                'content_length' => strlen($content),
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate problem section', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate problem section: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Generate all Lean Canvas sections for a project
     *
     * @return array<string, GeneratedContent>
     *
     * @throws Exception
     */
    public function generateLeanCanvas(Project $project): array
    {
        return $this->generateFullLeanCanvas($project);
    }

    /**
     * Generate all Lean Canvas sections for a project with comprehensive error handling
     *
     * @return array<string, GeneratedContent>
     *
     * @throws Exception
     */
    public function generateFullLeanCanvas(Project $project): array
    {
        // Validate input
        if (! $project->id) {
            throw new Exception('Invalid project provided for Lean Canvas generation');
        }

        if (empty(trim($project->input_prompt))) {
            throw new Exception('Project input prompt is required for Lean Canvas generation');
        }

        Log::info('Starting full Lean Canvas generation', [
            'project_id' => $project->id,
            'prompt_length' => strlen($project->input_prompt),
        ]);

        $sections = [
            'problem' => 'lean_canvas_problem',
            'solution' => 'lean_canvas_solution',
            'unique_value_proposition' => 'lean_canvas_unique_value_proposition',
            'unfair_advantage' => 'lean_canvas_unfair_advantage',
            'customer_segments' => 'lean_canvas_customer_segments',
            'existing_alternatives' => 'lean_canvas_existing_alternatives',
            'key_metrics' => 'lean_canvas_key_metrics',
            'channels' => 'lean_canvas_channels',
            'cost_structure' => 'lean_canvas_cost_structure',
            'revenue_streams' => 'lean_canvas_revenue_streams',
        ];

        $generatedContents = [];
        $errors = [];
        $successCount = 0;

        foreach ($sections as $sectionKey => $contentType) {
            try {
                // Check if content already exists
                $existingContent = $project->getGeneratedContent($contentType);
                if ($existingContent) {
                    Log::info('Section already exists, skipping generation', [
                        'project_id' => $project->id,
                        'section' => $sectionKey,
                        'content_type' => $contentType,
                    ]);
                    $generatedContents[$sectionKey] = $existingContent;
                    $successCount++;

                    continue;
                }

                // Generate the section using the appropriate method
                $generatedContent = match ($sectionKey) {
                    'problem' => $this->generateProblemSection($project),
                    'solution' => $this->generateSolutionSection($project),
                    'unique_value_proposition' => $this->generateUniqueValuePropositionSection($project),
                    'unfair_advantage' => $this->generateUnfairAdvantageSection($project),
                    'customer_segments' => $this->generateCustomerSegmentsSection($project),
                    'existing_alternatives' => $this->generateExistingAlternativesSection($project),
                    'key_metrics' => $this->generateKeyMetricsSection($project),
                    'channels' => $this->generateChannelsSection($project),
                    'cost_structure' => $this->generateCostStructureSection($project),
                    'revenue_streams' => $this->generateRevenueStreamsSection($project),
                };

                $generatedContents[$sectionKey] = $generatedContent;
                $successCount++;

                Log::info('Section generated successfully', [
                    'project_id' => $project->id,
                    'section' => $sectionKey,
                    'content_id' => $generatedContent->id,
                ]);

                // Add a small delay between API calls to avoid rate limiting
                usleep(100000); // 100ms delay

            } catch (Exception $e) {
                $errorMessage = "Failed to generate {$sectionKey} section: {$e->getMessage()}";
                $errors[$sectionKey] = $errorMessage;

                Log::error('Section generation failed', [
                    'project_id' => $project->id,
                    'section' => $sectionKey,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // Continue with other sections instead of failing completely
                continue;
            }
        }

        Log::info('Full Lean Canvas generation completed', [
            'project_id' => $project->id,
            'success_count' => $successCount,
            'total_sections' => count($sections),
            'errors' => $errors,
        ]);

        // If no sections were generated successfully, throw an exception
        if ($successCount === 0) {
            throw new Exception('Failed to generate any Lean Canvas sections. Errors: '.implode('; ', $errors));
        }

        // If some sections failed, log a warning but return the successful ones
        if (! empty($errors)) {
            Log::warning('Some Lean Canvas sections failed to generate', [
                'project_id' => $project->id,
                'failed_sections' => array_keys($errors),
                'errors' => $errors,
            ]);
        }

        return $generatedContents;
    }

    /**
     * Generate all missing Lean Canvas sections for a project
     *
     * @return array<string, GeneratedContent>
     *
     * @throws Exception
     */
    public function generateMissingLeanCanvasSections(Project $project): array
    {
        $allSections = [
            'problem' => 'lean_canvas_problem',
            'solution' => 'lean_canvas_solution',
            'unique_value_proposition' => 'lean_canvas_unique_value_proposition',
            'unfair_advantage' => 'lean_canvas_unfair_advantage',
            'customer_segments' => 'lean_canvas_customer_segments',
            'existing_alternatives' => 'lean_canvas_existing_alternatives',
            'key_metrics' => 'lean_canvas_key_metrics',
            'channels' => 'lean_canvas_channels',
            'cost_structure' => 'lean_canvas_cost_structure',
            'revenue_streams' => 'lean_canvas_revenue_streams',
        ];

        $missingSections = [];

        foreach ($allSections as $sectionKey => $contentType) {
            if (! $project->hasGeneratedContent($contentType)) {
                $missingSections[$sectionKey] = $contentType;
            }
        }

        if (empty($missingSections)) {
            Log::info('All Lean Canvas sections already exist for project', [
                'project_id' => $project->id,
            ]);

            return $this->getGeneratedContentByType($project);
        }

        Log::info('Generating missing Lean Canvas sections', [
            'project_id' => $project->id,
            'missing_sections' => array_keys($missingSections),
        ]);

        $generatedContents = [];
        $errors = [];

        foreach ($missingSections as $sectionKey => $contentType) {
            try {
                $generatedContent = match ($sectionKey) {
                    'problem' => $this->generateProblemSection($project),
                    'solution' => $this->generateSolutionSection($project),
                    'unique_value_proposition' => $this->generateUniqueValuePropositionSection($project),
                    'unfair_advantage' => $this->generateUnfairAdvantageSection($project),
                    'customer_segments' => $this->generateCustomerSegmentsSection($project),
                    'existing_alternatives' => $this->generateExistingAlternativesSection($project),
                    'key_metrics' => $this->generateKeyMetricsSection($project),
                    'channels' => $this->generateChannelsSection($project),
                    'cost_structure' => $this->generateCostStructureSection($project),
                    'revenue_streams' => $this->generateRevenueStreamsSection($project),
                };

                $generatedContents[$sectionKey] = $generatedContent;

                // Add delay between API calls
                usleep(100000);

            } catch (Exception $e) {
                $errors[$sectionKey] = $e->getMessage();
                Log::error('Failed to generate missing section', [
                    'project_id' => $project->id,
                    'section' => $sectionKey,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Include existing sections in the result
        $existingContents = $this->getGeneratedContentByType($project);
        $generatedContents = array_merge($existingContents, $generatedContents);

        return $generatedContents;
    }

    /**
     * Get Lean Canvas completion status
     *
     * @return array{completed: int, total: int, missing: array<string>, percentage: float}
     */
    public function getLeanCanvasCompletionStatus(Project $project): array
    {
        $allSections = [
            'problem' => 'lean_canvas_problem',
            'solution' => 'lean_canvas_solution',
            'unique_value_proposition' => 'lean_canvas_unique_value_proposition',
            'unfair_advantage' => 'lean_canvas_unfair_advantage',
            'customer_segments' => 'lean_canvas_customer_segments',
            'existing_alternatives' => 'lean_canvas_existing_alternatives',
            'key_metrics' => 'lean_canvas_key_metrics',
            'channels' => 'lean_canvas_channels',
            'cost_structure' => 'lean_canvas_cost_structure',
            'revenue_streams' => 'lean_canvas_revenue_streams',
        ];

        $completed = 0;
        $missing = [];

        foreach ($allSections as $sectionKey => $contentType) {
            if ($project->hasGeneratedContent($contentType)) {
                $completed++;
            } else {
                $missing[] = $sectionKey;
            }
        }

        $total = count($allSections);
        $percentage = ($completed / $total) * 100;

        return [
            'completed' => $completed,
            'total' => $total,
            'missing' => $missing,
            'percentage' => round($percentage, 1),
        ];
    }

    /**
     * Check if a project has any generated content
     */
    public function hasGeneratedContent(Project $project): bool
    {
        return $project->generatedContents()->exists();
    }

    /**
     * Get all generated content for a project grouped by type
     *
     * @return array<string, GeneratedContent>
     */
    public function getGeneratedContentByType(Project $project): array
    {
        $contents = $project->generatedContents()->get();
        $grouped = [];

        foreach ($contents as $content) {
            /** @var GeneratedContent $content */
            $grouped[$content->content_type] = $content;
        }

        return $grouped;
    }

    /**
     * Generate a specific Lean Canvas section
     *
     * @throws Exception
     */
    public function generateLeanCanvasSection(Project $project, string $contentType, string $sectionTitle): GeneratedContent
    {
        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Lean Canvas section already exists for project', [
                'project_id' => $project->id,
                'content_type' => $contentType,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->buildSectionPrompt($project->input_prompt, $sectionTitle, $contentType);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating Lean Canvas section', [
                'project_id' => $project->id,
                'content_type' => $contentType,
                'section_title' => $sectionTitle,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateContent(
                prompt: $prompt,
                systemPrompt: $systemPrompt,
                options: [
                    'max_tokens' => 500,
                    'temperature' => 0.7,
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => [
                    $this->getContentKey($contentType) => $content,
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ],
            ]);

            Log::info('Lean Canvas section generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
                'content_type' => $contentType,
                'content_length' => strlen($content),
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate Lean Canvas section', [
                'project_id' => $project->id,
                'content_type' => $contentType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate {$sectionTitle} section: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Regenerate a specific content section
     *
     * @throws Exception
     */
    public function regenerateContent(Project $project, string $contentType): GeneratedContent
    {
        // Delete existing content
        $project->generatedContents()
            ->where('content_type', $contentType)
            ->delete();

        // Generate new content based on type
        return match ($contentType) {
            'lean_canvas_problem' => $this->generateProblemSection($project),
            'lean_canvas_solution' => $this->generateSolutionSection($project),
            'lean_canvas_unique_value_proposition' => $this->generateUniqueValuePropositionSection($project),
            'lean_canvas_customer_segments' => $this->generateCustomerSegmentsSection($project),
            'lean_canvas_existing_alternatives' => $this->generateExistingAlternativesSection($project),
            'lean_canvas_key_metrics' => $this->generateKeyMetricsSection($project),
            'lean_canvas_channels' => $this->generateChannelsSection($project),
            'lean_canvas_unfair_advantage' => $this->generateUnfairAdvantageSection($project),
            'lean_canvas_cost_structure' => $this->generateCostStructureSection($project),
            'lean_canvas_revenue_streams' => $this->generateRevenueStreamsSection($project),
            'critical_hypothesis_desirability' => $this->generateCriticalHypothesis($project, 'desirability'),
            'critical_hypothesis_viability' => $this->generateCriticalHypothesis($project, 'viability'),
            'critical_hypothesis_feasibility' => $this->generateCriticalHypothesis($project, 'feasibility'),
            default => throw new Exception("Unknown content type: {$contentType}"),
        };
    }

    /**
     * Generate the Solution section for a Lean Canvas
     */
    public function generateSolutionSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_solution',
            'Solution'
        );
    }

    /**
     * Generate the Unique Value Proposition section for a Lean Canvas
     */
    public function generateUniqueValuePropositionSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_unique_value_proposition',
            'Unique Value Proposition'
        );
    }

    /**
     * Generate the Customer Segments section for a Lean Canvas
     */
    public function generateCustomerSegmentsSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_customer_segments',
            'Customer Segments'
        );
    }

    /**
     * Generate the Existing Alternatives section for a Lean Canvas
     */
    public function generateExistingAlternativesSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_existing_alternatives',
            'Existing Alternatives'
        );
    }

    /**
     * Generate the Key Metrics section for a Lean Canvas
     */
    public function generateKeyMetricsSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_key_metrics',
            'Key Metrics'
        );
    }

    /**
     * Generate the Channels section for a Lean Canvas
     */
    public function generateChannelsSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_channels',
            'Channels'
        );
    }

    /**
     * Generate the Unfair Advantage section for a Lean Canvas
     */
    public function generateUnfairAdvantageSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_unfair_advantage',
            'Unfair Advantage'
        );
    }

    /**
     * Generate the Cost Structure section for a Lean Canvas
     */
    public function generateCostStructureSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_cost_structure',
            'Cost Structure'
        );
    }

    /**
     * Generate the Revenue Streams section for a Lean Canvas
     */
    public function generateRevenueStreamsSection(Project $project): GeneratedContent
    {
        return $this->generateLeanCanvasSection(
            $project,
            'lean_canvas_revenue_streams',
            'Revenue Streams'
        );
    }

    /**
     * Build a specific prompt for the Problem section
     */
    protected function buildProblemSectionPrompt(string $startupIdea): string
    {
        return "Based on this startup idea: \"{$startupIdea}\"\n\n".
            'Identify and describe the top 3 most critical problems this startup solves. '.
            "For each problem:\n".
            "1. Clearly state the problem\n".
            "2. Explain why it's a significant pain point for customers\n".
            "3. Describe the current alternatives or workarounds people use\n".
            "4. Quantify the impact or frequency of the problem if possible\n\n".
            "Format your response as a clear, concise list that would fit in the 'Problem' section of a Lean Canvas. ".
            "Focus on customer pain points that are urgent, pervasive, and underserved by existing solutions.\n\n".
            'Keep the response under 300 words and make it actionable for business planning.';
    }

    /**
     * Build a prompt for any Lean Canvas section
     */
    protected function buildSectionPrompt(string $startupIdea, string $sectionTitle, string $contentType): string
    {
        $sectionPrompts = [
            'lean_canvas_solution' => "Based on this startup idea: \"{$startupIdea}\"\n\nDescribe the top 3 features or solutions that address the main problems. Focus on the core functionality that makes this startup unique and valuable. Keep it concise and actionable for a Lean Canvas.",
            'lean_canvas_unique_value_proposition' => "Based on this startup idea: \"{$startupIdea}\"\n\nCreate a single, clear unique value proposition that explains why this startup is different and worth paying attention to. This should be a compelling one-liner that captures the essence of the value delivered.",
            'lean_canvas_unfair_advantage' => "Based on this startup idea: \"{$startupIdea}\"\n\nIdentify the unfair advantage - something that cannot be easily copied or bought by competitors. This could be insider information, expert endorsements, personal authority, large network effects, community, existing customers, or SEO ranking.",
            'lean_canvas_customer_segments' => "Based on this startup idea: \"{$startupIdea}\"\n\nDefine the target customer segments. Who are the early adopters? Describe their characteristics, behaviors, and why they would be most likely to use this solution. Be specific about demographics and psychographics.",
            'lean_canvas_existing_alternatives' => "Based on this startup idea: \"{$startupIdea}\"\n\nList the existing alternatives and how people currently solve these problems today. Include direct competitors, indirect competitors, and manual/workaround solutions that people currently use.",
            'lean_canvas_key_metrics' => "Based on this startup idea: \"{$startupIdea}\"\n\nIdentify the key metrics that will tell you how the business is performing. Focus on actionable metrics that drive business decisions, not vanity metrics. Include both leading and lagging indicators.",
            'lean_canvas_channels' => "Based on this startup idea: \"{$startupIdea}\"\n\nDescribe the path to customers - how will you reach and acquire your target customers? Include both free and paid channels, and focus on scalable acquisition methods.",
            'lean_canvas_cost_structure' => "Based on this startup idea: \"{$startupIdea}\"\n\nOutline the main cost structure including customer acquisition costs, development costs, operational expenses, and any other significant costs required to run this business.",
            'lean_canvas_revenue_streams' => "Based on this startup idea: \"{$startupIdea}\"\n\nDescribe the revenue model - how will this business make money? Include pricing strategy, revenue streams, and estimated customer lifetime value. Be specific about the monetization approach.",
        ];

        return $sectionPrompts[$contentType] ?? "Based on this startup idea: \"{$startupIdea}\"\n\nGenerate content for the {$sectionTitle} section of a Lean Canvas. Keep it concise, actionable, and business-focused.";
    }

    /**
     * Get the appropriate content key for storing in content_data
     */
    protected function getContentKey(string $contentType): string
    {
        $keyMap = [
            'lean_canvas_problem' => 'problem',
            'lean_canvas_solution' => 'solution',
            'lean_canvas_unique_value_proposition' => 'unique_value_proposition',
            'lean_canvas_unfair_advantage' => 'unfair_advantage',
            'lean_canvas_customer_segments' => 'customer_segments',
            'lean_canvas_existing_alternatives' => 'existing_alternatives',
            'lean_canvas_key_metrics' => 'key_metrics',
            'lean_canvas_channels' => 'channels',
            'lean_canvas_cost_structure' => 'cost_structure',
            'lean_canvas_revenue_streams' => 'revenue_streams',
        ];

        return $keyMap[$contentType] ?? 'content';
    }

    /**
     * Generate Critical Hypotheses for a project (Desirability, Viability, Feasibility)
     *
     * @return array<string, GeneratedContent>
     *
     * @throws Exception
     */
    public function generateCriticalHypotheses(Project $project): array
    {
        // Validate input
        if (! $project->id) {
            throw new Exception('Invalid project provided for Critical Hypotheses generation');
        }

        if (empty(trim($project->input_prompt))) {
            throw new Exception('Project input prompt is required for Critical Hypotheses generation');
        }

        Log::info('Starting Critical Hypotheses generation', [
            'project_id' => $project->id,
            'prompt_length' => strlen($project->input_prompt),
        ]);

        $hypothesisTypes = ['desirability', 'viability', 'feasibility'];
        $generatedContents = [];
        $errors = [];
        $successCount = 0;

        foreach ($hypothesisTypes as $type) {
            try {
                $contentType = "critical_hypothesis_{$type}";

                // Check if content already exists
                $existingContent = $project->getGeneratedContent($contentType);
                if ($existingContent) {
                    Log::info('Critical hypothesis already exists, skipping generation', [
                        'project_id' => $project->id,
                        'hypothesis_type' => $type,
                        'content_type' => $contentType,
                    ]);
                    $generatedContents[$type] = $existingContent;
                    $successCount++;

                    continue;
                }

                $prompt = $this->buildCriticalHypothesisPrompt($project->input_prompt, $type);
                $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

                Log::info('Generating critical hypothesis', [
                    'project_id' => $project->id,
                    'hypothesis_type' => $type,
                    'prompt_length' => strlen($prompt),
                ]);

                // Use structured content generation for consistent format
                $content = $this->openAiService->generateStructuredContent(
                    $prompt,
                    [
                        'hypothesis' => 'string',
                        'criticality' => 'string', // High, Medium, Low
                        'testing_method' => 'string',
                        'success_criteria' => 'string',
                        'risk_level' => 'string', // High, Medium, Low
                    ]
                );

                // Create and save the generated content
                $generatedContent = GeneratedContent::create([
                    'project_id' => $project->id,
                    'content_type' => $contentType,
                    'content_data' => array_merge($content, [
                        'hypothesis_type' => $type,
                        'generated_at' => now()->toISOString(),
                        'prompt_used' => $prompt,
                    ]),
                ]);

                $generatedContents[$type] = $generatedContent;
                $successCount++;

                Log::info('Critical hypothesis generated successfully', [
                    'project_id' => $project->id,
                    'hypothesis_type' => $type,
                    'content_id' => $generatedContent->id,
                ]);

                // Add a small delay between API calls to avoid rate limiting
                usleep(100000); // 100ms delay

            } catch (Exception $e) {
                $errorMessage = "Failed to generate {$type} hypothesis: {$e->getMessage()}";
                $errors[$type] = $errorMessage;

                Log::error('Critical hypothesis generation failed', [
                    'project_id' => $project->id,
                    'hypothesis_type' => $type,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // Continue with other hypotheses instead of failing completely
                continue;
            }
        }

        Log::info('Critical Hypotheses generation completed', [
            'project_id' => $project->id,
            'success_count' => $successCount,
            'total_hypotheses' => count($hypothesisTypes),
            'errors' => $errors,
        ]);

        // If no hypotheses were generated successfully, throw an exception
        if ($successCount === 0) {
            throw new Exception('Failed to generate any Critical Hypotheses. Errors: '.implode('; ', $errors));
        }

        // If some hypotheses failed, log a warning but return the successful ones
        if (! empty($errors)) {
            Log::warning('Some Critical Hypotheses failed to generate', [
                'project_id' => $project->id,
                'failed_hypotheses' => array_keys($errors),
                'errors' => $errors,
            ]);
        }

        return $generatedContents;
    }

    /**
     * Generate a specific Critical Hypothesis
     *
     * @throws Exception
     */
    public function generateCriticalHypothesis(Project $project, string $hypothesisType): GeneratedContent
    {
        if (! in_array($hypothesisType, ['desirability', 'viability', 'feasibility'])) {
            throw new Exception("Invalid hypothesis type: {$hypothesisType}");
        }

        $contentType = "critical_hypothesis_{$hypothesisType}";

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Critical hypothesis already exists for project', [
                'project_id' => $project->id,
                'hypothesis_type' => $hypothesisType,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->buildCriticalHypothesisPrompt($project->input_prompt, $hypothesisType);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating single critical hypothesis', [
                'project_id' => $project->id,
                'hypothesis_type' => $hypothesisType,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'hypothesis' => 'string',
                    'criticality' => 'string',
                    'testing_method' => 'string',
                    'success_criteria' => 'string',
                    'risk_level' => 'string',
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'hypothesis_type' => $hypothesisType,
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ]),
            ]);

            Log::info('Critical hypothesis generated successfully', [
                'project_id' => $project->id,
                'hypothesis_type' => $hypothesisType,
                'content_id' => $generatedContent->id,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate critical hypothesis', [
                'project_id' => $project->id,
                'hypothesis_type' => $hypothesisType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate {$hypothesisType} hypothesis: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Check if a project has all critical hypotheses generated
     */
    public function hasCriticalHypotheses(Project $project): bool
    {
        $hypothesisTypes = ['desirability', 'viability', 'feasibility'];

        foreach ($hypothesisTypes as $type) {
            $contentType = "critical_hypothesis_{$type}";
            if (! $project->hasGeneratedContent($contentType)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all critical hypotheses for a project
     *
     * @return array<string, GeneratedContent>
     */
    public function getCriticalHypotheses(Project $project): array
    {
        $hypothesisTypes = ['desirability', 'viability', 'feasibility'];
        $hypotheses = [];

        foreach ($hypothesisTypes as $type) {
            $contentType = "critical_hypothesis_{$type}";
            $content = $project->getGeneratedContent($contentType);
            if ($content) {
                $hypotheses[$type] = $content;
            }
        }

        return $hypotheses;
    }

    /**
     * Regenerate a specific critical hypothesis
     *
     * @throws Exception
     */
    public function regenerateCriticalHypothesis(Project $project, string $hypothesisType): GeneratedContent
    {
        if (! in_array($hypothesisType, ['desirability', 'viability', 'feasibility'])) {
            throw new Exception("Invalid hypothesis type: {$hypothesisType}");
        }

        $contentType = "critical_hypothesis_{$hypothesisType}";

        // Delete existing content
        $project->generatedContents()
            ->where('content_type', $contentType)
            ->delete();

        // Generate new content
        return $this->generateCriticalHypothesis($project, $hypothesisType);
    }

    /**
     * Build a prompt for generating a critical hypothesis
     */
    protected function buildCriticalHypothesisPrompt(string $startupIdea, string $hypothesisType): string
    {
        $basePrompt = "Based on this startup idea: \"{$startupIdea}\"\n\n";

        $typeSpecificPrompts = [
            'desirability' => $basePrompt.
                "Generate a critical DESIRABILITY hypothesis that tests whether customers actually want this solution.\n\n".
                "Focus on:\n".
                "- Customer demand and willingness to pay\n".
                "- Problem-solution fit\n".
                "- User engagement and adoption\n".
                "- Market need validation\n\n".
                'The hypothesis should be specific, testable, and measurable. Include what success looks like and how to test it.',

            'viability' => $basePrompt.
                "Generate a critical VIABILITY hypothesis that tests whether this business can be profitable and sustainable.\n\n".
                "Focus on:\n".
                "- Revenue model and pricing\n".
                "- Cost structure and unit economics\n".
                "- Market size and growth potential\n".
                "- Competitive advantage and defensibility\n\n".
                'The hypothesis should address financial sustainability and long-term business viability.',

            'feasibility' => $basePrompt.
                "Generate a critical FEASIBILITY hypothesis that tests whether this solution can actually be built and delivered.\n\n".
                "Focus on:\n".
                "- Technical implementation challenges\n".
                "- Resource requirements and constraints\n".
                "- Operational complexity and scalability\n".
                "- Team capabilities and expertise\n\n".
                'The hypothesis should address the practical aspects of building and scaling the solution.',
        ];

        return $typeSpecificPrompts[$hypothesisType] ?? $basePrompt."Generate a critical hypothesis for {$hypothesisType}.";
    }

    /**
     * Generate an interview questionnaire for customer validation
     *
     * @throws Exception
     */
    public function generateInterviewQuestionnaire(Project $project): GeneratedContent
    {
        $contentType = 'interview_questionnaire';

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Interview questionnaire already exists for project', [
                'project_id' => $project->id,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->promptService->buildInterviewQuestionnairePrompt($project->input_prompt);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating interview questionnaire', [
                'project_id' => $project->id,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'introduction' => 'string',
                    'questions' => 'array',
                    'conclusion' => 'string',
                ]
            );

            // Calculate total question count
            $questionCount = 0;
            if (isset($content['questions']) && is_array($content['questions'])) {
                foreach ($content['questions'] as $section) {
                    if (isset($section['questions']) && is_array($section['questions'])) {
                        $questionCount += count($section['questions']);
                    }
                }
            }

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                    'question_count' => $questionCount,
                ]),
            ]);

            Log::info('Interview questionnaire generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
                'question_count' => $generatedContent->content_data['question_count'] ?? 0,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate interview questionnaire', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate interview questionnaire: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Check if a project has an interview questionnaire generated
     */
    public function hasInterviewQuestionnaire(Project $project): bool
    {
        return $project->hasGeneratedContent('interview_questionnaire');
    }

    /**
     * Get the interview questionnaire for a project
     */
    public function getInterviewQuestionnaire(Project $project): ?GeneratedContent
    {
        return $project->getGeneratedContent('interview_questionnaire');
    }

    /**
     * Regenerate the interview questionnaire for a project
     *
     * @throws Exception
     */
    public function regenerateInterviewQuestionnaire(Project $project): GeneratedContent
    {
        // Delete existing content
        $project->generatedContents()
            ->where('content_type', 'interview_questionnaire')
            ->delete();

        // Generate new content
        return $this->generateInterviewQuestionnaire($project);
    }

    /**
     * Generate a Brand Wheel for storytelling central
     *
     * @throws Exception
     */
    public function generateBrandWheel(Project $project): GeneratedContent
    {
        $contentType = 'brand_wheel';

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Brand wheel already exists for project', [
                'project_id' => $project->id,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->promptService->buildBrandWheelPrompt($project->input_prompt);
            $systemPrompt = $this->promptService->buildSystemPrompt('brand_strategist');

            Log::info('Generating brand wheel', [
                'project_id' => $project->id,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'mission' => 'string',
                    'vision' => 'string',
                    'values' => 'array',
                    'personality' => 'string',
                    'tone_of_voice' => 'string',
                    'brand_promise' => 'string',
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ]),
            ]);

            Log::info('Brand wheel generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate brand wheel', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate brand wheel: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Generate startup naming suggestions
     *
     * @throws Exception
     */
    public function generateStartupNaming(Project $project): GeneratedContent
    {
        $contentType = 'startup_naming';

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Startup naming already exists for project', [
                'project_id' => $project->id,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->promptService->buildStartupNamingPrompt($project->input_prompt);
            $systemPrompt = $this->promptService->buildSystemPrompt('brand_strategist');

            Log::info('Generating startup naming suggestions', [
                'project_id' => $project->id,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'suggestions' => 'array',
                    'rationales' => 'array',
                    'domain_availability' => 'array',
                    'trademark_considerations' => 'array',
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                    'suggestion_count' => count($content['suggestions'] ?? []),
                ]),
            ]);

            Log::info('Startup naming generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
                'suggestion_count' => $generatedContent->content_data['suggestion_count'] ?? 0,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate startup naming', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate startup naming: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Generate an elevator pitch
     *
     * @throws Exception
     */
    public function generateElevatorPitch(Project $project): GeneratedContent
    {
        $contentType = 'elevator_pitch';

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Elevator pitch already exists for project', [
                'project_id' => $project->id,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->promptService->buildElevatorPitchPrompt($project->input_prompt);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating elevator pitch', [
                'project_id' => $project->id,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'pitch_30_seconds' => 'string',
                    'pitch_60_seconds' => 'string',
                    'pitch_90_seconds' => 'string',
                    'key_points' => 'array',
                    'call_to_action' => 'string',
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ]),
            ]);

            Log::info('Elevator pitch generated successfully', [
                'project_id' => $project->id,
                'content_id' => $generatedContent->id,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate elevator pitch', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate elevator pitch: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Check if a project has storytelling content generated
     */
    public function hasStorytellingContent(Project $project): bool
    {
        $contentTypes = ['brand_wheel', 'startup_naming', 'elevator_pitch'];

        foreach ($contentTypes as $contentType) {
            if (! $project->hasGeneratedContent($contentType)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all storytelling content for a project
     *
     * @return array<string, GeneratedContent>
     */
    public function getStorytellingContent(Project $project): array
    {
        $contentTypes = ['brand_wheel', 'startup_naming', 'elevator_pitch'];
        $content = [];

        foreach ($contentTypes as $contentType) {
            $generatedContent = $project->getGeneratedContent($contentType);
            if ($generatedContent) {
                $content[$contentType] = $generatedContent;
            }
        }

        return $content;
    }

    /**
     * Generate all storytelling content for a project
     *
     * @return array<string, GeneratedContent>
     *
     * @throws Exception
     */
    public function generateAllStorytellingContent(Project $project): array
    {
        $content = [];

        try {
            $content['brand_wheel'] = $this->generateBrandWheel($project);
            $content['startup_naming'] = $this->generateStartupNaming($project);
            $content['elevator_pitch'] = $this->generateElevatorPitch($project);

            Log::info('All storytelling content generated successfully', [
                'project_id' => $project->id,
                'content_types' => array_keys($content),
            ]);

            return $content;
        } catch (Exception $e) {
            Log::error('Failed to generate all storytelling content', [
                'project_id' => $project->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Regenerate specific storytelling content
     *
     * @throws Exception
     */
    public function regenerateStorytellingContent(Project $project, string $contentType): GeneratedContent
    {
        if (! in_array($contentType, ['brand_wheel', 'startup_naming', 'elevator_pitch'])) {
            throw new Exception("Invalid storytelling content type: {$contentType}");
        }

        // Delete existing content
        $project->generatedContents()
            ->where('content_type', $contentType)
            ->delete();

        // Generate new content
        return match ($contentType) {
            'brand_wheel' => $this->generateBrandWheel($project),
            'startup_naming' => $this->generateStartupNaming($project),
            'elevator_pitch' => $this->generateElevatorPitch($project),
        };
    }

    /**
     * Generate all Market Sizing Analysis content for a project
     *
     * @return array<string, GeneratedContent>
     * @throws Exception
     */
    public function generateMarketSizingAnalysis(Project $project): array
    {
        // Validate input
        if (! $project->id) {
            throw new Exception('Invalid project provided for Market Sizing Analysis generation');
        }

        if (empty(trim($project->input_prompt))) {
            throw new Exception('Project input prompt is required for Market Sizing Analysis generation');
        }

        Log::info('Starting Market Sizing Analysis generation', [
            'project_id' => $project->id,
            'prompt_length' => strlen($project->input_prompt),
        ]);

        $analysisTypes = ['tam', 'sam', 'som'];
        $generatedContents = [];
        $errors = [];
        $successCount = 0;

        foreach ($analysisTypes as $type) {
            try {
                $contentType = "market_sizing_{$type}";

                // Check if content already exists
                $existingContent = $project->getGeneratedContent($contentType);
                if ($existingContent) {
                    Log::info('Market sizing analysis already exists, skipping generation', [
                        'project_id' => $project->id,
                        'analysis_type' => $type,
                        'content_type' => $contentType,
                    ]);
                    $generatedContents[$type] = $existingContent;
                    $successCount++;

                    continue;
                }

                $prompt = $this->buildMarketSizingPrompt($project->input_prompt, $type);
                $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

                Log::info('Generating market sizing analysis', [
                    'project_id' => $project->id,
                    'analysis_type' => $type,
                    'prompt_length' => strlen($prompt),
                ]);

                // Use structured content generation for consistent format
                $content = $this->openAiService->generateStructuredContent(
                    $prompt,
                    [
                        'market_size_value' => 'string',
                        'market_size_currency' => 'string',
                        'calculation_method' => 'string',
                        'data_sources' => 'array',
                        'assumptions' => 'array',
                        'growth_rate' => 'string',
                        'time_frame' => 'string',
                        'confidence_level' => 'string',
                        'key_insights' => 'array',
                        'market_description' => 'string',
                    ]
                );

                // Create and save the generated content
                $generatedContent = GeneratedContent::create([
                    'project_id' => $project->id,
                    'content_type' => $contentType,
                    'content_data' => array_merge($content, [
                        'analysis_type' => $type,
                        'generated_at' => now()->toISOString(),
                        'prompt_used' => $prompt,
                    ]),
                ]);

                $generatedContents[$type] = $generatedContent;
                $successCount++;

                Log::info('Market sizing analysis generated successfully', [
                    'project_id' => $project->id,
                    'analysis_type' => $type,
                    'content_id' => $generatedContent->id,
                ]);

                // Add a small delay between API calls to avoid rate limiting
                usleep(100000); // 100ms delay

            } catch (Exception $e) {
                $errorMessage = "Failed to generate {$type} analysis: {$e->getMessage()}";
                $errors[$type] = $errorMessage;

                Log::error('Market sizing analysis generation failed', [
                    'project_id' => $project->id,
                    'analysis_type' => $type,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // Continue with other analyses instead of failing completely
                continue;
            }
        }

        Log::info('Market Sizing Analysis generation completed', [
            'project_id' => $project->id,
            'success_count' => $successCount,
            'total_analyses' => count($analysisTypes),
            'errors' => $errors,
        ]);

        // If no analyses were generated successfully, throw an exception
        if ($successCount === 0) {
            throw new Exception('Failed to generate any Market Sizing Analysis. Errors: ' . implode('; ', $errors));
        }

        // If some analyses failed, log a warning but return the successful ones
        if (! empty($errors)) {
            Log::warning('Some Market Sizing Analyses failed to generate', [
                'project_id' => $project->id,
                'failed_analyses' => array_keys($errors),
                'errors' => $errors,
            ]);
        }

        return $generatedContents;
    }

    /**
     * Generate a specific Market Sizing Analysis (TAM, SAM, or SOM)
     *
     * @throws Exception
     */
    public function generateMarketSizingAnalysisType(Project $project, string $analysisType): GeneratedContent
    {
        if (! in_array($analysisType, ['tam', 'sam', 'som'])) {
            throw new Exception("Invalid market sizing analysis type: {$analysisType}");
        }

        $contentType = "market_sizing_{$analysisType}";

        // Check if content already exists
        $existingContent = $project->getGeneratedContent($contentType);
        if ($existingContent) {
            Log::info('Market sizing analysis already exists for project', [
                'project_id' => $project->id,
                'analysis_type' => $analysisType,
            ]);

            return $existingContent;
        }

        try {
            $prompt = $this->buildMarketSizingPrompt($project->input_prompt, $analysisType);
            $systemPrompt = $this->promptService->buildSystemPrompt('business_strategist');

            Log::info('Generating single market sizing analysis', [
                'project_id' => $project->id,
                'analysis_type' => $analysisType,
                'prompt_length' => strlen($prompt),
            ]);

            $content = $this->openAiService->generateStructuredContent(
                $prompt,
                [
                    'market_size_value' => 'string',
                    'market_size_currency' => 'string',
                    'calculation_method' => 'string',
                    'data_sources' => 'array',
                    'assumptions' => 'array',
                    'growth_rate' => 'string',
                    'time_frame' => 'string',
                    'confidence_level' => 'string',
                    'key_insights' => 'array',
                    'market_description' => 'string',
                ]
            );

            // Create and save the generated content
            $generatedContent = GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => $contentType,
                'content_data' => array_merge($content, [
                    'analysis_type' => $analysisType,
                    'generated_at' => now()->toISOString(),
                    'prompt_used' => $prompt,
                ]),
            ]);

            Log::info('Market sizing analysis generated successfully', [
                'project_id' => $project->id,
                'analysis_type' => $analysisType,
                'content_id' => $generatedContent->id,
            ]);

            return $generatedContent;
        } catch (Exception $e) {
            Log::error('Failed to generate market sizing analysis', [
                'project_id' => $project->id,
                'analysis_type' => $analysisType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new Exception("Failed to generate {$analysisType} analysis: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Check if a project has all market sizing analyses generated
     */
    public function hasMarketSizingAnalysis(Project $project): bool
    {
        $requiredTypes = ['market_sizing_tam', 'market_sizing_sam', 'market_sizing_som'];
        
        foreach ($requiredTypes as $contentType) {
            if (! $project->hasGeneratedContent($contentType)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get all market sizing analyses for a project
     *
     * @return array<string, GeneratedContent|null>
     */
    public function getMarketSizingAnalysis(Project $project): array
    {
        return [
            'tam' => $project->getGeneratedContent('market_sizing_tam'),
            'sam' => $project->getGeneratedContent('market_sizing_sam'),
            'som' => $project->getGeneratedContent('market_sizing_som'),
        ];
    }

    /**
     * Regenerate a specific market sizing analysis
     *
     * @throws Exception
     */
    public function regenerateMarketSizingAnalysis(Project $project, string $analysisType): GeneratedContent
    {
        if (! in_array($analysisType, ['tam', 'sam', 'som'])) {
            throw new Exception("Invalid market sizing analysis type: {$analysisType}");
        }

        $contentType = "market_sizing_{$analysisType}";

        // Delete existing content
        $project->generatedContents()
            ->where('content_type', $contentType)
            ->delete();

        // Generate new content
        return $this->generateMarketSizingAnalysisType($project, $analysisType);
    }

    /**
     * Build the prompt for market sizing analysis
     */
    protected function buildMarketSizingPrompt(string $startupIdea, string $analysisType): string
    {
        $basePrompt = "Based on this startup idea: \"{$startupIdea}\"\n\n";

        $exampleSection = "REFERENCE EXAMPLE (Food Delivery App):\n" .
            "• TAM: All food spending globally (\$8 trillion)\n" .
            "• SAM: Online food delivery in target cities (\$50 billion)\n" .
            "• SOM: Market share realistically capturable (1% = \$500 million)\n\n";

        $typeSpecificPrompts = [
            'tam' => $basePrompt . $exampleSection .
                "Generate a comprehensive TAM (Total Addressable Market) analysis.\n\n" .
                "TAM represents the total revenue opportunity available if the product achieved 100% market share.\n\n" .
                "Focus on:\n" .
                "- Global market size for the entire category (like 'all food spending globally')\n" .
                "- All potential customers who could benefit from this solution\n" .
                "- Multiple market research sources and industry reports\n" .
                "- Top-down and bottom-up calculation approaches\n" .
                "- Historical growth trends and future projections\n" .
                "- Market expansion opportunities and related segments\n\n" .
                "Provide realistic estimates with clear methodology, data sources, and assumptions.\n" .
                "Format market_size_value as a clear number with unit (e.g., '\$8 trillion', '\$500 billion', '\$50 million').",

            'sam' => $basePrompt . $exampleSection .
                "Generate a detailed SAM (Serviceable Addressable Market) analysis.\n\n" .
                "SAM represents the portion of TAM that your business model and go-to-market strategy can realistically target.\n\n" .
                "Focus on:\n" .
                "- Geographic markets you can serve effectively (like 'target cities' vs global)\n" .
                "- Customer segments that fit your value proposition\n" .
                "- Channels and distribution limitations\n" .
                "- Regulatory and compliance constraints\n" .
                "- Language, cultural, and localization requirements\n" .
                "- Competitive landscape and market accessibility\n\n" .
                "Consider practical business limitations and strategic focus areas.\n" .
                "Format market_size_value as a clear number with unit (e.g., '\$50 billion', '\$10 billion').",

            'som' => $basePrompt . $exampleSection .
                "Generate a realistic SOM (Serviceable Obtainable Market) analysis.\n\n" .
                "SOM represents the portion of SAM that you can realistically capture given your resources, competition, and market dynamics.\n\n" .
                "Focus on:\n" .
                "- Realistic market share projections (typically 0.1-10% of SAM, like the 1% example)\n" .
                "- Competitive advantage and differentiation\n" .
                "- Customer acquisition capabilities and costs\n" .
                "- Resource constraints and scaling limitations\n" .
                "- Time to market and adoption rates\n" .
                "- Network effects and viral growth potential\n" .
                "- Market penetration strategies and milestones\n\n" .
                "Provide conservative, realistic, and optimistic scenarios with clear justification.\n" .
                "Express as percentage of SAM and dollar amount (e.g., '1% of SAM = \$500 million').\n" .
                "Format market_size_value as a clear number with unit (e.g., '\$500 million', '\$50 million').",
        ];

        return $typeSpecificPrompts[$analysisType] ?? $basePrompt . "Generate a market sizing analysis for {$analysisType}.";
    }
}
