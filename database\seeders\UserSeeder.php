<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
            ]
        );

        // Create additional admin users for testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
            ]
        );

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
            ]
        );

        // Create demo admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Demo Admin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
            ]
        );

        // Create additional random admin users using factory
        User::factory(3)->create();

        $this->command->info('Created 7 admin users (4 specific + 3 random)');
    }
}
