<?php

namespace App\Jobs;

use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateStorytellingContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Project $project;

    public bool $regenerate;

    public ?string $contentType;

    /**
     * Create a new job instance.
     */
    public function __construct(Project $project, bool $regenerate = false, ?string $contentType = null)
    {
        $this->project = $project;
        $this->regenerate = $regenerate;
        $this->contentType = $contentType;
    }

    /**
     * Execute the job.
     */
    public function handle(ContentGenerationService $contentService): void
    {
        try {
            Log::info('Starting storytelling content generation job', [
                'project_id' => $this->project->id,
                'regenerate' => $this->regenerate,
                'content_type' => $this->contentType,
            ]);

            if ($this->contentType) {
                // Generate specific content type
                $this->generateSpecificContent($contentService);
            } else {
                // Generate all storytelling content
                $this->generateAllContent($contentService);
            }

            Log::info('Storytelling content generation job completed successfully', [
                'project_id' => $this->project->id,
                'content_type' => $this->contentType ?? 'all',
            ]);
        } catch (\Exception $e) {
            Log::error('Storytelling content generation job failed', [
                'project_id' => $this->project->id,
                'content_type' => $this->contentType ?? 'all',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Generate specific content type.
     */
    private function generateSpecificContent(ContentGenerationService $contentService): void
    {
        if ($this->regenerate) {
            $contentService->regenerateStorytellingContent($this->project, $this->contentType);
        } else {
            match ($this->contentType) {
                'brand_wheel' => $this->generateIfNotExists($contentService, 'generateBrandWheel'),
                'startup_naming' => $this->generateIfNotExists($contentService, 'generateStartupNaming'),
                'elevator_pitch' => $this->generateIfNotExists($contentService, 'generateElevatorPitch'),
                default => throw new \InvalidArgumentException("Invalid content type: {$this->contentType}"),
            };
        }
    }

    /**
     * Generate all storytelling content.
     */
    private function generateAllContent(ContentGenerationService $contentService): void
    {
        if ($this->regenerate) {
            // For regeneration, delete existing content and generate all fresh
            $contentTypes = ['brand_wheel', 'startup_naming', 'elevator_pitch'];
            foreach ($contentTypes as $contentType) {
                $contentService->regenerateStorytellingContent($this->project, $contentType);
            }
        } else {
            // Only generate missing content
            $storytellingContent = $contentService->getStorytellingContent($this->project);

            if (! isset($storytellingContent['brand_wheel'])) {
                $this->generateIfNotExists($contentService, 'generateBrandWheel');
            }

            if (! isset($storytellingContent['startup_naming'])) {
                $this->generateIfNotExists($contentService, 'generateStartupNaming');
            }

            if (! isset($storytellingContent['elevator_pitch'])) {
                $this->generateIfNotExists($contentService, 'generateElevatorPitch');
            }
        }
    }

    /**
     * Generate content if it doesn't exist.
     */
    private function generateIfNotExists(ContentGenerationService $contentService, string $method): void
    {
        try {
            $contentService->$method($this->project);
        } catch (\Exception $e) {
            Log::warning("Failed to generate content using {$method}", [
                'project_id' => $this->project->id,
                'error' => $e->getMessage(),
            ]);
            // Don't throw here to allow other content types to be generated
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'storytelling-content',
            'project:'.$this->project->id,
            $this->contentType ? "content-type:{$this->contentType}" : 'all-content',
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // Retry after 30s, 1m, 2m
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10);
    }
}
