<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property string $industry
 * @property string $region
 * @property array|null $market_attractiveness
 * @property array|null $market_size
 * @property array|null $opportunity_zones
 * @property array|null $research_scope
 * @property array|null $strategic_implications
 * @property array|null $customer_pain_points
 * @property array|null $competitive_landscape
 * @property array|null $enablers_barriers
 * @property array|null $swot_analysis
 * @property \Illuminate\Support\Carbon|null $generated_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class MarketResearchSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'industry',
        'region',
        'market_attractiveness',
        'market_size',
        'opportunity_zones',
        'research_scope',
        'strategic_implications',
        'customer_pain_points',
        'competitive_landscape',
        'enablers_barriers',
        'swot_analysis',
        'generated_at',
    ];

    protected $casts = [
        'market_attractiveness' => 'array',
        'market_size' => 'array',
        'opportunity_zones' => 'array',
        'research_scope' => 'array',
        'strategic_implications' => 'array',
        'customer_pain_points' => 'array',
        'competitive_landscape' => 'array',
        'enablers_barriers' => 'array',
        'swot_analysis' => 'array',
        'generated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the market research session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the session has complete data.
     */
    public function isComplete(): bool
    {
        return !is_null($this->market_attractiveness) &&
               !is_null($this->market_size) &&
               !is_null($this->opportunity_zones) &&
               !is_null($this->research_scope) &&
               !is_null($this->strategic_implications) &&
               !is_null($this->customer_pain_points) &&
               !is_null($this->competitive_landscape) &&
               !is_null($this->enablers_barriers) &&
               !is_null($this->swot_analysis);
    }

    /**
     * Get the completion percentage of the session.
     */
    public function getCompletionPercentage(): int
    {
        $fields = [
            'market_attractiveness',
            'market_size',
            'opportunity_zones',
            'research_scope',
            'strategic_implications',
            'customer_pain_points',
            'competitive_landscape',
            'enablers_barriers',
            'swot_analysis',
        ];

        $completedFields = 0;
        foreach ($fields as $field) {
            if (!is_null($this->$field)) {
                $completedFields++;
            }
        }

        return round(($completedFields / count($fields)) * 100);
    }

    /**
     * Accessor for completion_percentage attribute (for Filament)
     */
    public function getCompletionPercentageAttribute(): int
    {
        return $this->getCompletionPercentage();
    }

    /**
     * Formatted accessors for Filament TextEntry components
     * These ensure TextEntry receives strings instead of arrays
     */
    public function getFormattedOpportunityZonesAttribute(): string
    {
        if (is_array($this->opportunity_zones)) {
            return collect($this->opportunity_zones)->map(function ($zone) {
                if (is_array($zone)) {
                    $title = $zone['title'] ?? 'Untitled Zone';
                    $description = $zone['description'] ?? 'No description';
                    return $title . ': ' . $description;
                }
                return (string) $zone;
            })->join(' | ');
        }
        return $this->opportunity_zones ?? 'No opportunity zones available';
    }

    public function getFormattedCustomerPainPointsAttribute(): string
    {
        if (is_array($this->customer_pain_points)) {
            return collect($this->customer_pain_points)->map(function ($painPoint) {
                if (is_array($painPoint)) {
                    $title = $painPoint['title'] ?? 'Untitled Pain Point';
                    $description = $painPoint['description'] ?? 'No description';
                    return $title . ': ' . $description;
                }
                return (string) $painPoint;
            })->join(' | ');
        }
        return $this->customer_pain_points ?? 'No pain points available';
    }

    public function getFormattedResearchScopeAttribute(): string
    {
        if (is_array($this->research_scope)) {
            return collect($this->research_scope)->map(function ($value, $key) {
                if (is_array($value)) {
                    $formatted = collect($value)->map(function ($item) {
                        return is_array($item) ? json_encode($item) : (string) $item;
                    })->join(', ');
                    return $key . ': ' . $formatted;
                }
                return $key . ': ' . $value;
            })->join(' | ');
        }
        return $this->research_scope ?? 'No research scope data available';
    }

    public function getFormattedStrategicImplicationsAttribute(): string
    {
        if (is_array($this->strategic_implications)) {
            return collect($this->strategic_implications)->map(function ($item) {
                if (is_array($item) && isset($item['title'], $item['description'])) {
                    return $item['title'] . ': ' . $item['description'];
                }
                return (string) $item;
            })->join(', ');
        }
        return $this->strategic_implications ?? 'No strategic implications available';
    }

    public function getFormattedCompetitiveLandscapeAttribute(): string
    {
        if (is_array($this->competitive_landscape)) {
            return collect($this->competitive_landscape)->map(function ($competitor) {
                if (is_array($competitor)) {
                    $name = $competitor['name'] ?? 'Unknown';
                    
                    // Handle legacy structure (just name and description)
                    if (isset($competitor['description']) && !isset($competitor['marketPosition'])) {
                        $strength = $competitor['strength'] ?? $competitor['description'] ?? '';
                        $differentiation = $competitor['differentiation'] ?? '';
                        $details = collect([$strength, $differentiation])->filter()->join(' | ');
                        return $name . ': ' . ($details ?: 'No details available');
                    }
                    
                    // Handle enhanced structure with new fields
                    $marketPosition = $competitor['marketPosition'] ?? '';
                    $marketShare = $competitor['marketShare'] ?? '';
                    $description = $competitor['description'] ?? '';
                    
                    $details = collect([$marketPosition, $marketShare, $description])->filter()->join(' | ');
                    return $name . ': ' . ($details ?: 'No details available');
                }
                return (string) $competitor;
            })->join(' • ');
        }
        return $this->competitive_landscape ?? 'No competitive landscape data available';
    }

    public function getFormattedSwotAnalysisAttribute(): string
    {
        if (is_array($this->swot_analysis)) {
            return collect($this->swot_analysis)->map(function ($value, $key) {
                if (is_array($value)) {
                    $formatted = collect($value)->map(function ($item) {
                        return is_array($item) ? json_encode($item) : (string) $item;
                    })->join(', ');
                    return $key . ': ' . $formatted;
                }
                return $key . ': ' . $value;
            })->join(' | ');
        }
        return $this->swot_analysis ?? 'No SWOT analysis data available';
    }

    public function getFormattedEnablersBarriersAttribute(): string
    {
        if (is_array($this->enablers_barriers)) {
            return collect($this->enablers_barriers)->map(function ($value, $key) {
                if (is_array($value)) {
                    $formatted = collect($value)->map(function ($item) {
                        return is_array($item) ? json_encode($item) : (string) $item;
                    })->join(', ');
                    return $key . ': ' . $formatted;
                }
                return $key . ': ' . $value;
            })->join(' | ');
        }
        return $this->enablers_barriers ?? 'No enablers & barriers data available';
    }

    public function getFormattedMarketAttractivenessAttribute(): string
    {
        if (is_array($this->market_attractiveness)) {
            return collect($this->market_attractiveness)->map(function ($value, $key) {
                if (is_array($value)) {
                    $formatted = collect($value)->map(function ($item) {
                        if (is_array($item)) {
                            return collect($item)->map(fn($v, $k) => "{$k}: {$v}")->join(', ');
                        }
                        return (string) $item;
                    })->join('; ');
                    return $key . ': ' . $formatted;
                }
                return $key . ': ' . $value;
            })->join(' | ');
        }
        return $this->market_attractiveness ?? 'No market attractiveness data available';
    }

    public function getFormattedMarketSizeAttribute(): string
    {
        if (is_array($this->market_size)) {
            return collect($this->market_size)->map(function ($value, $key) {
                if (is_array($value)) {
                    return $key . ': ' . collect($value)->join(', ');
                }
                return $key . ': ' . $value;
            })->join(', ');
        }
        return $this->market_size ?? 'No market size data available';
    }
}
