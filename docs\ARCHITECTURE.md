# System Architecture

This document provides a comprehensive overview of the Venture Discovery Platform's architecture, design patterns, and code organization.

## 🏗️ High-Level Architecture

### System Overview
The Venture Discovery Platform follows a modern **Model-View-Controller (MVC)** architecture built on <PERSON>vel, with additional layers for service-oriented business logic and real-time communication.

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Filament Admin Panel  │  Livewire Components  │  Blade UI  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Application Layer                           │
├─────────────────────────────────────────────────────────────┤
│     Controllers     │    Middleware    │    Form Requests   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Service Layer                              │
├─────────────────────────────────────────────────────────────┤
│ ContentGenerationService │ OpenAiService │ PromptEngineering │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Domain Layer                              │
├─────────────────────────────────────────────────────────────┤
│      Models       │     Events      │      Jobs           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
├─────────────────────────────────────────────────────────────┤
│   Database    │   Queue System   │   External APIs        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Core Components

### 1. Authentication System

**Multi-Guard Authentication:**
The platform uses Laravel's multi-guard authentication system to separate admin and application users:

```php
// config/auth.php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'account' => [
        'driver' => 'session',
        'provider' => 'accounts',
    ],
],
```

**Guard Usage:**
- `web` guard: Admin panel users (User model)
- `account` guard: Application users (Account model)

### 2. Content Generation Pipeline

**AI Content Generation Flow:**

```
User Input → Validation → Prompt Engineering → OpenAI API → Content Processing → Storage → Event Broadcasting
```

**Key Components:**
- `ContentGenerationService`: Orchestrates content generation
- `OpenAiService`: Handles API communication with retry logic
- `PromptEngineeringService`: Builds optimized prompts
- Background Jobs: Asynchronous processing
- Events: Real-time UI updates

### 3. Real-time Communication

**WebSocket Architecture:**
```
Frontend (Livewire) ←→ Laravel Reverb ←→ Event Broadcasting ←→ Background Jobs
```

**Implementation:**
- Laravel Reverb for WebSocket server
- Private channels for user-specific updates
- Event broadcasting for real-time notifications
- Livewire integration for seamless UI updates

## 📁 Directory Structure

### Application Structure
```
app/
├── Console/
│   └── Commands/           # Artisan commands
├── Events/                 # Domain events
├── Filament/              # Filament panels and resources
│   ├── App/               # Main application panel
│   └── Resources/         # Admin panel resources
├── Http/
│   └── Controllers/       # HTTP controllers
├── Jobs/                  # Background jobs
├── Livewire/             # Livewire components
├── Models/               # Eloquent models
├── Providers/            # Service providers
└── Services/             # Business logic services
```

### Frontend Structure
```
resources/
├── css/                  # Stylesheets
├── js/                   # JavaScript files
└── views/                # Blade templates
    ├── components/       # Blade components
    ├── filament/        # Filament customizations
    └── livewire/        # Livewire component views
```

## 🗄️ Database Architecture

### Entity Relationship Overview

```
Users (Admin)
    │
    └── (No direct relationship to app data)

Accounts (App Users)
    │
    ├── Projects (1:N)
    │   │
    │   └── GeneratedContent (1:N)
    │
    └── AccountHistory (1:N)
```

### Key Models

**User Model:**
- Admin panel authentication
- Filament panel access control
- No relationship to application data

**Account Model:**
- Application user authentication
- Project ownership
- Account status management (active/blocked)

**Project Model:**
- Startup idea storage
- Status tracking (pending/processing/completed)
- Content generation orchestration

**GeneratedContent Model:**
- AI-generated content storage
- Content type categorization
- Metadata tracking (tokens, timestamps)

## 🔄 Service Architecture

### 1. ContentGenerationService

**Responsibilities:**
- Orchestrate AI content generation
- Manage content lifecycle
- Handle regeneration requests
- Track completion status

**Key Methods:**
```php
// Lean Canvas generation
generateLeanCanvasSection(Project $project, string $sectionKey)
generateFullLeanCanvas(Project $project)

// Critical Hypotheses
generateCriticalHypotheses(Project $project)
generateCriticalHypothesis(Project $project, string $type)

// Interview Tools
generateInterviewQuestionnaire(Project $project)

// Storytelling Assets
generateBrandWheel(Project $project)
generateStartupNaming(Project $project)
generateElevatorPitch(Project $project)
```

### 2. OpenAiService

**Responsibilities:**
- OpenAI API communication
- Retry logic and error handling
- Token usage tracking
- Response caching

**Features:**
- Exponential backoff retry strategy
- Rate limiting compliance
- Cost tracking and optimization
- Structured output parsing

### 3. PromptEngineeringService

**Responsibilities:**
- Build optimized prompts for different content types
- Maintain prompt templates
- Handle context injection
- Optimize for token efficiency

## 🚀 Job Architecture

### Background Job System

**Job Types:**
```php
GenerateLeanCanvasSection::class     // Individual canvas sections
GenerateCriticalHypotheses::class    // Business hypotheses
GenerateInterviewQuestionnaireJob::class  // Customer interviews
GenerateStorytellingContentJob::class     // Brand assets
```

**Job Features:**
- Automatic retry with exponential backoff
- Failed job handling and logging
- Progress tracking and status updates
- Event broadcasting on completion

### Queue Configuration

**Queue Drivers:**
- **Development**: Database driver for simplicity
- **Production**: Redis for performance
- **Testing**: Sync driver for immediate execution

## 🎨 Frontend Architecture

### Filament Panel Structure

**Admin Panel (`/admin`):**
- User management
- Account administration
- System monitoring
- Content oversight

**App Panel (`/app`):**
- Project management
- Content generation interface
- Real-time progress tracking
- Export functionality

### Livewire Components

**Core Components:**
```php
LeanCanvasDisplay::class           // Interactive canvas interface
CriticalHypothesesDisplay::class   // Hypothesis management
InterviewQuestionnaireDisplay::class // Interview tools
StorytellingCentralDisplay::class  // Brand asset hub
```

**Component Features:**
- Real-time updates via WebSocket
- Progressive enhancement
- Server-side validation
- Optimistic UI updates

## 🔐 Security Architecture

### Authentication & Authorization

**Security Layers:**
1. **Session Management**: Secure session handling
2. **CSRF Protection**: Token-based CSRF prevention
3. **Input Validation**: Comprehensive request validation
4. **SQL Injection Prevention**: Eloquent ORM protection

### Data Protection

**Security Measures:**
- Encrypted sensitive data storage
- Secure API key management
- Rate limiting on API endpoints
- XSS protection through Blade templating

### Access Control

**Permission System:**
- Account-based project isolation
- Resource-level permissions
- Admin panel access restrictions
- Multi-tenant data separation

## 📊 Performance Architecture

### Caching Strategy

**Multi-level Caching:**
```php
// Application Cache
config()->cache()           // Configuration caching
route()->cache()           // Route caching

// Database Cache
Model::remember()          // Query result caching

// Content Cache
Cache::remember()          // Generated content caching
```

### Database Optimization

**Performance Features:**
- Strategic database indexing
- Eager loading for relationships
- Query optimization to prevent N+1
- Connection pooling

### Queue Optimization

**Background Processing:**
- Asynchronous content generation
- Batch job processing
- Failed job retry logic
- Queue monitoring and alerting

## 🔄 Event System

### Domain Events

**Event Types:**
```php
LeanCanvasSectionGenerated::class  // Content generation events
ProjectStatusChanged::class        // Project lifecycle events
AccountBlocked::class             // Security events
```

**Event Broadcasting:**
- Real-time UI updates
- WebSocket communication
- Private channel security
- Event persistence for audit

## 🧪 Testing Architecture

### Test Structure

**Testing Layers:**
```
tests/
├── Feature/
│   ├── Admin/              # Admin panel functionality
│   └── App/                # Main application features
└── Unit/                   # Individual component tests
```

**Test Categories:**
- **Unit Tests**: Service and model testing
- **Feature Tests**: End-to-end functionality
- **Integration Tests**: External service interaction
- **Browser Tests**: UI and user experience

## 🚀 Deployment Architecture

### Environment Configuration

**Environment Separation:**
- **Development**: Local development with debugging
- **Staging**: Production-like testing environment
- **Production**: Optimized for performance and security

### Infrastructure Components

**Required Services:**
- **Web Server**: Nginx/Apache with PHP-FPM
- **Database**: MySQL/PostgreSQL cluster
- **Queue Worker**: Redis with Laravel Horizon
- **WebSocket**: Laravel Reverb server
- **Cache**: Redis for application caching

## 🔧 Configuration Management

### Environment Variables

**Critical Configuration:**
```env
# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=venture_discovery

# OpenAI Integration
OPENAI_API_KEY=your_api_key
OPENAI_MODEL=gpt-4

# Queue Configuration
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1

# Broadcasting
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your_app_id
```

### Service Providers

**Custom Providers:**
- `OpenAiServiceProvider`: OpenAI service configuration
- `AppPanelProvider`: Main application panel setup
- `AdminPanelProvider`: Admin panel configuration

## 📈 Monitoring & Observability

### Logging Strategy

**Log Channels:**
- **Application**: General application logs
- **OpenAI**: AI service interaction logs
- **Queue**: Background job processing logs
- **Security**: Authentication and authorization logs

### Performance Monitoring

**Metrics Tracking:**
- Response time monitoring
- Database query performance
- Queue job processing times
- OpenAI API usage and costs

## 🔄 Integration Patterns

### External Service Integration

**OpenAI Integration Pattern:**
```php
try {
    $response = $this->openAiService->generateContent($prompt);
    // Process successful response
} catch (RateLimitException $e) {
    // Handle rate limiting
    $this->retryLater();
} catch (ApiException $e) {
    // Handle API errors
    $this->logError($e);
}
```

### Event-Driven Communication

**Event Flow:**
1. User action triggers job dispatch
2. Job processes in background
3. Job completion fires event
4. Event broadcasts to frontend
5. Livewire component updates UI

This architecture ensures scalability, maintainability, and robust performance while providing a seamless user experience for startup validation and business planning.