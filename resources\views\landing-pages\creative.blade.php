<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="{{ $meta['viewport'] }}">
    <meta name="description" content="{{ $meta['description'] }}">
    <meta name="keywords" content="{{ $meta['keywords'] }}">
    <meta name="author" content="{{ $meta['author'] }}">
    <title>{{ $meta['title'] }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: #ffffff;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        /* Animated Background Elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(72, 187, 120, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(66, 153, 225, 0.1) 0%, transparent 50%);
            animation: float-shapes 15s ease-in-out infinite;
            z-index: -1;
        }
        
        @keyframes float-shapes {
            0%, 100% { transform: translateY(0px) scale(1); }
            33% { transform: translateY(-20px) scale(1.1); }
            66% { transform: translateY(10px) scale(0.9); }
        }
        
        /* Floating Elements */
        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            opacity: 0.6;
            animation: float-element 8s ease-in-out infinite;
        }
        
        .floating-element.pink {
            background: linear-gradient(45deg, #ff6b6b, #ff8e53);
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-element.blue {
            background: linear-gradient(45deg, #4299e1, #3182ce);
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .floating-element.green {
            background: linear-gradient(45deg, #48bb78, #38a169);
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }
        
        .floating-element.purple {
            background: linear-gradient(45deg, #9f7aea, #805ad5);
            bottom: 20%;
            right: 10%;
            animation-delay: 6s;
        }
        
        @keyframes float-element {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
            75% { transform: translateY(-30px) rotate(270deg); }
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid #ff6b6b;
            z-index: 1000;
            padding: 16px 0;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4299e1, #48bb78);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-decoration: none;
            animation: rainbow-pulse 3s ease-in-out infinite;
        }
        
        @keyframes rainbow-pulse {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(180deg); }
        }
        
        /* Hero Section */
        .hero {
            padding: 140px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="white" opacity="0.4"><animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/></circle><circle cx="60" cy="70" r="1" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/></circle></svg>') repeat;
            animation: sparkle 10s linear infinite;
        }
        
        @keyframes sparkle {
            0% { background-position: 0 0; }
            100% { background-position: 100px 100px; }
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
        }
        
        .hero h1 {
            font-size: 4rem;
            margin-bottom: 24px;
            font-weight: 900;
            line-height: 1.1;
            animation: bounce-in 1s ease-out;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        @keyframes bounce-in {
            0% { transform: scale(0.5) translateY(50px); opacity: 0; }
            60% { transform: scale(1.1) translateY(-10px); opacity: 1; }
            100% { transform: scale(1) translateY(0); opacity: 1; }
        }
        
        .hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.95;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fade-up 1s ease-out 0.3s both;
        }
        
        @keyframes fade-up {
            0% { transform: translateY(30px); opacity: 0; }
            100% { transform: translateY(0); opacity: 0.95; }
        }
        
        /* Button Styles */
        .btn {
            display: inline-block;
            padding: 18px 36px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
        }
        
        .btn-secondary {
            background: transparent;
            border: 3px solid #ffffff;
            color: #ffffff;
            margin-left: 20px;
            box-shadow: none;
        }
        
        .btn-secondary:hover {
            background: #ffffff;
            color: #ff6b6b;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(255, 255, 255, 0.4);
        }
        
        /* Section Styles */
        .section {
            padding: 120px 0;
            position: relative;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .section h2 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4299e1);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .section-subtitle {
            font-size: 1.3rem;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* Problem & Solution */
        .problem-solution {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            position: relative;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }
        
        .column {
            background: rgba(255, 255, 255, 0.9);
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .column::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }
        
        .column:hover {
            transform: translateY(-10px);
        }
        
        .column h3 {
            font-size: 2rem;
            margin-bottom: 24px;
            color: #2d3748;
            font-weight: 700;
        }
        
        .column p {
            font-size: 1.1rem;
            color: #4a5568;
            line-height: 1.8;
        }
        
        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            margin-top: 80px;
        }
        
        .feature-card {
            background: white;
            padding: 50px;
            border-radius: 25px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(66, 153, 225, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .feature-card:hover::before {
            opacity: 1;
        }
        
        .feature-card:hover {
            transform: translateY(-15px) rotate(2deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            position: relative;
            z-index: 1;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .feature-card h3 {
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 700;
            font-size: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .feature-card p {
            color: #718096;
            position: relative;
            z-index: 1;
            font-size: 1rem;
            line-height: 1.6;
        }
        
        /* Stats Section */
        .stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }
        
        .stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,10 90,90 10,90" fill="white" opacity="0.03"/></svg>') repeat;
        }
        
        .stats-content {
            position: relative;
            z-index: 1;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 50px;
            text-align: center;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        
        .stat-item:hover {
            transform: scale(1.05);
        }
        
        .stat-item h3 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            font-weight: 900;
            animation: count-up 2s ease-out;
        }
        
        @keyframes count-up {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .stat-item p {
            font-size: 1.2rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }
        
        /* CTA Section */
        .cta {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cta::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .cta-content {
            position: relative;
            z-index: 1;
        }
        
        .cta h2 {
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 3rem;
            font-weight: 900;
        }
        
        .cta p {
            font-size: 1.3rem;
            margin-bottom: 50px;
            color: #4a5568;
            font-weight: 500;
        }
        
        /* Footer */
        .footer {
            background: #2d3748;
            color: white;
            padding: 80px 0 50px;
            text-align: center;
            position: relative;
        }
        
        .footer p {
            opacity: 0.8;
            font-size: 1rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .section h2 {
                font-size: 2.2rem;
            }
            
            .btn-secondary {
                margin-left: 0;
                margin-top: 20px;
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }
            
            .container {
                padding: 0 16px;
            }
            
            .floating-element {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Elements -->
    <div class="floating-element pink"></div>
    <div class="floating-element blue"></div>
    <div class="floating-element green"></div>
    <div class="floating-element purple"></div>

    <!-- Header -->
    <header class="header">
        <nav class="container">
            <div class="nav">
                <a href="#" class="logo">
                    {{ $content['storytelling']['startup_naming']['business_name'] ?? $content['storytelling']['startup_naming']['name'] ?? $meta['title'] }}
                </a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>{{ $content['storytelling']['startup_naming']['tagline'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'Create. Inspire. Innovate.' }}</h1>
                <p class="subtitle">
                    {{ $content['storytelling']['elevator_pitch']['pitch'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'Unleash your creative potential with our innovative platform designed to transform ideas into extraordinary experiences.' }}
                </p>
                <div class="hero-actions">
                    <a href="#contact" class="btn">Start Creating</a>
                    <a href="#features" class="btn btn-secondary">Explore Magic</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem & Solution Section -->
    <section class="section problem-solution">
        <div class="container">
            <div class="two-column">
                <div class="column">
                    <h3>The Creative Challenge</h3>
                    <p>
                        @if(isset($content['lean_canvas']['problem']['content']))
                            {{ $content['lean_canvas']['problem']['content'] }}
                        @else
                            Creativity often gets trapped by limitations, tools that don't inspire, and processes that drain the joy from artistic expression.
                        @endif
                    </p>
                </div>
                <div class="column">
                    <h3>Our Creative Solution</h3>
                    <p>
                        @if(isset($content['lean_canvas']['solution']['content']))
                            {{ $content['lean_canvas']['solution']['content'] }}
                        @else
                            We've crafted an intuitive platform that amplifies creativity, removes barriers, and turns every interaction into an opportunity for inspiration.
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section" id="features">
        <div class="container">
            <div class="section-header">
                <h2>Creative Superpowers</h2>
                <p class="section-subtitle">
                    @if(isset($content['lean_canvas']['unfair_advantage']['content']))
                        {{ $content['lean_canvas']['unfair_advantage']['content'] }}
                    @else
                        Discover the tools and features that will revolutionize your creative workflow and inspire breakthrough innovations.
                    @endif
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Limitless Expression</h3>
                    <p>Break free from constraints with tools designed to expand your creative horizons and bring your wildest ideas to life.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Lightning Fast</h3>
                    <p>Experience instant gratification with real-time rendering and seamless performance that keeps up with your creative flow.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌈</div>
                    <h3>Infinite Possibilities</h3>
                    <p>Explore endless combinations of styles, effects, and techniques with our AI-powered creative assistance.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    @if(isset($content['lean_canvas']['key_metrics']))
    <section class="section stats">
        <div class="container">
            <div class="stats-content">
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3>50K+</h3>
                        <p>Creative Projects</p>
                    </div>
                    <div class="stat-item">
                        <h3>100+</h3>
                        <p>Countries Inspired</p>
                    </div>
                    <div class="stat-item">
                        <h3>99%</h3>
                        <p>Satisfaction Rate</p>
                    </div>
                    <div class="stat-item">
                        <h3>24/7</h3>
                        <p>Creative Support</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="section cta" id="contact">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Create Magic?</h2>
                <p>
                    @if(isset($content['storytelling']['elevator_pitch']['call_to_action']))
                        {{ $content['storytelling']['elevator_pitch']['call_to_action'] }}
                    @else
                        Join thousands of creators who have already discovered the power of unlimited creative expression.
                    @endif
                </p>
                <a href="#" class="btn">Begin Your Journey</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; {{ date('Y') }} {{ $content['storytelling']['startup_naming']['business_name'] ?? $meta['title'] }}. Inspiring creativity worldwide.</p>
        </div>
    </footer>
</body>
</html> 