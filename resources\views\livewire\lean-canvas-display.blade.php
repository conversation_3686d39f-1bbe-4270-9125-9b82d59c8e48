<div>
    <div class="px-4 py-5 sm:p-6">

        <!-- Lean <PERSON> Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Row 1: Problem | Solution | Unique Value Proposition | Unfair Advantage | Customer Segments -->
            
            <!-- Problem (1) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'problem',
                    'config' => $sectionConfig['problem'],
                    'content' => $this->getSectionContent('problem'),
                    'isLoading' => $loadingStates['problem'] ?? false,
                    'error' => $errors['problem'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('problem')
                ])
            </div>

            <!-- Solution (2) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'solution',
                    'config' => $sectionConfig['solution'],
                    'content' => $this->getSectionContent('solution'),
                    'isLoading' => $loadingStates['solution'] ?? false,
                    'error' => $errors['solution'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('solution')
                ])
            </div>

            <!-- Unique Value Proposition (3) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'unique_value_proposition',
                    'config' => $sectionConfig['unique_value_proposition'],
                    'content' => $this->getSectionContent('unique_value_proposition'),
                    'isLoading' => $loadingStates['unique_value_proposition'] ?? false,
                    'error' => $errors['unique_value_proposition'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('unique_value_proposition'),
                    'isHighlighted' => true,
                    'centerContent' => true
                ])
            </div>

            <!-- Unfair Advantage (4) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'unfair_advantage',
                    'config' => $sectionConfig['unfair_advantage'],
                    'content' => $this->getSectionContent('unfair_advantage'),
                    'isLoading' => $loadingStates['unfair_advantage'] ?? false,
                    'error' => $errors['unfair_advantage'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('unfair_advantage')
                ])
            </div>

            <!-- Customer Segments (5) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'customer_segments',
                    'config' => $sectionConfig['customer_segments'],
                    'content' => $this->getSectionContent('customer_segments'),
                    'isLoading' => $loadingStates['customer_segments'] ?? false,
                    'error' => $errors['customer_segments'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('customer_segments'),
                    'showEarlyAdopters' => true
                ])
            </div>

            <!-- Row 2: Existing Alternatives | Key Metrics | Channels | Cost Structure | Revenue Streams -->
            
            <!-- Existing Alternatives (6) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'existing_alternatives',
                    'config' => $sectionConfig['existing_alternatives'],
                    'content' => $this->getSectionContent('existing_alternatives'),
                    'isLoading' => $loadingStates['existing_alternatives'] ?? false,
                    'error' => $errors['existing_alternatives'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('existing_alternatives')
                ])
            </div>

            <!-- Key Metrics (7) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'key_metrics',
                    'config' => $sectionConfig['key_metrics'],
                    'content' => $this->getSectionContent('key_metrics'),
                    'isLoading' => $loadingStates['key_metrics'] ?? false,
                    'error' => $errors['key_metrics'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('key_metrics')
                ])
            </div>

            <!-- Channels (8) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'channels',
                    'config' => $sectionConfig['channels'],
                    'content' => $this->getSectionContent('channels'),
                    'isLoading' => $loadingStates['channels'] ?? false,
                    'error' => $errors['channels'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('channels')
                ])
            </div>

            <!-- Cost Structure (9) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'cost_structure',
                    'config' => $sectionConfig['cost_structure'],
                    'content' => $this->getSectionContent('cost_structure'),
                    'isLoading' => $loadingStates['cost_structure'] ?? false,
                    'error' => $errors['cost_structure'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('cost_structure')
                ])
            </div>

            <!-- Revenue Streams (10) -->
            <div>
                @include('livewire.partials.lean-canvas-section', [
                    'sectionKey' => 'revenue_streams',
                    'config' => $sectionConfig['revenue_streams'],
                    'content' => $this->getSectionContent('revenue_streams'),
                    'isLoading' => $loadingStates['revenue_streams'] ?? false,
                    'error' => $errors['revenue_streams'] ?? null,
                    'generatedAt' => $this->getGeneratedAt('revenue_streams')
                ])
            </div>
        </div>
    </div>

    <!-- Full Text Modal -->
    @if($showModal)
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>
            
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4" id="modal-title">
                                {{ $modalTitle }}
                            </h3>
                            <div class="mt-2">
                                <div class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed prose prose-sm dark:prose-invert max-w-none">
                                    {!! \Illuminate\Support\Str::markdown($modalContent) !!}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" wire:click="closeModal" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Polling Script -->
    <script>
        document.addEventListener('livewire:init', () => {
            let pollingInterval;
            
            // Start polling when component loads
            function startPolling() {
                pollingInterval = setInterval(() => {
                    @this.pollForUpdates();
                }, 5000); // Poll every 5 seconds
            }
            
            // Stop polling when all content is generated
            Livewire.on('stop-polling', () => {
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = null;
                }
            });
            
            // Start polling immediately if there's any loading state
            @if(collect($loadingStates)->contains(true) || !$this->hasAllContentGenerated())
                startPolling();
            @endif
        });
    </script>
</div>