<?php

namespace Database\Seeders;

use App\Models\Account;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin account
        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'phone' => '******-0001',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'active',
            ]
        );

        // Create demo entrepreneur accounts
        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'phone' => '******-0101',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'active',
            ]
        );

        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'phone' => '******-0102',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'active',
            ]
        );

        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Emily Rodriguez',
                'phone' => '******-0103',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'active',
            ]
        );

        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'David Kim',
                'phone' => '******-0104',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'active',
            ]
        );

        // Create a blocked account for testing
        Account::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Blocked User',
                'phone' => '******-0999',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'status' => 'blocked',
                'block_reason' => 'Account suspended for testing purposes',
            ]
        );

        // Create additional random accounts using factory
        Account::factory(15)->create();

        $this->command->info('Created 21 accounts (6 specific + 15 random)');
    }
}
