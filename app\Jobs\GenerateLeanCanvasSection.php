<?php

namespace App\Jobs;

use App\Events\LeanCanvasSectionGenerated;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class GenerateLeanCanvasSection implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $maxExceptions = 3;

    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Project $project,
        public string $sectionKey,
        public bool $isRegeneration = false
    ) {
        // Set queue based on priority
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(ContentGenerationService $contentService): void
    {
        try {
            Log::info('Starting Lean Canvas section generation', [
                'project_id' => $this->project->id,
                'section_key' => $this->sectionKey,
                'is_regeneration' => $this->isRegeneration,
                'job_id' => $this->job->getJobId(),
            ]);

            // Generate the content based on section type
            $generatedContent = match ($this->sectionKey) {
                'problem' => $contentService->generateProblemSection($this->project),
                'solution' => $contentService->generateSolutionSection($this->project),
                'unique_value_proposition' => $contentService->generateUniqueValuePropositionSection($this->project),
                'customer_segments' => $contentService->generateCustomerSegmentsSection($this->project),
                'existing_alternatives' => $contentService->generateExistingAlternativesSection($this->project),
                'key_metrics' => $contentService->generateKeyMetricsSection($this->project),
                'channels' => $contentService->generateChannelsSection($this->project),
                'unfair_advantage' => $contentService->generateUnfairAdvantageSection($this->project),
                'cost_structure' => $contentService->generateCostStructureSection($this->project),
                'revenue_streams' => $contentService->generateRevenueStreamsSection($this->project),
                default => throw new \InvalidArgumentException("Unknown section key: {$this->sectionKey}")
            };

            Log::info('Lean Canvas section generated successfully', [
                'project_id' => $this->project->id,
                'section_key' => $this->sectionKey,
                'content_id' => $generatedContent->id,
                'job_id' => $this->job->getJobId(),
            ]);

            // Broadcast the success event
            broadcast(new LeanCanvasSectionGenerated(
                $this->project,
                $this->sectionKey,
                $generatedContent,
                true // success
            ));

        } catch (Throwable $exception) {
            Log::error('Failed to generate Lean Canvas section', [
                'project_id' => $this->project->id,
                'section_key' => $this->sectionKey,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
                'job_id' => $this->job->getJobId(),
            ]);

            // Broadcast the failure event
            broadcast(new LeanCanvasSectionGenerated(
                $this->project,
                $this->sectionKey,
                null,
                false, // success = false
                $exception->getMessage()
            ));

            throw $exception;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Log::error('Lean Canvas section generation job failed permanently', [
            'project_id' => $this->project->id,
            'section_key' => $this->sectionKey,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
            'job_id' => $this->job?->getJobId(),
        ]);

        // Broadcast final failure event
        broadcast(new LeanCanvasSectionGenerated(
            $this->project,
            $this->sectionKey,
            null,
            false,
            "Generation failed after {$this->tries} attempts: ".$exception->getMessage()
        ));
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'lean-canvas',
            'project:'.$this->project->id,
            'section:'.$this->sectionKey,
            $this->isRegeneration ? 'regeneration' : 'generation',
        ];
    }
}
