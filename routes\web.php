<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Landing page preview route
Route::get('/landing-page-preview/{project}/{theme?}', function ($projectId, $theme = 'modern') {
    $project = \App\Models\Project::findOrFail($projectId);
    $templateEngine = app(\App\Services\LandingPageTemplateEngine::class);
    
    try {
        $html = $templateEngine->generateLandingPage($project, $theme);
        return response($html)->header('Content-Type', 'text/html');
    } catch (\Exception $e) {
        return response('<html><body><h1>Preview Error</h1><p>' . $e->getMessage() . '</p></body></html>')
            ->header('Content-Type', 'text/html');
    }
})->name('landing-page.preview');
