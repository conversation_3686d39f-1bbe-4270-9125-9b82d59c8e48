<?php

namespace App\Filament\App\Resources;

use App\Filament\App\Resources\MarketResearchSessionResource\Pages;
use App\Filament\App\Resources\MarketResearchSessionResource\RelationManagers;
use App\Models\MarketResearchSession;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MarketResearchSessionResource extends Resource
{
    protected static ?string $model = MarketResearchSession::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    
    protected static ?string $navigationLabel = 'Research Sessions';
    
    protected static ?string $navigationGroup = 'Research Tools';
    
    protected static ?int $navigationSort = 2;
    
    protected static ?string $recordTitleAttribute = 'industry';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Research Parameters')
                    ->schema([
                        Forms\Components\TextInput::make('industry')
                            ->required()
                            ->maxLength(100)
                            ->columnSpan(1),
                        Forms\Components\TextInput::make('region')
                            ->required()
                            ->maxLength(100)
                            ->columnSpan(1),
                        Forms\Components\DateTimePicker::make('generated_at')
                            ->default(now())
                            ->columnSpan(1),
                    ])
                    ->columns(3),
                    
                Forms\Components\Section::make('Market Analysis')
                    ->schema([
                        Forms\Components\KeyValue::make('market_attractiveness')
                            ->label('Market Attractiveness')
                            ->keyLabel('Metric')
                            ->valueLabel('Value')
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($value, $key) {
                                        if (is_array($value)) {
                                            return [$key => json_encode($value)];
                                        }
                                        return [$key => (string) $value];
                                    })->collapse()->toArray();
                                }
                                return $state ?? [];
                            }),
                        Forms\Components\KeyValue::make('market_size')
                            ->label('Market Size')
                            ->keyLabel('Metric')
                            ->valueLabel('Value')
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($value, $key) {
                                        if (is_array($value)) {
                                            return [$key => json_encode($value)];
                                        }
                                        return [$key => (string) $value];
                                    })->collapse()->toArray();
                                }
                                return $state ?? [];
                            }),
                    ])
                    ->collapsible(),
                    
                Forms\Components\Section::make('Research Data')
                    ->schema([
                        Forms\Components\Repeater::make('opportunity_zones')
                            ->label('Opportunity Zones')
                            ->schema([
                                Forms\Components\TextInput::make('title')->required(),
                                Forms\Components\Textarea::make('description'),
                            ])
                            ->collapsed()
                            ->columnSpanFull(),
                        Forms\Components\Repeater::make('customer_pain_points')
                            ->label('Customer Pain Points')
                            ->schema([
                                Forms\Components\TextInput::make('title')->required(),
                                Forms\Components\Textarea::make('description'),
                            ])
                            ->collapsed()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
                    
                Forms\Components\Section::make('Strategic Analysis')
                    ->schema([
                        Forms\Components\Textarea::make('strategic_implications')
                            ->label('Strategic Implications')
                            ->rows(3)
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($item) {
                                        if (is_array($item) && isset($item['title'], $item['description'])) {
                                            return $item['title'] . ': ' . $item['description'];
                                        }
                                        return (string) $item;
                                    })->join("\n");
                                }
                                return $state;
                            })
                            ->dehydrateStateUsing(function ($state) {
                                if (is_string($state)) {
                                    return array_filter(explode("\n", $state));
                                }
                                return $state;
                            }),
                        Forms\Components\Textarea::make('competitive_landscape')
                            ->label('Competitive Landscape')
                            ->rows(3)
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($competitor) {
                                        if (is_array($competitor)) {
                                            // Handle the actual structure: name, strength, differentiation
                                            $name = $competitor['name'] ?? 'Unknown';
                                            $strength = $competitor['strength'] ?? $competitor['description'] ?? '';
                                            $differentiation = $competitor['differentiation'] ?? '';
                                            
                                            $details = collect([$strength, $differentiation])->filter()->join(' | ');
                                            return $name . ': ' . ($details ?: 'No details available');
                                        }
                                        return (string) $competitor;
                                    })->join(' • ');
                                }
                                return $state ?? 'No competitive landscape data available';
                            })
                            ->dehydrateStateUsing(function ($state) {
                                if (is_string($state)) {
                                    $lines = array_filter(explode("\n", $state));
                                    return collect($lines)->map(function ($line) {
                                        if (strpos($line, ':') !== false) {
                                            [$name, $description] = explode(':', $line, 2);
                                            return ['name' => trim($name), 'description' => trim($description)];
                                        }
                                        return ['name' => trim($line), 'description' => 'Competitor in the market'];
                                    })->toArray();
                                }
                                return $state;
                            }),
                        Forms\Components\KeyValue::make('enablers_barriers')
                            ->label('Enablers & Barriers')
                            ->keyLabel('Type')
                            ->valueLabel('Details')
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($value, $key) {
                                        if (is_array($value)) {
                                            // Safe handling of nested arrays
                                            $formatted = collect($value)->map(function ($item) {
                                                return is_array($item) ? json_encode($item) : (string) $item;
                                            })->join(', ');
                                            return [$key => $formatted];
                                        }
                                        return [$key => (string) $value];
                                    })->collapse()->toArray();
                                }
                                return $state ?? [];
                            }),
                        Forms\Components\KeyValue::make('swot_analysis')
                            ->label('SWOT Analysis')
                            ->keyLabel('Category')
                            ->valueLabel('Items')
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return collect($state)->map(function ($value, $key) {
                                        if (is_array($value)) {
                                            // Handle SWOT arrays (strengths, weaknesses, opportunities, threats)
                                            $formatted = collect($value)->map(function ($item) {
                                                return is_array($item) ? json_encode($item) : (string) $item;
                                            })->join(', ');
                                            return [$key => $formatted];
                                        }
                                        return [$key => (string) $value];
                                    })->collapse()->toArray();
                                }
                                return $state ?? [];
                            }),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('industry')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                Tables\Columns\TextColumn::make('region')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('completion_percentage')
                    ->label('Completion')
                    ->formatStateUsing(function ($state) {
                        if (is_numeric($state)) {
                            return $state . '%';
                        }
                        if (is_array($state)) {
                            return '0%';
                        }
                        return (string) $state . '%';
                    })
                    ->colors([
                        'danger' => static fn ($state): bool => is_numeric($state) && $state < 50,
                        'warning' => static fn ($state): bool => is_numeric($state) && $state >= 50 && $state < 80,
                        'success' => static fn ($state): bool => is_numeric($state) && $state >= 80,
                    ]),
                Tables\Columns\TextColumn::make('generated_at')
                    ->label('Generated')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('generated_at', 'desc')
            ->filters([
                Tables\Filters\Filter::make('recent')
                    ->label('Recent (Last 7 days)')
                    ->query(fn (Builder $query): Builder => $query->where('generated_at', '>=', now()->subDays(7))),
                Tables\Filters\SelectFilter::make('industry')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('region')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('Generate First Research')
                    ->icon('heroicon-m-plus'),
            ]);
    }
    
    public static function infolist(\Filament\Infolists\Infolist $infolist): \Filament\Infolists\Infolist
    {
        return $infolist
            ->schema([
                // Header Section - Research Overview with Card Layout
                Infolists\Components\Section::make('Market Research Session')
                    ->description('Comprehensive AI-powered market analysis and strategic insights')
                    ->schema([
                        // Session Metadata Cards
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                // Primary Info Card
                                Infolists\Components\Section::make()
                                    ->schema([
                                        Infolists\Components\TextEntry::make('industry')
                                            ->label('Target Industry')
                                            ->badge()
                                            ->color('primary')
                                            ->size('lg')
                                            ->icon('heroicon-o-building-office-2'),
                                        Infolists\Components\TextEntry::make('region')
                                            ->label('Target Region/Country')
                                            ->badge()
                                            ->color('success')
                                            ->size('lg')
                                            ->icon('heroicon-o-globe-americas'),
                                    ])
                                    ->columnSpan(1),
                                
                                // Session Details Card
                                Infolists\Components\Section::make()
                                    ->schema([
                                        Infolists\Components\TextEntry::make('generated_at')
                                            ->label('Generated On')
                                            ->dateTime('M j, Y g:i A')
                                            ->badge()
                                            ->color('gray')
                                            ->icon('heroicon-o-calendar'),
                                        Infolists\Components\TextEntry::make('completion_percentage')
                                            ->label('Data Completeness')
                                            ->badge()
                                            ->color(fn ($state) => $state >= 80 ? 'success' : ($state >= 50 ? 'warning' : 'danger'))
                                            ->suffix('%')
                                            ->icon('heroicon-o-chart-bar'),
                                    ])
                                    ->columnSpan(1),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Key Metrics - Enhanced Card Layout
                Infolists\Components\Grid::make(3)
                    ->schema([
                        // Market Attractiveness Card
                        Infolists\Components\Section::make('Market Attractiveness')
                            ->icon('heroicon-o-star')
                            ->iconColor('warning')
                            ->schema([
                                Infolists\Components\TextEntry::make('formatted_market_attractiveness')
                                    ->hiddenLabel()
                                    ->html()
                                    ->formatStateUsing(function ($state, $record) {
                                        $score = 'N/A';
                                        $statement = 'No assessment available';
                                        
                                        try {
                                            if (is_array($record->market_attractiveness)) {
                                                $score = $record->market_attractiveness['score'] ?? 
                                                        $record->market_attractiveness['attractivenessScore'] ?? 'N/A';
                                                $statement = $record->market_attractiveness['statement'] ?? 
                                                           $record->market_attractiveness['assessment'] ?? 
                                                           'No assessment available';
                                                
                                                if (is_numeric($score)) {
                                                    $score = $score . '/100';
                                                }
                                            }
                                        } catch (\Exception $e) {
                                            // Fallback values already set
                                        }
                                        
                                        return '<div class="text-center p-4">
                                            <div class="text-4xl font-bold text-warning-600 mb-3">' . htmlspecialchars($score) . '</div>
                                            <div class="text-sm text-gray-600 leading-relaxed">' . htmlspecialchars($statement) . '</div>
                                        </div>';
                                    }),
                            ])
                            ->columnSpan(1),

                        // Market Size Card
                        Infolists\Components\Section::make('Market Size Analysis')
                            ->icon('heroicon-o-currency-dollar')
                            ->iconColor('success')
                            ->schema([
                                Infolists\Components\TextEntry::make('formatted_market_size')
                                    ->hiddenLabel()
                                    ->html()
                                    ->formatStateUsing(function ($state, $record) {
                                        try {
                                            if (is_array($record->market_size)) {
                                                $tam = $record->market_size['tam'] ?? $record->market_size['totalMarket'] ?? 'N/A';
                                                $sam = $record->market_size['sam'] ?? $record->market_size['serviceableMarket'] ?? 'N/A';
                                                $cagr = $record->market_size['cagr'] ?? $record->market_size['growthRate'] ?? 'N/A';
                                                
                                                return '<div class="space-y-3 p-2">
                                                    <div class="text-center">
                                                        <div class="text-2xl font-bold text-success-600 mb-1">' . htmlspecialchars(is_array($tam) ? json_encode($tam) : $tam) . '</div>
                                                        <div class="text-xs text-gray-500 mb-3">Total Addressable Market</div>
                                                    </div>
                                                    <div class="space-y-2 text-sm">
                                                        <div class="flex justify-between items-center">
                                                            <span class="font-medium text-gray-700">SAM:</span>
                                                            <span class="text-gray-600">' . htmlspecialchars(is_array($sam) ? json_encode($sam) : $sam) . '</span>
                                                        </div>
                                                        <div class="flex justify-between items-center">
                                                            <span class="font-medium text-gray-700">CAGR:</span>
                                                            <span class="text-gray-600">' . htmlspecialchars(is_array($cagr) ? json_encode($cagr) : $cagr) . '</span>
                                                        </div>
                                                    </div>
                                                </div>';
                                            }
                                        } catch (\Exception $e) {
                                            // Fallback
                                        }
                                        return '<div class="text-center text-gray-500 p-4">No market size data available</div>';
                                    }),
                            ])
                            ->columnSpan(1),

                        // Opportunity Zones Card
                        Infolists\Components\Section::make('Opportunity Zones')
                            ->icon('heroicon-o-light-bulb')
                            ->iconColor('warning')
                            ->schema([
                                Infolists\Components\TextEntry::make('formatted_opportunity_zones')
                                    ->hiddenLabel()
                                    ->html()
                                    ->formatStateUsing(function ($state, $record) {
                                        try {
                                            if (is_array($record->opportunity_zones)) {
                                                $count = count($record->opportunity_zones);
                                                $zones = collect($record->opportunity_zones)->take(3);
                                                
                                                $html = '<div class="text-center p-2">
                                                    <div class="text-4xl font-bold text-warning-600 mb-3">' . $count . '</div>
                                                    <div class="text-sm text-gray-600 mb-3">High-potential opportunities</div>';
                                                
                                                if ($zones->isNotEmpty()) {
                                                    $html .= '<div class="text-xs text-left space-y-1">';
                                                    foreach ($zones as $zone) {
                                                        $title = is_array($zone) ? ($zone['title'] ?? 'Opportunity') : 'Opportunity';
                                                        $html .= '<div class="text-gray-500">• ' . htmlspecialchars($title) . '</div>';
                                                    }
                                                    if ($count > 3) {
                                                        $html .= '<div class="text-gray-400">+ ' . ($count - 3) . ' more...</div>';
                                                    }
                                                    $html .= '</div>';
                                                }
                                                
                                                return $html . '</div>';
                                            }
                                        } catch (\Exception $e) {
                                            // Fallback
                                        }
                                        return '<div class="text-center text-gray-500 p-4">No opportunities identified</div>';
                                    }),
                            ])
                            ->columnSpan(1),
                    ]),

                // Main Content - Enhanced Two Column Layout with Better Spacing
                Infolists\Components\Grid::make(2)
                    ->schema([
                        // Left Column - Research Analysis
                        Infolists\Components\Section::make('Research Analysis')
                            ->icon('heroicon-o-magnifying-glass')
                            ->iconColor('info')
                            ->schema([
                                // Research Scope
                                Infolists\Components\Section::make('Research Scope Highlights')
                                    ->icon('heroicon-o-document-magnifying-glass')
                                    ->iconColor('blue')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('formatted_research_scope')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state) {
                                                if (is_string($state) && !empty($state)) {
                                                    return '<div class="prose prose-sm max-w-none text-gray-700 leading-relaxed">' . nl2br(htmlspecialchars($state)) . '</div>';
                                                }
                                                return '<div class="text-gray-500 italic">No research scope data available</div>';
                                            }),
                                    ])
                                    ->collapsible(),
                                
                                // Customer Pain Points
                                Infolists\Components\Section::make('Customer Pain Points')
                                    ->icon('heroicon-o-exclamation-triangle')
                                    ->iconColor('danger')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('formatted_customer_pain_points')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state) {
                                                if (is_string($state) && !empty($state) && $state !== 'No pain points available') {
                                                    $points = explode(' | ', $state);
                                                    $html = '<div class="space-y-3">';
                                                    foreach ($points as $index => $point) {
                                                        $html .= '<div class="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
                                                            <div class="w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">' . ($index + 1) . '</div>
                                                            <div class="text-sm text-red-800 leading-relaxed">' . htmlspecialchars(trim($point)) . '</div>
                                                        </div>';
                                                    }
                                                    return $html . '</div>';
                                                }
                                                return '<div class="text-gray-500 italic">No customer pain points identified</div>';
                                            }),
                                    ])
                                    ->collapsible(),
                                
                                // Competitive Landscape
                                Infolists\Components\Section::make('Competitive Landscape')
                                    ->icon('heroicon-o-building-office')
                                    ->iconColor('purple')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('id')
                                            ->label('Competitive Analysis Matrix')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state, $record) {
                                                if (is_array($record->competitive_landscape) && !empty($record->competitive_landscape)) {
                                                    $html = '<div class="space-y-4">';
                                                    
                                                    // Add professional header
                                                    $html .= '<div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                                        <h4 class="font-semibold text-purple-800 mb-2 flex items-center">
                                                            <span class="mr-2">🏢</span>
                                                            Competitive Analysis Matrix
                                                        </h4>
                                                        <p class="text-sm text-purple-600">Key competitors in the market with detailed positioning analysis</p>
                                                    </div>';
                                                    
                                                    // Generate competitor cards
                                                    foreach ($record->competitive_landscape as $index => $competitor) {
                                                        // Extract competitor data
                                                        $name = is_array($competitor) ? ($competitor['name'] ?? 'Unknown Company') : (string) $competitor;
                                                        $marketPosition = is_array($competitor) ? ($competitor['marketPosition'] ?? 'Market participant') : 'Market participant';
                                                        $marketShare = is_array($competitor) ? ($competitor['marketShare'] ?? 'N/A') : 'N/A';
                                                        $companyDomain = is_array($competitor) ? ($competitor['companyDomain'] ?? '') : '';
                                                        $description = is_array($competitor) ? ($competitor['description'] ?? 'No description available') : 'No description available';
                                                        
                                                        // Handle differentiators array
                                                        $differentiators = [];
                                                        if (is_array($competitor) && isset($competitor['keyDifferentiators']) && is_array($competitor['keyDifferentiators'])) {
                                                            $differentiators = $competitor['keyDifferentiators'];
                                                        } elseif (is_array($competitor) && isset($competitor['differentiation'])) {
                                                            $differentiators = [$competitor['differentiation']];
                                                        }
                                                        
                                                        // Handle strengths array
                                                        $strengths = [];
                                                        if (is_array($competitor) && isset($competitor['strengths']) && is_array($competitor['strengths'])) {
                                                            $strengths = $competitor['strengths'];
                                                        } elseif (is_array($competitor) && isset($competitor['strength'])) {
                                                            $strengths = [$competitor['strength']];
                                                        }
                                                        
                                                        // Format website link
                                                        $websiteLink = '';
                                                        if (!empty($companyDomain)) {
                                                            $domain = htmlspecialchars($companyDomain);
                                                            $url = strpos($domain, 'http') === 0 ? $domain : 'https://' . $domain;
                                                            $websiteLink = '<a href="' . $url . '" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline font-medium">' . $domain . '</a>';
                                                        }
                                                        
                                                        // Create competitor card
                                                        $html .= '<div class="border border-gray-200 rounded-lg p-5 bg-white hover:shadow-lg transition-shadow duration-200">
                                                            <div class="flex justify-between items-start mb-4">
                                                                <h5 class="font-bold text-xl text-gray-900">' . htmlspecialchars($name) . '</h5>
                                                                <div class="flex items-center space-x-2">
                                                                    <span class="px-3 py-1 text-sm font-medium bg-purple-100 text-purple-800 rounded-full">' . htmlspecialchars($marketPosition) . '</span>
                                                                    <span class="px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">' . htmlspecialchars($marketShare) . '</span>
                                                                </div>
                                                            </div>
                                                            
                                                            <p class="text-gray-700 text-sm mb-4 leading-relaxed">' . htmlspecialchars($description) . '</p>
                                                            
                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                <div>
                                                                    <h6 class="font-semibold text-gray-800 text-sm mb-3 flex items-center">
                                                                        <span class="mr-2">🎯</span>
                                                                        Key Differentiators
                                                                    </h6>
                                                                    <ul class="text-sm text-gray-600 space-y-2">';
                                                        
                                                        foreach ($differentiators as $diff) {
                                                            $html .= '<li class="flex items-start">
                                                                <span class="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                                <span>' . htmlspecialchars($diff) . '</span>
                                                            </li>';
                                                        }
                                                        if (empty($differentiators)) {
                                                            $html .= '<li class="text-gray-400 italic">Not specified</li>';
                                                        }
                                                        
                                                        $html .= '</ul>
                                                                </div>
                                                                <div>
                                                                    <h6 class="font-semibold text-gray-800 text-sm mb-3 flex items-center">
                                                                        <span class="mr-2">💪</span>
                                                                        Core Strengths
                                                                    </h6>
                                                                    <ul class="text-sm text-gray-600 space-y-2">';
                                                        
                                                        foreach ($strengths as $strength) {
                                                            $html .= '<li class="flex items-start">
                                                                <span class="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                                                <span>' . htmlspecialchars($strength) . '</span>
                                                            </li>';
                                                        }
                                                        if (empty($strengths)) {
                                                            $html .= '<li class="text-gray-400 italic">Not specified</li>';
                                                        }
                                                        
                                                        $html .= '</ul>
                                                                </div>
                                                            </div>';
                                                        
                                                        if (!empty($websiteLink)) {
                                                            $html .= '<div class="mt-4 pt-4 border-t border-gray-100">
                                                                <div class="flex items-center text-sm text-gray-600">
                                                                    <span class="mr-2">🌐</span>
                                                                    <span class="mr-2">Website:</span>
                                                                    ' . $websiteLink . '
                                                                </div>
                                                            </div>';
                                                        }
                                                        
                                                        $html .= '</div>';
                                                    }
                                                    
                                                    // Add summary footer
                                                    $totalCompetitors = count($record->competitive_landscape);
                                                    $html .= '<div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                                        <div class="text-sm text-purple-700 flex items-center">
                                                            <span class="mr-2">📊</span>
                                                            <strong>Analysis Summary:</strong>
                                                            <span class="ml-2">' . $totalCompetitors . ' key competitors identified with detailed market positioning and competitive advantages.</span>
                                                        </div>
                                                    </div>';
                                                    
                                                    $html .= '</div>';
                                                    return $html;
                                                }
                                                
                                                return '<div class="text-gray-500 italic p-4 text-center bg-gray-50 rounded-lg">No competitive landscape data available</div>';
                                            }),
                                    ])
                                    ->collapsible()
                                    ->collapsed(false),
                            ])
                            ->columnSpan(1),

                        // Right Column - Strategic Insights
                        Infolists\Components\Section::make('Strategic Insights')
                            ->icon('heroicon-o-light-bulb')
                            ->iconColor('warning')
                            ->schema([
                                // Strategic Implications
                                Infolists\Components\Section::make('Strategic Implications')
                                    ->icon('heroicon-o-puzzle-piece')
                                    ->iconColor('info')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('formatted_strategic_implications')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state) {
                                                if (is_string($state) && !empty($state) && $state !== 'No strategic implications available') {
                                                    $implications = explode(', ', $state);
                                                    $html = '<div class="space-y-3">';
                                                    foreach ($implications as $index => $implication) {
                                                        $html .= '<div class="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                                                            <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">' . ($index + 1) . '</div>
                                                            <div class="text-sm text-blue-800 leading-relaxed">' . htmlspecialchars(trim($implication)) . '</div>
                                                        </div>';
                                                    }
                                                    return $html . '</div>';
                                                }
                                                return '<div class="text-gray-500 italic">No strategic implications available</div>';
                                            }),
                                    ])
                                    ->collapsible(),
                                
                                // Opportunity Zones Details
                                Infolists\Components\Section::make('Opportunity Zone Details')
                                    ->icon('heroicon-o-map-pin')
                                    ->iconColor('success')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('formatted_opportunity_zones')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state) {
                                                if (is_string($state) && !empty($state) && $state !== 'No opportunity zones available') {
                                                    $zones = explode(' | ', $state);
                                                    $colors = ['yellow', 'green', 'blue', 'purple', 'pink'];
                                                    $html = '<div class="space-y-4">';
                                                    foreach ($zones as $index => $zone) {
                                                        $color = $colors[$index % count($colors)];
                                                        if (strpos($zone, ':') !== false) {
                                                            [$title, $description] = explode(':', $zone, 2);
                                                            $html .= '<div class="border-l-4 border-' . $color . '-400 bg-' . $color . '-50 p-4 rounded-r-lg">
                                                                <div class="font-semibold text-' . $color . '-800 mb-2">' . htmlspecialchars(trim($title)) . '</div>
                                                                <div class="text-sm text-' . $color . '-600 leading-relaxed">' . htmlspecialchars(trim($description)) . '</div>
                                                            </div>';
                                                        }
                                                    }
                                                    return $html . '</div>';
                                                }
                                                return '<div class="text-gray-500 italic">No opportunity zones available</div>';
                                            }),
                                    ])
                                    ->collapsible(),
                                
                                // SWOT Analysis - Enhanced Layout
                                Infolists\Components\Section::make('SWOT Analysis Matrix')
                                    ->icon('heroicon-o-squares-2x2')
                                    ->iconColor('gray')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('formatted_swot_analysis')
                                            ->hiddenLabel()
                                            ->html()
                                            ->formatStateUsing(function ($state) {
                                                if (is_string($state) && !empty($state) && $state !== 'No SWOT analysis data available') {
                                                    $swotParts = explode(' | ', $state);
                                                    $swotData = [];
                                                    
                                                    foreach ($swotParts as $part) {
                                                        if (strpos($part, 'strengths:') !== false) {
                                                            $swotData['strengths'] = str_replace('strengths:', '', $part);
                                                        } elseif (strpos($part, 'weaknesses:') !== false) {
                                                            $swotData['weaknesses'] = str_replace('weaknesses:', '', $part);
                                                        } elseif (strpos($part, 'opportunities:') !== false) {
                                                            $swotData['opportunities'] = str_replace('opportunities:', '', $part);
                                                        } elseif (strpos($part, 'threats:') !== false) {
                                                            $swotData['threats'] = str_replace('threats:', '', $part);
                                                        }
                                                    }
                                                    
                                                    $html = '<div class="grid grid-cols-1 gap-4">';
                                                    
                                                    $categories = [
                                                        'strengths' => ['label' => 'Strengths', 'color' => 'green', 'icon' => '💪'],
                                                        'weaknesses' => ['label' => 'Weaknesses', 'color' => 'red', 'icon' => '⚠️'],
                                                        'opportunities' => ['label' => 'Opportunities', 'color' => 'blue', 'icon' => '🚀'],
                                                        'threats' => ['label' => 'Threats', 'color' => 'yellow', 'icon' => '⚡']
                                                    ];
                                                    
                                                    foreach ($categories as $key => $category) {
                                                        $content = trim($swotData[$key] ?? 'N/A');
                                                        $html .= '<div class="border-l-4 border-' . $category['color'] . '-400 bg-' . $category['color'] . '-50 p-4 rounded-r-lg">
                                                            <div class="font-semibold text-' . $category['color'] . '-800 mb-2 flex items-center">
                                                                <span class="mr-2">' . $category['icon'] . '</span>
                                                                ' . $category['label'] . '
                                                            </div>
                                                            <div class="text-sm text-' . $category['color'] . '-700 leading-relaxed">' . htmlspecialchars($content) . '</div>
                                                        </div>';
                                                    }
                                                    
                                                    return $html . '</div>';
                                                }
                                                return '<div class="text-gray-500 italic">No SWOT analysis data available</div>';
                                            }),
                                    ])
                                    ->collapsible(),
                            ])
                            ->columnSpan(1),
                    ]),

                // Enablers & Barriers - Full Width Section
                Infolists\Components\Section::make('Market Enablers & Barriers')
                    ->icon('heroicon-o-scale')
                    ->iconColor('gray')
                    ->schema([
                        Infolists\Components\TextEntry::make('formatted_enablers_barriers')
                            ->hiddenLabel()
                            ->html()
                            ->formatStateUsing(function ($state) {
                                if (is_string($state) && !empty($state) && $state !== 'No enablers & barriers data available') {
                                    $parts = explode(' | ', $state);
                                    $html = '<div class="grid grid-cols-1 md:grid-cols-2 gap-6">';
                                    
                                    foreach ($parts as $part) {
                                        if (strpos($part, 'enablers:') !== false) {
                                            $content = str_replace('enablers:', '', $part);
                                            $html .= '<div class="border-l-4 border-green-400 bg-green-50 p-4 rounded-r-lg">
                                                <div class="font-semibold text-green-800 mb-3 flex items-center">
                                                    <span class="mr-2">✅</span>
                                                    Market Enablers
                                                </div>
                                                <div class="text-sm text-green-700 leading-relaxed">' . htmlspecialchars(trim($content)) . '</div>
                                            </div>';
                                        } elseif (strpos($part, 'barriers:') !== false) {
                                            $content = str_replace('barriers:', '', $part);
                                            $html .= '<div class="border-l-4 border-red-400 bg-red-50 p-4 rounded-r-lg">
                                                <div class="font-semibold text-red-800 mb-3 flex items-center">
                                                    <span class="mr-2">🚫</span>
                                                    Market Barriers
                                                </div>
                                                <div class="text-sm text-red-700 leading-relaxed">' . htmlspecialchars(trim($content)) . '</div>
                                            </div>';
                                        }
                                    }
                                    
                                    return $html . '</div>';
                                }
                                return '<div class="text-gray-500 italic">No enablers & barriers data available</div>';
                            }),
                    ])
                    ->collapsible(),

                // Footer Information
                Infolists\Components\Section::make('Session Metadata')
                    ->icon('heroicon-o-information-circle')
                    ->iconColor('gray')
                    ->schema([
                        Infolists\Components\Grid::make(4)
                            ->schema([
                                Infolists\Components\TextEntry::make('created_at')
                                    ->label('Created')
                                    ->icon('heroicon-o-plus-circle')
                                    ->dateTime('M j, Y g:i A')
                                    ->badge()
                                    ->color('gray'),
                                Infolists\Components\TextEntry::make('updated_at')
                                    ->label('Last Updated')
                                    ->icon('heroicon-o-pencil-square')
                                    ->dateTime('M j, Y g:i A')
                                    ->badge()
                                    ->color('gray'),
                                Infolists\Components\TextEntry::make('id')
                                    ->label('Session ID')
                                    ->icon('heroicon-o-hashtag')
                                    ->badge()
                                    ->color('primary'),
                                Infolists\Components\TextEntry::make('user.name')
                                    ->label('Created By')
                                    ->icon('heroicon-o-user')
                                    ->badge()
                                    ->color('success')
                                    ->default('System'),
                            ]),
                        
                        Infolists\Components\TextEntry::make('disclaimer')
                            ->hiddenLabel()
                            ->html()
                            ->formatStateUsing(function () {
                                return '<div class="text-center text-xs text-gray-500 mt-6 p-4 bg-gray-50 rounded-lg border">
                                    <p class="font-semibold mb-2">📊 Market Research Dashboard Analysis</p>
                                    <p><strong>Generated:</strong> ' . now()->format('M j, Y \a\t g:i A') . '</p>
                                    <p class="mt-2">This dashboard presents AI-generated market insights based on research framework analysis. Data is conceptual and should be validated with additional market research.</p>
                                </div>';
                            }),
                    ])
                    ->collapsible()
                    ->collapsed(true),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarketResearchSessions::route('/'),
            'create' => Pages\CreateMarketResearchSession::route('/create'),
            'view' => Pages\ViewMarketResearchSession::route('/{record}'),
            'edit' => Pages\EditMarketResearchSession::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
    
    public static function getGloballySearchableAttributes(): array
    {
        return ['industry', 'region'];
    }
}
