<div class="space-y-6">
    {{-- Success Message --}}
    @if($showSuccessMessage)
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center justify-between">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 dark:text-green-200 font-medium">{{ $successMessage }}</span>
            </div>
            <button wire:click="hideSuccessMessage" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    @endif

    {{-- Error Messages --}}
    @if(!empty($errorMessages))
        @foreach($errorMessages as $errorMessage)
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-red-800 dark:text-red-200">{{ $errorMessage }}</span>
                </div>
            </div>
        @endforeach
    @endif

    {{-- Main Content --}}
    @if($this->hasAnyHypotheses())
        {{-- Status Badge --}}
        <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
                Generated
            </span>
        </div>

        {{-- Hypotheses Table --}}
        <div class="space-y-4">
            @php
                $hypothesesList = $this->getHypothesesList();
                $counter = 1;
            @endphp
            
            @foreach($hypothesesList as $hypothesis)
                <div class="flex items-start space-x-4 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    {{-- Number Circle --}}
                    <div class="flex-shrink-0 w-8 h-8 bg-cyan-100 dark:bg-cyan-900/50 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-cyan-600 dark:text-cyan-400">{{ sprintf('%02d', $counter++) }}</span>
                    </div>

                    {{-- Content --}}
                    <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                        {{-- Hypothesis Column --}}
                        <div class="md:col-span-1">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1 capitalize">
                                {{ $hypothesis['type'] }}
                            </h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                {{ $hypothesis['text'] }}
                            </p>
                        </div>

                        {{-- Criticality Column --}}
                        <div class="md:col-span-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getCriticalityColor($hypothesis['criticality']) }} bg-gray-100 dark:bg-gray-700">
                                {{ $hypothesis['criticality'] }}
                            </span>
                        </div>

                        {{-- Testing Method Column --}}
                        <div class="md:col-span-1 flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                {{ $hypothesis['testing_method'] }}
                            </span>
                            
                            {{-- Edit Button --}}
                            <button 
                                wire:click="regenerateHypothesis('{{ $hypothesis['type'] }}')" 
                                wire:loading.attr="disabled"
                                wire:target="regenerateHypothesis('{{ $hypothesis['type'] }}')"
                                class="ml-4 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 font-medium"
                            >
                                <span wire:loading.remove wire:target="regenerateHypothesis('{{ $hypothesis['type'] }}')">Edit</span>
                                <span wire:loading wire:target="regenerateHypothesis('{{ $hypothesis['type'] }}')">...</span>
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        {{-- Empty State --}}
        <div class="text-center py-12">
            <div class="text-gray-400 dark:text-gray-500 mb-4">
                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No hypotheses generated yet</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Generate critical hypotheses to identify and prioritize the fundamental assumptions underpinning your business.
            </p>
        </div>
    @endif

    {{-- Loading State --}}
    @if($loadingStates['all'] || array_filter($loadingStates))
        <div class="text-center py-8">
            <svg class="animate-spin mx-auto h-8 w-8 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Generating hypotheses...</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This may take a few moments while we analyze your startup idea.</p>
        </div>
    @endif

    <!-- Polling Script -->
    <script>
        document.addEventListener('livewire:init', () => {
            let pollingInterval;
            
            // Start polling when component loads
            function startPolling() {
                pollingInterval = setInterval(() => {
                    @this.pollForUpdates();
                }, 5000); // Poll every 5 seconds
            }
            
            // Stop polling when all content is generated
            Livewire.on('stop-polling', () => {
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = null;
                }
            });
            
            // Start polling immediately if there's any loading state or missing content
            @if(collect($loadingStates)->contains(true) || !$this->hasAllHypotheses())
                startPolling();
            @endif
        });
    </script>
</div> 