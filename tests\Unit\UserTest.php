<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Tests\TestCase;

class UserTest extends TestCase
{
    public function test_user_has_required_traits(): void
    {
        $user = new User;

        $this->assertContains(HasApiTokens::class, class_uses_recursive($user));
        $this->assertContains(HasFactory::class, class_uses_recursive($user));
        $this->assertContains(Notifiable::class, class_uses_recursive($user));
    }

    public function test_password_is_hashed_when_set(): void
    {
        $user = new User;
        $plainPassword = 'password123';

        $user->password = $plainPassword;

        $this->assertNotEquals($plainPassword, $user->password);
        $this->assertTrue(password_verify($plainPassword, $user->password));
    }

    public function test_hidden_attributes_are_properly_configured(): void
    {
        $user = new User;

        $this->assertContains('password', $user->getHidden());
        $this->assertContains('remember_token', $user->getHidden());
    }

    public function test_factory_creates_valid_user(): void
    {
        $user = User::factory()->make();

        $this->assertNotEmpty($user->name);
        $this->assertNotEmpty($user->email);
        $this->assertStringContainsString('@', $user->email);
        $this->assertGreaterThan(0, strlen($user->name));
    }
}
