<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Test command for market sizing analysis
Artisan::command('test:market-sizing {project_id}', function () {
    $projectId = $this->argument('project_id');
    $this->info('Testing market sizing analysis generation...');
    
    $project = \App\Models\Project::find($projectId);
    if (!$project) {
        $this->error("Project with ID {$projectId} not found");
        return 1;
    }
    
    $this->info("Found project: {$project->input_prompt}");
    $this->info("Dispatching job for project ID: {$project->id}");
    
    try {
        \App\Jobs\GenerateMarketSizingAnalysisJob::dispatch($project);
        $this->info('✅ Job dispatched successfully!');
        $this->info('💡 Run "php artisan queue:work" to process the job');
        $this->info('💡 Run "php artisan check:market-sizing ' . $project->id . '" to see results');
        return 0;
    } catch (\Exception $e) {
        $this->error('❌ Failed to dispatch job: ' . $e->getMessage());
        return 1;
    }
})->purpose('Test market sizing analysis job dispatch');

// Command to check analyses
Artisan::command('check:market-sizing {project_id}', function () {
    $projectId = $this->argument('project_id');
    $project = \App\Models\Project::find($projectId);
    if (!$project) {
        $this->error("Project with ID {$projectId} not found");
        return 1;
    }
    
    $this->info("Checking market sizing analyses for project: {$project->id}");
    
    $contentService = app(\App\Services\ContentGenerationService::class);
    $analyses = $contentService->getMarketSizingAnalysis($project);
    
    $this->info('Found analyses: ' . count($analyses));
    
    foreach (['tam', 'sam', 'som'] as $type) {
        if (isset($analyses[$type])) {
            $analysis = $analyses[$type];
            $data = $analysis->content_data ?? [];
            $marketValue = isset($data['market_size_value']) ? $data['market_size_value'] : 'N/A';
            $confidence = isset($data['confidence_level']) ? $data['confidence_level'] : 'N/A';
            
            $this->info("✅ {$type}: {$marketValue} (Confidence: {$confidence})");
        } else {
            $this->line("⏳ {$type}: Not generated yet");
        }
    }
    
    return 0;
})->purpose('Check market sizing analysis results for a project');

// Command to check queue status
Artisan::command('check:queue', function () {
    $this->info('Queue Configuration:');
    $this->line('Default Connection: ' . config('queue.default'));
    $this->line('Driver: ' . config('queue.connections.' . config('queue.default') . '.driver'));
    
    // Check for failed jobs
    $failedJobs = \Illuminate\Support\Facades\DB::table('failed_jobs')->count();
    $this->info("Failed Jobs: {$failedJobs}");
    
    if ($failedJobs > 0) {
        $recentFailed = \Illuminate\Support\Facades\DB::table('failed_jobs')
            ->orderBy('failed_at', 'desc')
            ->limit(3)
            ->get(['id', 'queue', 'payload', 'exception', 'failed_at']);
            
        $this->warn('Recent Failed Jobs:');
        foreach ($recentFailed as $job) {
            $this->line("ID: {$job->id}, Queue: {$job->queue}, Failed: {$job->failed_at}");
        }
    }
    
    // Check for jobs table if using database queue
    if (config('queue.connections.' . config('queue.default') . '.driver') === 'database') {
        try {
            $pendingJobs = \Illuminate\Support\Facades\DB::table('jobs')->count();
            $this->info("Pending Jobs: {$pendingJobs}");
        } catch (\Exception $e) {
            $this->warn('Could not check jobs table: ' . $e->getMessage());
        }
    }
    
    return 0;
})->purpose('Check queue configuration and status');

// Command to test service directly (bypass job)
Artisan::command('test:service {project_id}', function () {
    $projectId = $this->argument('project_id');
    $this->info('Testing ContentGenerationService directly...');
    
    $project = \App\Models\Project::find($projectId);
    if (!$project) {
        $this->error("Project with ID {$projectId} not found");
        return 1;
    }
    
    try {
        $contentService = app(\App\Services\ContentGenerationService::class);
        $this->info('✅ ContentGenerationService instantiated successfully');
        
        // Test generating a single analysis
        $this->info('Testing TAM analysis generation...');
        $analysis = $contentService->generateMarketSizingAnalysisType($project, 'tam');
        
        $this->info('✅ TAM analysis generated successfully');
        $this->info("Analysis ID: {$analysis->id}");
        $this->info("Content Type: {$analysis->content_type}");
        
        $data = $analysis->content_data ?? [];
        $this->info("Market Size: " . (isset($data['market_size_value']) ? $data['market_size_value'] : 'N/A'));
        
        return 0;
    } catch (\Exception $e) {
        $this->error('❌ Service test failed: ' . $e->getMessage());
        $this->line('Stack trace: ' . $e->getTraceAsString());
        return 1;
    }
})->purpose('Test ContentGenerationService directly without queue');

// Test with food delivery app example
Artisan::command('test:food-delivery', function () {
    $this->info('Testing market sizing with food delivery app example...');
    
    try {
        // Get the first account or create one for testing
        $account = \App\Models\Account::first();
        if (!$account) {
            $this->error('No account found. Please create an account first or test with an existing project.');
            return 1;
        }
        
        // Create a test project with food delivery app idea
        $project = \App\Models\Project::create([
            'account_id' => $account->id,
            'input_prompt' => 'A food delivery app that connects customers with local restaurants, offering fast delivery, real-time tracking, and multiple payment options. The app will start in major cities and expand to suburban areas.',
            'status' => 'pending'
        ]);
        
        $this->info("Created test project ID: {$project->id}");
        
        $contentService = app(\App\Services\ContentGenerationService::class);
        
        // Generate all three analyses
        foreach (['tam', 'sam', 'som'] as $type) {
            $this->info("Generating {$type} analysis...");
            $analysis = $contentService->generateMarketSizingAnalysisType($project, $type);
            
            $data = $analysis->content_data ?? [];
            $marketValue = isset($data['market_size_value']) ? $data['market_size_value'] : 'N/A';
            $method = isset($data['calculation_method']) ? $data['calculation_method'] : 'N/A';
            $confidence = isset($data['confidence_level']) ? $data['confidence_level'] : 'N/A';
            
            $this->info("✅ {$type}: {$marketValue}");
            $this->line("   Method: {$method}");
            $this->line("   Confidence: {$confidence}");
        }
        
        // Clean up test project
        $project->delete();
        $this->info('Test completed and cleaned up');
        
        return 0;
    } catch (\Exception $e) {
        $this->error('❌ Food delivery test failed: ' . $e->getMessage());
        return 1;
    }
})->purpose('Test market sizing with food delivery app example');
