<div class="bg-white rounded-lg shadow-lg">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Landing Page Generator</h3>
                <p class="text-sm text-gray-600">Create professional landing pages from your generated content</p>
            </div>
        </div>
    </div>

    <!-- Theme Selection -->
    <div class="px-6 py-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-900 mb-4">Choose a Theme</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @foreach($this->availableThemes as $themeKey => $theme)
                <div class="relative">
                    <button
                        wire:click="changeTheme('{{ $themeKey }}')"
                        class="w-full p-4 border-2 rounded-lg text-left transition-all duration-200
                            {{ $selectedTheme === $themeKey 
                                ? 'border-blue-500 bg-blue-50' 
                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50' 
                            }}"
                    >
                        <div class="flex items-center justify-between mb-2">
                            <h5 class="font-medium text-gray-900">{{ $theme['name'] }}</h5>
                            @if($selectedTheme === $themeKey)
                                <svg class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                        <p class="text-sm text-gray-600">{{ $theme['description'] }}</p>
                    </button>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Actions -->
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex space-x-3">
                <button
                    onclick="openPreviewTab()"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    Open Preview in New Tab
                </button>
                
                <!-- Hidden link for popup-free opening -->
                <a id="preview-link" 
                   href="{{ route('landing-page.preview', ['project' => $project->id, 'theme' => $selectedTheme]) }}" 
                   target="_blank" 
                   rel="noopener noreferrer" 
                   style="display: none;">Preview</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to open preview tab using hidden link (popup-blocker friendly)
    function openPreviewTab() {
        const previewLink = document.getElementById('preview-link');
        if (previewLink) {
            previewLink.click();
        }
    }

    document.addEventListener('livewire:init', () => {
        // Listen for theme changes to update the preview link
        Livewire.on('theme-changed', (event) => {
            const previewLink = document.getElementById('preview-link');
            if (previewLink && event.theme) {
                // Update the href with the new theme
                const currentHref = previewLink.href;
                const url = new URL(currentHref);
                const pathParts = url.pathname.split('/');
                // Replace the theme part (last part of the path)
                pathParts[pathParts.length - 1] = event.theme;
                url.pathname = pathParts.join('/');
                previewLink.href = url.toString();
                console.log('Updated preview link for theme:', event.theme);
            }
        });

        // Listen for error events
        Livewire.on('preview-error', (event) => {
            alert('Error: ' + event.message);
        });
        
        Livewire.on('export-error', (event) => {
            alert('Export Error: ' + event.message);
        });
        
        Livewire.on('validation-refreshed', (event) => {
            console.log('Validation refreshed:', event);
        });

        // Keep the fallback event listener for any remaining Livewire calls
        Livewire.on('open-preview-url', (event) => {
            console.log('Fallback: Opening preview URL:', event.url);
            var newWindow = window.open(event.url, '_blank', 'noopener,noreferrer');
            if (!newWindow) {
                console.error('Fallback failed: popup blocked?');
                alert('Preview blocked by popup blocker. Please allow popups for this site.');
            }
        });
    });
</script> 