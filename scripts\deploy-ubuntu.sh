#!/bin/bash

# Venture Discovery Platform - Ubuntu Deployment Script
# This script automates the deployment process on Ubuntu systems

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="venture-discovery"
PHP_VERSION="8.2"
NODE_VERSION="20"
MYSQL_VERSION="8.0"
DOMAIN="venture-discovery.local"
PROJECT_PATH="/var/www/$PROJECT_NAME"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Function to check Ubuntu version
check_ubuntu_version() {
    if ! command_exists lsb_release; then
        print_error "This script is designed for Ubuntu systems only!"
        exit 1
    fi
    
    UBUNTU_VERSION=$(lsb_release -rs)
    if [[ $(echo "$UBUNTU_VERSION >= 20.04" | bc -l) -eq 0 ]]; then
        print_error "Ubuntu 20.04 or higher is required!"
        exit 1
    fi
    
    print_success "Ubuntu $UBUNTU_VERSION detected"
}

# Function to update system packages
update_system() {
    print_status "Updating system packages..."
    sudo apt update
    sudo apt upgrade -y
    sudo apt install -y software-properties-common curl wget gnupg2 lsb-release ca-certificates apt-transport-https
}

# Function to install PHP
install_php() {
    print_status "Installing PHP $PHP_VERSION..."
    
    # Add Ondrej PHP repository
    sudo add-apt-repository ppa:ondrej/php -y
    sudo apt update
    
    # Install PHP and extensions
    sudo apt install -y \
        php$PHP_VERSION \
        php$PHP_VERSION-fpm \
        php$PHP_VERSION-cli \
        php$PHP_VERSION-common \
        php$PHP_VERSION-mysql \
        php$PHP_VERSION-zip \
        php$PHP_VERSION-gd \
        php$PHP_VERSION-mbstring \
        php$PHP_VERSION-curl \
        php$PHP_VERSION-xml \
        php$PHP_VERSION-bcmath \
        php$PHP_VERSION-json \
        php$PHP_VERSION-intl \
        php$PHP_VERSION-redis \
        php$PHP_VERSION-imagick
    
    # Configure PHP-FPM
    sudo systemctl enable php$PHP_VERSION-fpm
    sudo systemctl start php$PHP_VERSION-fpm
    
    print_success "PHP $PHP_VERSION installed successfully"
}

# Function to install Composer
install_composer() {
    if ! command_exists composer; then
        print_status "Installing Composer..."
        curl -sS https://getcomposer.org/installer | php
        sudo mv composer.phar /usr/local/bin/composer
        sudo chmod +x /usr/local/bin/composer
        print_success "Composer installed successfully"
    else
        print_success "Composer is already installed"
    fi
}

# Function to install Node.js
install_node() {
    print_status "Installing Node.js $NODE_VERSION..."
    
    # Install NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    sudo apt install -y nodejs
    
    # Install npm and yarn
    sudo npm install -g npm@latest
    sudo npm install -g yarn
    
    print_success "Node.js $NODE_VERSION installed successfully"
}

# Function to install MySQL
install_mysql() {
    print_status "Installing MySQL $MYSQL_VERSION..."
    
    # Install MySQL server
    sudo apt install -y mysql-server mysql-client
    
    # Start and enable MySQL
    sudo systemctl enable mysql
    sudo systemctl start mysql
    
    # Secure MySQL installation
    print_warning "Please run 'sudo mysql_secure_installation' after deployment to secure your MySQL installation"
    
    print_success "MySQL installed successfully"
}

# Function to install Redis
install_redis() {
    print_status "Installing Redis..."
    
    sudo apt install -y redis-server
    
    # Configure Redis
    sudo sed -i 's/^supervised no/supervised systemd/' /etc/redis/redis.conf
    
    # Start and enable Redis
    sudo systemctl enable redis-server
    sudo systemctl start redis-server
    
    print_success "Redis installed successfully"
}

# Function to install Nginx
install_nginx() {
    print_status "Installing Nginx..."
    
    sudo apt install -y nginx
    
    # Start and enable Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    # Configure firewall
    sudo ufw allow 'Nginx Full'
    
    print_success "Nginx installed successfully"
}

# Function to install Supervisor
install_supervisor() {
    print_status "Installing Supervisor..."
    
    sudo apt install -y supervisor
    
    # Start and enable Supervisor
    sudo systemctl enable supervisor
    sudo systemctl start supervisor
    
    print_success "Supervisor installed successfully"
}

# Function to install additional tools
install_tools() {
    print_status "Installing additional tools..."
    
    sudo apt install -y \
        git \
        unzip \
        htop \
        vim \
        certbot \
        python3-certbot-nginx \
        fail2ban \
        ufw
    
    # Configure basic firewall
    sudo ufw --force enable
    sudo ufw allow ssh
    sudo ufw allow http
    sudo ufw allow https
    
    print_success "Additional tools installed successfully"
}

# Function to create project user
create_project_user() {
    print_status "Creating project user..."
    
    # Create www-data user if it doesn't exist
    if ! id "www-data" &>/dev/null; then
        sudo useradd -r -s /bin/false www-data
    fi
    
    # Add current user to www-data group
    sudo usermod -a -G www-data $USER
    
    print_success "Project user configured"
}

# Function to setup project directory
setup_project_directory() {
    print_status "Setting up project directory..."
    
    # Create project directory
    sudo mkdir -p $PROJECT_PATH
    sudo chown $USER:www-data $PROJECT_PATH
    sudo chmod 755 $PROJECT_PATH
    
    cd $PROJECT_PATH
    
    # Clone or update repository
    if [ ! -d ".git" ]; then
        print_status "Cloning repository..."
        git clone https://github.com/your-username/$PROJECT_NAME.git .
    else
        print_status "Updating repository..."
        git pull origin main
    fi
    
    # Set proper ownership and permissions
    sudo chown -R $USER:www-data $PROJECT_PATH
    sudo find $PROJECT_PATH -type f -exec chmod 644 {} \;
    sudo find $PROJECT_PATH -type d -exec chmod 755 {} \;
    
    print_success "Project directory setup completed"
}

# Function to install project dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    cd $PROJECT_PATH
    
    # Install PHP dependencies
    print_status "Installing PHP dependencies..."
    composer install --optimize-autoloader --no-dev
    
    # Install Node.js dependencies
    print_status "Installing Node.js dependencies..."
    npm ci
    
    # Build assets
    print_status "Building frontend assets..."
    npm run build
    
    print_success "Dependencies installed successfully"
}

# Function to configure environment
configure_environment() {
    print_status "Configuring environment..."
    
    cd $PROJECT_PATH
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Please update .env file with your configuration"
    fi
    
    # Generate application key
    php artisan key:generate
    
    # Create storage symlink
    php artisan storage:link
    
    # Set storage permissions
    sudo chown -R www-data:www-data storage bootstrap/cache
    sudo chmod -R 775 storage bootstrap/cache
    
    print_success "Environment configured successfully"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Create database and user
    sudo mysql -e "CREATE DATABASE IF NOT EXISTS venture_discovery;"
    sudo mysql -e "CREATE USER IF NOT EXISTS 'venture_user'@'localhost' IDENTIFIED BY 'secure_password';"
    sudo mysql -e "GRANT ALL PRIVILEGES ON venture_discovery.* TO 'venture_user'@'localhost';"
    sudo mysql -e "FLUSH PRIVILEGES;"
    
    cd $PROJECT_PATH
    
    # Run migrations
    php artisan migrate --force
    
    # Seed database (optional)
    read -p "Do you want to seed the database with sample data? (y/n): " seed_db
    if [[ $seed_db == "y" || $seed_db == "Y" ]]; then
        php artisan db:seed
    fi
    
    print_success "Database setup completed"
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Create Nginx site configuration
    sudo tee /etc/nginx/sites-available/$PROJECT_NAME > /dev/null <<EOF
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN;
    root $PROJECT_PATH/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php$PHP_VERSION-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Security headers
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";
}
EOF

    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
    
    # Remove default site
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Add to hosts file for local development
    if ! grep -q "$DOMAIN" /etc/hosts; then
        echo "127.0.0.1 $DOMAIN" | sudo tee -a /etc/hosts
    fi
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Restart Nginx
    sudo systemctl restart nginx
    
    print_success "Nginx configured successfully"
}

# Function to setup queue worker
setup_queue_worker() {
    print_status "Setting up queue worker..."
    
    # Create supervisor configuration
    sudo tee /etc/supervisor/conf.d/$PROJECT_NAME-worker.conf > /dev/null <<EOF
[program:$PROJECT_NAME-worker]
process_name=%(program_name)s_%(process_num)02d
command=php $PROJECT_PATH/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=$PROJECT_PATH/storage/logs/worker.log
stopwaitsecs=3600
EOF

    # Update supervisor
    sudo supervisorctl reread
    sudo supervisorctl update
    sudo supervisorctl start $PROJECT_NAME-worker:*
    
    print_success "Queue worker configured successfully"
}

# Function to setup SSL with Let's Encrypt
setup_ssl() {
    read -p "Do you want to setup SSL with Let's Encrypt? (y/n): " setup_ssl
    if [[ $setup_ssl == "y" || $setup_ssl == "Y" ]]; then
        read -p "Enter your domain name (e.g., yourdomain.com): " ssl_domain
        read -p "Enter your email address: " ssl_email
        
        print_status "Setting up SSL certificate..."
        sudo certbot --nginx -d $ssl_domain --email $ssl_email --agree-tos --non-interactive
        
        # Setup auto-renewal
        sudo systemctl enable certbot.timer
        sudo systemctl start certbot.timer
        
        print_success "SSL certificate configured successfully"
    fi
}

# Function to setup log rotation
setup_log_rotation() {
    print_status "Setting up log rotation..."
    
    sudo tee /etc/logrotate.d/$PROJECT_NAME > /dev/null <<EOF
$PROJECT_PATH/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        sudo supervisorctl restart $PROJECT_NAME-worker:*
    endscript
}
EOF

    print_success "Log rotation configured successfully"
}

# Function to setup monitoring
setup_monitoring() {
    print_status "Setting up basic monitoring..."
    
    # Create monitoring script
    sudo tee /usr/local/bin/venture-discovery-monitor.sh > /dev/null <<'EOF'
#!/bin/bash

# Check if services are running
services=("nginx" "mysql" "redis-server" "php8.2-fpm" "supervisor")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is not running" >> /var/log/venture-discovery-monitor.log
        systemctl restart $service
    fi
done

# Check disk space
disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    echo "$(date): Disk usage is at ${disk_usage}%" >> /var/log/venture-discovery-monitor.log
fi
EOF

    sudo chmod +x /usr/local/bin/venture-discovery-monitor.sh
    
    # Add to crontab
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/venture-discovery-monitor.sh") | crontab -
    
    print_success "Basic monitoring configured successfully"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    cd $PROJECT_PATH
    
    # Run PHP tests
    ./vendor/bin/pest
    
    # Run code quality checks
    composer check
    
    print_success "All tests passed successfully"
}

# Function to setup backup script
setup_backup() {
    print_status "Setting up backup script..."
    
    sudo tee /usr/local/bin/venture-discovery-backup.sh > /dev/null <<EOF
#!/bin/bash

BACKUP_DIR="/var/backups/venture-discovery"
DATE=\$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p \$BACKUP_DIR

# Backup database
mysqldump -u venture_user -psecure_password venture_discovery > \$BACKUP_DIR/database_\$DATE.sql

# Backup application files
tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz -C $PROJECT_PATH .

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "\$(date): Backup completed" >> /var/log/venture-discovery-backup.log
EOF

    sudo chmod +x /usr/local/bin/venture-discovery-backup.sh
    
    # Add to crontab for daily backups
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/venture-discovery-backup.sh") | crontab -
    
    print_success "Backup script configured successfully"
}

# Function to display final information
display_final_info() {
    print_success "Deployment completed successfully!"
    echo
    echo "=== Venture Discovery Platform ==="
    echo "Project Location: $PROJECT_PATH"
    echo "Local URL: http://$DOMAIN"
    echo "Admin Panel: http://$DOMAIN/admin"
    echo
    echo "=== Next Steps ==="
    echo "1. Update .env file with your OpenAI API key and other configurations"
    echo "2. Configure your email settings in .env"
    echo "3. Run 'sudo mysql_secure_installation' to secure MySQL"
    echo "4. Update database credentials in .env file"
    echo "5. Access the application at http://$DOMAIN"
    echo
    echo "=== Service Management ==="
    echo "Start services: sudo systemctl start nginx mysql redis-server php$PHP_VERSION-fpm supervisor"
    echo "Stop services: sudo systemctl stop nginx mysql redis-server php$PHP_VERSION-fpm supervisor"
    echo "Restart services: sudo systemctl restart nginx mysql redis-server php$PHP_VERSION-fpm supervisor"
    echo
    echo "=== Logs ==="
    echo "Application logs: tail -f $PROJECT_PATH/storage/logs/laravel.log"
    echo "Nginx logs: tail -f /var/log/nginx/error.log"
    echo "Queue worker status: sudo supervisorctl status"
    echo
    echo "=== Security ==="
    echo "Firewall status: sudo ufw status"
    echo "Fail2ban status: sudo fail2ban-client status"
    echo
}

# Main deployment function
main() {
    print_status "Starting Venture Discovery Platform deployment on Ubuntu..."
    echo
    
    # Pre-flight checks
    check_root
    check_ubuntu_version
    
    # System setup
    update_system
    
    # Install dependencies
    install_php
    install_composer
    install_node
    install_mysql
    install_redis
    install_nginx
    install_supervisor
    install_tools
    
    # Project setup
    create_project_user
    setup_project_directory
    install_dependencies
    configure_environment
    setup_database
    configure_nginx
    setup_queue_worker
    setup_ssl
    setup_log_rotation
    setup_monitoring
    setup_backup
    
    # Run tests
    run_tests
    
    # Display final information
    display_final_info
}

# Run main function
main "$@" 