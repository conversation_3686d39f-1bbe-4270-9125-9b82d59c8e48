<?php

namespace App\Jobs;

use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateCriticalHypotheses implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Project $project,
        public ?string $hypothesisType = null,
        public bool $isRegeneration = false
    ) {
        $this->onQueue('default');
    }

    public function handle(ContentGenerationService $contentService): void
    {
        try {
            if ($this->hypothesisType) {
                // Generate single hypothesis
                if ($this->isRegeneration) {
                    $hypothesis = $contentService->regenerateCriticalHypothesis($this->project, $this->hypothesisType);
                    Log::info('Successfully regenerated critical hypothesis', [
                        'project_id' => $this->project->id,
                        'hypothesis_type' => $this->hypothesisType,
                    ]);
                } else {
                    $hypothesis = $contentService->generateCriticalHypothesis($this->project, $this->hypothesisType);
                    Log::info('Successfully generated critical hypothesis', [
                        'project_id' => $this->project->id,
                        'hypothesis_type' => $this->hypothesisType,
                    ]);
                }
            } else {
                // Generate all hypotheses
                $hypotheses = $contentService->generateCriticalHypotheses($this->project);
                Log::info('Successfully generated all critical hypotheses', [
                    'project_id' => $this->project->id,
                    'generated_count' => count($hypotheses),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to generate critical hypotheses', [
                'project_id' => $this->project->id,
                'hypothesis_type' => $this->hypothesisType,
                'is_regeneration' => $this->isRegeneration,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Critical hypotheses generation job failed', [
            'project_id' => $this->project->id,
            'hypothesis_type' => $this->hypothesisType,
            'is_regeneration' => $this->isRegeneration,
            'error' => $exception->getMessage(),
        ]);
    }
}
