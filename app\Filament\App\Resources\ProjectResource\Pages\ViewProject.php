<?php

namespace App\Filament\App\Resources\ProjectResource\Pages;

use App\Filament\App\Resources\ProjectResource;
use App\Jobs\GenerateCriticalHypotheses;
use App\Jobs\GenerateInterviewQuestionnaireJob;
use App\Jobs\GenerateLeanCanvasSection;
use App\Jobs\GenerateMarketSizingAnalysisJob;
use App\Jobs\GenerateStorytellingContentJob;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewProject extends ViewRecord
{
    protected static string $resource = ProjectResource::class;

    // Define all Lean Canvas sections
    public array $sectionConfig = [
        'problem' => [
            'title' => 'Problem',
            'key' => 'B',
            'content_type' => 'lean_canvas_problem',
            'description' => 'Top 1-3 problems',
        ],
        'solution' => [
            'title' => 'Solution',
            'key' => 'D',
            'content_type' => 'lean_canvas_solution',
            'description' => 'Top 3 features',
        ],
        'unique_value_proposition' => [
            'title' => 'Unique Value Prop.',
            'key' => 'E',
            'content_type' => 'lean_canvas_unique_value_proposition',
            'description' => 'Single, clear message',
        ],
        'unfair_advantage' => [
            'title' => 'Unfair Advantage',
            'key' => 'J',
            'content_type' => 'lean_canvas_unfair_advantage',
            'description' => "Can't be copied",
        ],
        'customer_segments' => [
            'title' => 'Customer Segments',
            'key' => 'A',
            'content_type' => 'lean_canvas_customer_segments',
            'description' => 'Target customers',
        ],
        'existing_alternatives' => [
            'title' => 'Existing Alternatives',
            'key' => 'C',
            'content_type' => 'lean_canvas_existing_alternatives',
            'description' => 'List how these problems are solved today',
        ],
        'key_metrics' => [
            'title' => 'Key Metrics',
            'key' => 'I',
            'content_type' => 'lean_canvas_key_metrics',
            'description' => 'Key numbers that tell you how your business is doing',
        ],
        'channels' => [
            'title' => 'Channels',
            'key' => 'F',
            'content_type' => 'lean_canvas_channels',
            'description' => 'Path to customers',
        ],
        'cost_structure' => [
            'title' => 'Cost Structure',
            'key' => 'H',
            'content_type' => 'lean_canvas_cost_structure',
            'description' => 'Customer acquisition costs, distribution costs, hosting, people, etc.',
        ],
        'revenue_streams' => [
            'title' => 'Revenue Streams',
            'key' => 'G',
            'content_type' => 'lean_canvas_revenue_streams',
            'description' => 'Revenue model, life time value, revenue, gross margin',
        ],
    ];

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Project Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('id')
                            ->label('Project ID')
                            ->badge()
                            ->color('primary'),

                        Infolists\Components\TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'pending' => 'warning',
                                'processing' => 'info',
                                'completed' => 'success',
                                'failed' => 'danger',
                                default => 'gray',
                            })
                            ->icon(fn (string $state): string => match ($state) {
                                'pending' => 'heroicon-o-clock',
                                'processing' => 'heroicon-o-arrow-path',
                                'completed' => 'heroicon-o-check-circle',
                                'failed' => 'heroicon-o-x-circle',
                                default => 'heroicon-o-question-mark-circle',
                            }),

                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime()
                            ->since(),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime()
                            ->since(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Project Description')
                    ->schema([
                        Infolists\Components\TextEntry::make('input_prompt')
                            ->label('')
                            ->prose()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Lean Canvas')
                    ->schema([
                        Infolists\Components\Livewire::make(
                            \App\Livewire\LeanCanvasDisplay::class,
                            fn ($record) => ['record' => $record]
                        )
                            ->columnSpanFull(),
                    ])
                    ->footerActions([
                        Infolists\Components\Actions\Action::make('generateAllSections')->label('Generate All Sections')->icon('heroicon-o-arrow-path')->requiresConfirmation()->action(fn () => $this->generateAllSections()),
                    ])
                    ->label('Generate All Sections')
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('AI-generated Lean Canvas sections for your startup idea'),

                Infolists\Components\Section::make('Critical Hypotheses')
                    ->schema([
                        Infolists\Components\ViewEntry::make('critical_hypotheses')
                            ->view('components.critical-hypotheses-section')
                            ->viewData(fn ($record) => ['project' => $record])
                            ->columnSpanFull(),
                    ])
                    ->footerActions([
                        Infolists\Components\Actions\Action::make('generateCriticalHypotheses')
                            ->label('Generate Critical Hypotheses')
                            ->icon('heroicon-o-arrow-path')
                            ->requiresConfirmation()
                            ->action(fn () => $this->generateCriticalHypotheses()),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('Test the fundamental assumptions about your business idea'),

                Infolists\Components\Section::make('Customer Interview Questionnaire')
                    ->schema([
                        Infolists\Components\ViewEntry::make('interview_questionnaire')
                            ->view('components.interview-questionnaire-section')
                            ->viewData(fn ($record) => ['project' => $record])
                            ->columnSpanFull(),
                    ])
                    ->footerActions([
                        Infolists\Components\Actions\Action::make('generateInterviewQuestionnaire')
                            ->label('Generate Interview Questionnaire')
                            ->icon('heroicon-o-arrow-path')
                            ->requiresConfirmation()
                            ->action(fn () => $this->generateInterviewQuestionnaire()),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('Structured questions to validate your business assumptions through customer interviews'),

                Infolists\Components\Section::make('Storytelling Central')
                    ->schema([
                        Infolists\Components\ViewEntry::make('storytelling_central')
                            ->view('components.storytelling-central-section')
                            ->viewData(fn ($record) => ['project' => $record])
                            ->columnSpanFull(),
                    ])
                    ->footerActions([
                        Infolists\Components\Actions\Action::make('generateStorytellingContent')
                            ->label('Generate All Storytelling Content')
                            ->icon('heroicon-o-arrow-path')
                            ->requiresConfirmation()
                            ->action(fn () => $this->generateStorytellingContent()),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('Brand wheel, startup naming suggestions, and elevator pitch for your venture'),

                Infolists\Components\Section::make('Market Sizing Analysis')
                    ->schema([
                        Infolists\Components\Livewire::make(
                            \App\Livewire\MarketSizingAnalysisDisplay::class,
                            fn ($record) => ['project' => $record]
                        )
                            ->columnSpanFull(),
                    ])
                    ->footerActions([
                        Infolists\Components\Actions\Action::make('generateMarketSizingAnalysis')
                            ->label('Generate Market Sizing Analysis')
                            ->icon('heroicon-o-arrow-path')
                            ->requiresConfirmation()
                            ->action(fn () => $this->generateMarketSizingAnalysis()),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('Comprehensive TAM, SAM, and SOM analysis to understand your market opportunity and size'),

                Infolists\Components\Section::make('Landing Page Generator')
                    ->schema([
                        Infolists\Components\Livewire::make(
                            \App\Livewire\LandingPageGenerator::class,
                            fn ($record) => ['project' => $record]
                        )
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->description('Generate professional landing pages from your startup content with multiple themes and export capabilities'),
            ]);
    }

    public function generateAllSections()
    {
        /** @var \App\Models\Project $project */
        $project = $this->record;

        foreach ($this->sectionConfig as $sectionKey => $config) {
            // Check if content already exists
            $existingContent = $project->generatedContents()
                ->where('content_type', $config['content_type'])
                ->latest()
                ->first();

            if (! $existingContent) {
                $this->generateSection($sectionKey);
            }
        }
    }

    public function generateSection($sectionKey)
    {
        if (! isset($this->sectionConfig[$sectionKey])) {
            return;
        }

        /** @var \App\Models\Project $project */
        $project = $this->record;

        // Check if content already exists
        $existingContent = $project->generatedContents()
            ->where('content_type', $this->sectionConfig[$sectionKey]['content_type'])
            ->latest()
            ->first();

        if ($existingContent) {
            return;
        }

        try {
            // Dispatch the job for background processing
            GenerateLeanCanvasSection::dispatch(
                $project,
                $sectionKey,
                false // isRegeneration = false
            );

        } catch (\Exception $e) {
        }
    }

    public function generateCriticalHypotheses()
    {
        /** @var \App\Models\Project $project */
        $project = $this->record;

        try {
            // Dispatch the job for background processing
            GenerateCriticalHypotheses::dispatch($project);

        } catch (\Exception $e) {
        }
    }

    public function generateInterviewQuestionnaire()
    {
        /** @var \App\Models\Project $project */
        $project = $this->record;

        try {
            // Dispatch the job for background processing
            GenerateInterviewQuestionnaireJob::dispatch($project, false);

        } catch (\Exception $e) {
        }
    }

    public function generateStorytellingContent()
    {
        /** @var \App\Models\Project $project */
        $project = $this->record;

        try {
            // Dispatch the job for background processing
            GenerateStorytellingContentJob::dispatch($project, false);

        } catch (\Exception $e) {
        }
    }

    public function generateMarketSizingAnalysis()
    {
        /** @var \App\Models\Project $project */
        $project = $this->record;

        try {
            // Validate project
            if (!$project || !$project->id) {
                throw new \Exception('Invalid project provided');
            }

            if (empty(trim($project->input_prompt))) {
                throw new \Exception('Project input prompt is required for market sizing analysis');
            }

            // Dispatch the job for background processing
            GenerateMarketSizingAnalysisJob::dispatch($project);

            // Add user notification
            \Filament\Notifications\Notification::make()
                ->title('Market Sizing Analysis Started')
                ->body('Analysis generation is running in the background. Refresh the page in a few moments to see results.')
                ->success()
                ->duration(5000)
                ->send();

        } catch (\Exception $e) {
            // Show user-friendly error notification
            \Filament\Notifications\Notification::make()
                ->title('Generation Failed')
                ->body('Failed to start market sizing analysis: ' . $e->getMessage())
                ->danger()
                ->duration(8000)
                ->send();
            
            // Re-throw the exception so Filament can handle it properly
            throw $e;
        }
    }
}
