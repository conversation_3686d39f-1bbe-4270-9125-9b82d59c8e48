<?php

namespace Database\Factories;

use App\Models\GeneratedContent;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GeneratedContent>
 */
class GeneratedContentFactory extends Factory
{
    protected $model = GeneratedContent::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $contentTypes = [
            'lean_canvas_problem',
            'lean_canvas_solution',
            'lean_canvas_unique_value_proposition',
            'lean_canvas_unfair_advantage',
            'lean_canvas_customer_segments',
            'lean_canvas_existing_alternatives',
            'lean_canvas_key_metrics',
            'lean_canvas_channels',
            'lean_canvas_cost_structure',
            'lean_canvas_revenue_streams',
        ];

        $contentType = $this->faker->randomElement($contentTypes);
        $contentKey = $this->getContentKey($contentType);

        return [
            'project_id' => Project::factory(),
            'content_type' => $contentType,
            'content_data' => [
                $contentKey => $this->faker->paragraphs(3, true),
                'generated_at' => now()->toISOString(),
                'model_used' => 'claude-3-sonnet',
                'token_count' => $this->faker->numberBetween(100, 500),
                'prompt_used' => $this->faker->sentence(),
            ],
        ];
    }

    /**
     * Create content for a specific content type
     */
    public function forContentType(string $contentType): static
    {
        return $this->state(function (array $attributes) use ($contentType) {
            $contentKey = $this->getContentKey($contentType);

            return [
                'content_type' => $contentType,
                'content_data' => [
                    $contentKey => $this->getContentForType($contentType),
                    'generated_at' => now()->toISOString(),
                    'model_used' => 'claude-3-sonnet',
                    'token_count' => $this->faker->numberBetween(100, 500),
                    'prompt_used' => $this->faker->sentence(),
                ],
            ];
        });
    }

    /**
     * Create content for a specific project
     */
    public function forProject(Project $project): static
    {
        return $this->state(function (array $attributes) use ($project) {
            return [
                'project_id' => $project->id,
            ];
        });
    }

    /**
     * Create problem section content
     */
    public function problemSection(): static
    {
        return $this->forContentType('lean_canvas_problem');
    }

    /**
     * Create solution section content
     */
    public function solutionSection(): static
    {
        return $this->forContentType('lean_canvas_solution');
    }

    /**
     * Create unique value proposition section content
     */
    public function uniqueValuePropositionSection(): static
    {
        return $this->forContentType('lean_canvas_unique_value_proposition');
    }

    /**
     * Get the appropriate content key for storing in content_data
     */
    protected function getContentKey(string $contentType): string
    {
        $keyMap = [
            'lean_canvas_problem' => 'problem',
            'lean_canvas_solution' => 'solution',
            'lean_canvas_unique_value_proposition' => 'unique_value_proposition',
            'lean_canvas_unfair_advantage' => 'unfair_advantage',
            'lean_canvas_customer_segments' => 'customer_segments',
            'lean_canvas_existing_alternatives' => 'existing_alternatives',
            'lean_canvas_key_metrics' => 'key_metrics',
            'lean_canvas_channels' => 'channels',
            'lean_canvas_cost_structure' => 'cost_structure',
            'lean_canvas_revenue_streams' => 'revenue_streams',
        ];

        return $keyMap[$contentType] ?? 'content';
    }

    /**
     * Get realistic content for a specific content type
     */
    protected function getContentForType(string $contentType): string
    {
        return match ($contentType) {
            'lean_canvas_problem' => $this->faker->paragraphs(2, true).' This represents a significant pain point for customers.',
            'lean_canvas_solution' => 'Our solution addresses the core problems through: '.$this->faker->sentence(),
            'lean_canvas_unique_value_proposition' => $this->faker->sentence().' - making it unique in the market.',
            'lean_canvas_unfair_advantage' => 'Our unfair advantage includes: '.$this->faker->words(5, true),
            'lean_canvas_customer_segments' => 'Target customers: '.$this->faker->words(8, true),
            'lean_canvas_existing_alternatives' => 'Current alternatives include: '.$this->faker->sentence(),
            'lean_canvas_key_metrics' => 'Key metrics to track: '.$this->faker->words(6, true),
            'lean_canvas_channels' => 'Distribution channels: '.$this->faker->words(5, true),
            'lean_canvas_cost_structure' => 'Main costs: '.$this->faker->words(7, true),
            'lean_canvas_revenue_streams' => 'Revenue model: '.$this->faker->sentence(),
            default => $this->faker->paragraphs(2, true),
        };
    }
}
