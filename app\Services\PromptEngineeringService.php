<?php

namespace App\Services;

class PromptEngineeringService
{
    /**
     * Generate a comprehensive business plan prompt
     */
    public function buildBusinessPlanPrompt(string $startupIdea, array $options = []): string
    {
        $sections = $options['sections'] ?? [
            'executive_summary',
            'market_analysis',
            'competitive_analysis',
            'marketing_strategy',
            'operations_plan',
            'financial_projections',
            'risk_analysis',
        ];

        $prompt = "As an experienced business strategist and startup advisor, create a comprehensive business plan for the following startup idea:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n\n";
        $prompt .= "Please provide detailed analysis and recommendations for the following sections:\n\n";

        $sectionDescriptions = [
            'executive_summary' => 'Executive Summary: A concise overview of the business concept, target market, competitive advantage, and financial highlights.',
            'market_analysis' => 'Market Analysis: Detailed analysis of the target market size, growth trends, customer segments, and market opportunities.',
            'competitive_analysis' => 'Competitive Analysis: Identification of direct and indirect competitors, their strengths/weaknesses, and your competitive positioning.',
            'marketing_strategy' => 'Marketing & Sales Strategy: Customer acquisition strategies, pricing model, distribution channels, and marketing tactics.',
            'operations_plan' => 'Operations Plan: Business model, key processes, technology requirements, and operational structure.',
            'financial_projections' => 'Financial Projections: Revenue model, cost structure, funding requirements, and 3-year financial forecasts.',
            'risk_analysis' => 'Risk Analysis: Key business risks, mitigation strategies, and contingency plans.',
        ];

        foreach ($sections as $section) {
            if (isset($sectionDescriptions[$section])) {
                $prompt .= "- {$sectionDescriptions[$section]}\n";
            }
        }

        $prompt .= "\nFor each section, provide:\n";
        $prompt .= "- Specific, actionable insights\n";
        $prompt .= "- Data-driven recommendations where possible\n";
        $prompt .= "- Industry best practices\n";
        $prompt .= "- Realistic timelines and milestones\n\n";
        $prompt .= 'Format the response in clear sections with headers and bullet points for easy reading.';

        return $prompt;
    }

    /**
     * Generate a Lean Canvas prompt
     *
     * @return array Structure for Lean Canvas
     */
    public function buildLeanCanvasStructure(string $startupIdea): array
    {
        return [
            'problem' => 'List the top 3 problems your startup solves',
            'solution' => 'Outline your solution and key features',
            'unique_value_proposition' => 'Single, clear, compelling message that states why you are different and worth buying',
            'unfair_advantage' => 'Something that cannot be easily copied or bought',
            'customer_segments' => 'Target customers and early adopters',
            'key_metrics' => 'Key numbers that tell you how your business is doing',
            'channels' => 'Path to customers (how you reach them)',
            'cost_structure' => 'All the costs incurred to operate your business model',
            'revenue_streams' => 'How your business makes money',
        ];
    }

    /**
     * Generate a market analysis prompt
     */
    public function buildMarketAnalysisPrompt(string $startupIdea, string $industry = '', string $targetMarket = ''): string
    {
        $prompt = "As a market research expert, conduct a comprehensive market analysis for this startup idea:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n";

        if ($industry) {
            $prompt .= "**Industry:** {$industry}\n";
        }

        if ($targetMarket) {
            $prompt .= "**Target Market:** {$targetMarket}\n";
        }

        $prompt .= "\nProvide detailed analysis covering:\n\n";
        $prompt .= "1. **Market Size & Growth**\n";
        $prompt .= "   - Total Addressable Market (TAM)\n";
        $prompt .= "   - Serviceable Addressable Market (SAM)\n";
        $prompt .= "   - Serviceable Obtainable Market (SOM)\n";
        $prompt .= "   - Market growth rate and trends\n\n";

        $prompt .= "2. **Customer Segmentation**\n";
        $prompt .= "   - Primary customer segments\n";
        $prompt .= "   - Customer personas and demographics\n";
        $prompt .= "   - Customer pain points and needs\n";
        $prompt .= "   - Buying behavior and decision factors\n\n";

        $prompt .= "3. **Market Trends & Drivers**\n";
        $prompt .= "   - Industry trends affecting the market\n";
        $prompt .= "   - Technology trends and disruptions\n";
        $prompt .= "   - Regulatory and economic factors\n";
        $prompt .= "   - Seasonal or cyclical patterns\n\n";

        $prompt .= "4. **Market Entry Strategy**\n";
        $prompt .= "   - Barriers to entry\n";
        $prompt .= "   - Go-to-market strategy recommendations\n";
        $prompt .= "   - Timing considerations\n";
        $prompt .= "   - Geographic expansion opportunities\n\n";

        $prompt .= 'Provide specific data points, statistics, and actionable insights where possible.';

        return $prompt;
    }

    /**
     * Generate a competitive analysis prompt
     */
    public function buildCompetitiveAnalysisPrompt(string $startupIdea, array $competitors = []): string
    {
        $prompt = "As a competitive intelligence analyst, conduct a thorough competitive analysis for this startup:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n\n";

        if (! empty($competitors)) {
            $prompt .= '**Known Competitors:** '.implode(', ', $competitors)."\n\n";
        }

        $prompt .= "Provide comprehensive competitive analysis including:\n\n";
        $prompt .= "1. **Direct Competitors**\n";
        $prompt .= "   - Companies offering similar solutions\n";
        $prompt .= "   - Their market position and market share\n";
        $prompt .= "   - Strengths and weaknesses\n";
        $prompt .= "   - Pricing strategies\n\n";

        $prompt .= "2. **Indirect Competitors**\n";
        $prompt .= "   - Alternative solutions customers might choose\n";
        $prompt .= "   - Substitute products or services\n";
        $prompt .= "   - Traditional methods being replaced\n\n";

        $prompt .= "3. **Competitive Positioning**\n";
        $prompt .= "   - Unique value proposition vs competitors\n";
        $prompt .= "   - Differentiation opportunities\n";
        $prompt .= "   - Competitive advantages to leverage\n";
        $prompt .= "   - Potential competitive threats\n\n";

        $prompt .= "4. **Market Gaps & Opportunities**\n";
        $prompt .= "   - Underserved market segments\n";
        $prompt .= "   - Feature gaps in existing solutions\n";
        $prompt .= "   - Emerging opportunities\n\n";

        $prompt .= "5. **Strategic Recommendations**\n";
        $prompt .= "   - Positioning strategy\n";
        $prompt .= "   - Competitive response strategies\n";
        $prompt .= "   - Areas to avoid direct competition\n";
        $prompt .= "   - Partnership opportunities\n\n";

        $prompt .= 'Format as a structured analysis with clear sections and actionable insights.';

        return $prompt;
    }

    /**
     * Generate a financial projections prompt
     */
    public function buildFinancialProjectionsPrompt(string $startupIdea, array $options = []): string
    {
        $timeframe = $options['timeframe'] ?? '3 years';
        $businessModel = $options['business_model'] ?? '';

        $prompt = "As a financial analyst specializing in startups, create detailed financial projections for this business:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n";

        if ($businessModel) {
            $prompt .= "**Business Model:** {$businessModel}\n";
        }

        $prompt .= "**Projection Period:** {$timeframe}\n\n";

        $prompt .= "Provide comprehensive financial projections including:\n\n";
        $prompt .= "1. **Revenue Model & Projections**\n";
        $prompt .= "   - Revenue streams identification\n";
        $prompt .= "   - Pricing strategy and unit economics\n";
        $prompt .= "   - Customer acquisition and retention rates\n";
        $prompt .= "   - Monthly/quarterly revenue projections\n\n";

        $prompt .= "2. **Cost Structure Analysis**\n";
        $prompt .= "   - Fixed costs (rent, salaries, software, etc.)\n";
        $prompt .= "   - Variable costs (materials, transaction fees, etc.)\n";
        $prompt .= "   - Customer acquisition costs (CAC)\n";
        $prompt .= "   - Operating expense projections\n\n";

        $prompt .= "3. **Key Financial Metrics**\n";
        $prompt .= "   - Gross margin and contribution margin\n";
        $prompt .= "   - Customer Lifetime Value (CLV)\n";
        $prompt .= "   - CAC payback period\n";
        $prompt .= "   - Monthly recurring revenue (if applicable)\n";
        $prompt .= "   - Break-even analysis\n\n";

        $prompt .= "4. **Funding Requirements**\n";
        $prompt .= "   - Initial capital requirements\n";
        $prompt .= "   - Working capital needs\n";
        $prompt .= "   - Growth capital requirements\n";
        $prompt .= "   - Funding timeline and milestones\n\n";

        $prompt .= "5. **Financial Statements**\n";
        $prompt .= "   - Projected income statement\n";
        $prompt .= "   - Cash flow projections\n";
        $prompt .= "   - Key assumptions and sensitivities\n\n";

        $prompt .= 'Present projections in a clear, tabular format with monthly/quarterly breakdowns and include key assumptions.';

        return $prompt;
    }

    /**
     * Generate a marketing strategy prompt
     */
    public function buildMarketingStrategyPrompt(string $startupIdea, array $targetAudience = [], int $budget = 0): string
    {
        $prompt = "As a marketing strategist specializing in startups, develop a comprehensive marketing strategy for:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n";

        if (! empty($targetAudience)) {
            $prompt .= '**Target Audience:** '.implode(', ', $targetAudience)."\n";
        }

        if ($budget > 0) {
            $prompt .= '**Marketing Budget:** $'.number_format($budget)."\n";
        }

        $prompt .= "\nDevelop a detailed marketing strategy covering:\n\n";
        $prompt .= "1. **Brand Positioning & Messaging**\n";
        $prompt .= "   - Brand identity and value proposition\n";
        $prompt .= "   - Key messaging for different audiences\n";
        $prompt .= "   - Brand voice and personality\n\n";

        $prompt .= "2. **Customer Acquisition Strategy**\n";
        $prompt .= "   - Customer acquisition channels\n";
        $prompt .= "   - Channel-specific strategies and tactics\n";
        $prompt .= "   - Customer journey mapping\n";
        $prompt .= "   - Conversion optimization strategies\n\n";

        $prompt .= "3. **Digital Marketing Plan**\n";
        $prompt .= "   - Content marketing strategy\n";
        $prompt .= "   - Social media marketing\n";
        $prompt .= "   - Search engine optimization (SEO)\n";
        $prompt .= "   - Paid advertising (PPC, social ads)\n";
        $prompt .= "   - Email marketing campaigns\n\n";

        $prompt .= "4. **Growth Hacking Tactics**\n";
        $prompt .= "   - Viral marketing opportunities\n";
        $prompt .= "   - Referral programs\n";
        $prompt .= "   - Partnership marketing\n";
        $prompt .= "   - Community building strategies\n\n";

        $prompt .= "5. **Marketing Calendar & Budget**\n";
        $prompt .= "   - 90-day marketing calendar\n";
        $prompt .= "   - Budget allocation by channel\n";
        $prompt .= "   - Key performance indicators (KPIs)\n";
        $prompt .= "   - Success metrics and tracking\n\n";

        $prompt .= 'Provide specific, actionable tactics with timelines and expected outcomes.';

        return $prompt;
    }

    /**
     * Generate a risk analysis prompt
     */
    public function buildRiskAnalysisPrompt(string $startupIdea, string $industry = ''): string
    {
        $prompt = "As a risk management consultant, conduct a comprehensive risk analysis for this startup:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n";

        if ($industry) {
            $prompt .= "**Industry:** {$industry}\n";
        }

        $prompt .= "\nIdentify and analyze potential risks across all business areas:\n\n";
        $prompt .= "1. **Market Risks**\n";
        $prompt .= "   - Market size and demand risks\n";
        $prompt .= "   - Competitive threats\n";
        $prompt .= "   - Economic and industry downturns\n";
        $prompt .= "   - Customer concentration risks\n\n";

        $prompt .= "2. **Operational Risks**\n";
        $prompt .= "   - Technology and system failures\n";
        $prompt .= "   - Supply chain disruptions\n";
        $prompt .= "   - Key personnel dependencies\n";
        $prompt .= "   - Quality control issues\n\n";

        $prompt .= "3. **Financial Risks**\n";
        $prompt .= "   - Cash flow and liquidity risks\n";
        $prompt .= "   - Funding and investment risks\n";
        $prompt .= "   - Currency and interest rate risks\n";
        $prompt .= "   - Credit and collection risks\n\n";

        $prompt .= "4. **Legal & Regulatory Risks**\n";
        $prompt .= "   - Compliance and regulatory changes\n";
        $prompt .= "   - Intellectual property risks\n";
        $prompt .= "   - Data privacy and security\n";
        $prompt .= "   - Contractual and liability risks\n\n";

        $prompt .= "5. **Strategic Risks**\n";
        $prompt .= "   - Technology disruption\n";
        $prompt .= "   - Partnership and alliance risks\n";
        $prompt .= "   - Reputation and brand risks\n";
        $prompt .= "   - Scaling and growth challenges\n\n";

        $prompt .= "For each risk category, provide:\n";
        $prompt .= "- Risk probability and impact assessment\n";
        $prompt .= "- Specific mitigation strategies\n";
        $prompt .= "- Contingency plans\n";
        $prompt .= "- Early warning indicators\n";
        $prompt .= "- Risk monitoring recommendations\n\n";

        $prompt .= 'Prioritize risks by severity and provide actionable risk management recommendations.';

        return $prompt;
    }

    /**
     * Build a system prompt for business advisory context
     */
    public function buildSystemPrompt(string $role = 'business_strategist'): string
    {
        $prompts = [
            'business_strategist' => 'You are an experienced business strategist and startup advisor with 15+ years of experience helping entrepreneurs build successful companies. You have deep expertise in business planning, market analysis, competitive strategy, and growth tactics. You provide practical, actionable advice based on proven business principles and real-world experience. Your responses are detailed, well-structured, and include specific recommendations with clear reasoning.',

            'financial_analyst' => 'You are a senior financial analyst specializing in startup financial modeling and projections. You have extensive experience in venture capital, financial planning, and business valuation. You create realistic financial models based on industry benchmarks and provide detailed analysis of unit economics, cash flow, and funding requirements. Your projections are conservative yet optimistic, grounded in market data and comparable company analysis.',

            'marketing_expert' => 'You are a growth marketing expert with proven experience scaling startups from zero to millions in revenue. You specialize in customer acquisition, digital marketing, brand building, and growth hacking. You understand modern marketing channels, conversion optimization, and customer lifecycle management. Your strategies are data-driven, cost-effective, and focused on sustainable growth.',

            'market_researcher' => 'You are a market research expert with deep experience in industry analysis, competitive intelligence, and market sizing. You have access to extensive market data and understand how to analyze market trends, customer behavior, and competitive dynamics. Your research is thorough, data-driven, and provides actionable insights for business decision-making.',

            'risk_analyst' => 'You are a risk management consultant specializing in startup and small business risk assessment. You have extensive experience identifying, analyzing, and mitigating business risks across various industries. You provide comprehensive risk analysis with practical mitigation strategies and contingency planning. Your assessments are thorough, realistic, and help businesses prepare for potential challenges.',

            'brand_strategist' => 'You are a brand strategist and creative director with 12+ years of experience building memorable brands for startups and scale-ups. You specialize in brand identity development, naming strategies, storytelling, and brand positioning. You understand how to create cohesive brand experiences that resonate with target audiences and differentiate companies in competitive markets. Your approach combines strategic thinking with creative execution, ensuring brands are both meaningful and marketable.',
        ];

        return $prompts[$role] ?? $prompts['business_strategist'];
    }

    /**
     * Generate an interview questionnaire prompt for customer validation
     */
    public function buildInterviewQuestionnairePrompt(string $startupIdea): string
    {
        $prompt = "As a customer development expert and startup advisor, create a comprehensive customer interview questionnaire for this startup idea:\n\n";
        $prompt .= "**Startup Idea:** {$startupIdea}\n\n";

        $prompt .= "Create a structured interview questionnaire that will help validate key assumptions and gather insights about:\n\n";
        $prompt .= "1. **Customer Problems & Pain Points**\n";
        $prompt .= "   - Current challenges and frustrations\n";
        $prompt .= "   - Existing solutions they use\n";
        $prompt .= "   - Severity and frequency of problems\n\n";

        $prompt .= "2. **Solution Validation**\n";
        $prompt .= "   - Interest in proposed solution\n";
        $prompt .= "   - Feature preferences and priorities\n";
        $prompt .= "   - Willingness to pay and pricing sensitivity\n\n";

        $prompt .= "3. **Customer Behavior & Context**\n";
        $prompt .= "   - Current workflows and processes\n";
        $prompt .= "   - Decision-making criteria\n";
        $prompt .= "   - Preferred communication channels\n\n";

        $prompt .= "The questionnaire should include:\n";
        $prompt .= "- A warm, professional introduction explaining the purpose\n";
        $prompt .= "- 15-20 open-ended questions that encourage detailed responses\n";
        $prompt .= "- Questions that avoid leading the interviewee toward desired answers\n";
        $prompt .= "- A mix of problem exploration, solution validation, and behavioral questions\n";
        $prompt .= "- A conclusion that thanks the participant and explains next steps\n\n";

        $prompt .= 'Format the response as a structured questionnaire with clear sections and numbered questions.';

        return $prompt;
    }

    /**
     * Optimize a prompt to fit within token limits while preserving key information
     */
    public function optimizePrompt(string $prompt, int $maxTokens = 2000): string
    {
        // Simple optimization: truncate if too long
        // In a real implementation, you might use more sophisticated techniques
        $estimatedTokens = strlen($prompt) / 4; // Rough estimate: 4 chars per token

        if ($estimatedTokens <= $maxTokens) {
            return $prompt;
        }

        // Truncate to approximate token limit
        $maxChars = $maxTokens * 4;

        return substr($prompt, 0, $maxChars).'...';
    }

    /**
     * Generate a Brand Wheel prompt for storytelling central
     */
    public function buildBrandWheelPrompt(string $startupIdea): string
    {
        return "As a brand strategist, create a comprehensive Brand Wheel for this startup idea:\n\n".
            "**Startup Idea:** {$startupIdea}\n\n".
            "Generate a Brand Wheel that includes:\n\n".
            "1. **Mission Statement**: What the company exists to do (purpose)\n".
            "2. **Vision Statement**: Where the company wants to be in the future\n".
            "3. **Core Values**: 3-5 fundamental beliefs that guide decisions\n".
            "4. **Brand Personality**: Human characteristics that describe the brand\n".
            "5. **Tone of Voice**: How the brand communicates (professional, friendly, innovative, etc.)\n".
            "6. **Brand Promise**: The unique value proposition and commitment to customers\n\n".
            "Make each element:\n".
            "- Authentic and aligned with the startup's purpose\n".
            "- Memorable and distinctive\n".
            "- Actionable for marketing and communication\n".
            "- Consistent across all elements\n\n".
            'Focus on creating a cohesive brand identity that will resonate with the target audience and differentiate from competitors.';
    }

    /**
     * Generate a Startup Naming prompt
     */
    public function buildStartupNamingPrompt(string $startupIdea): string
    {
        return "As a brand strategist specializing in startup naming, generate creative and strategic name suggestions for this startup:\n\n".
            "**Startup Idea:** {$startupIdea}\n\n".
            "Provide 8-12 name suggestions across different naming strategies:\n\n".
            "**Naming Categories to Include:**\n".
            "- Descriptive names (clearly describe what the company does)\n".
            "- Abstract/invented names (unique, brandable words)\n".
            "- Metaphorical names (evoke the brand's essence)\n".
            "- Compound names (combine relevant words)\n".
            "- Acronym-based names (if appropriate)\n\n".
            "For each name suggestion, provide:\n".
            "1. **Name**: The suggested company name\n".
            "2. **Rationale**: Why this name works for the startup\n".
            "3. **Domain Considerations**: Likely .com availability assessment\n".
            "4. **Trademark Risk**: Potential trademark conflicts (high/medium/low risk)\n\n".
            "Consider:\n".
            "- Easy to pronounce and spell\n".
            "- Memorable and distinctive\n".
            "- Scalable as the company grows\n".
            "- Positive associations and meanings\n".
            "- International considerations (if applicable)\n".
            "- Social media handle availability\n\n".
            'Avoid names that are too generic, hard to pronounce, or have negative connotations.';
    }

    /**
     * Generate an Elevator Pitch prompt
     */
    public function buildElevatorPitchPrompt(string $startupIdea): string
    {
        return "As a business strategist and pitch expert, create compelling elevator pitches for this startup:\n\n".
            "**Startup Idea:** {$startupIdea}\n\n".
            "Create three versions of the elevator pitch:\n\n".
            "1. **30-Second Pitch**: Ultra-concise version for brief encounters\n".
            "2. **60-Second Pitch**: Standard elevator pitch with key details\n".
            "3. **90-Second Pitch**: Extended version with more context and proof points\n\n".
            "Each pitch should follow this structure:\n".
            "- **Hook**: Attention-grabbing opening\n".
            "- **Problem**: The pain point you're solving\n".
            "- **Solution**: Your unique approach\n".
            "- **Market**: Target audience and opportunity size\n".
            "- **Traction**: Evidence of progress or validation\n".
            "- **Ask**: Clear call to action\n\n".
            "Also provide:\n".
            "- **Key Points**: 5-7 bullet points highlighting the most important aspects\n".
            "- **Call to Action**: Specific next steps for different audiences (investors, customers, partners)\n\n".
            "Make the pitches:\n".
            "- Conversational and natural\n".
            "- Compelling and memorable\n".
            "- Tailored to generate interest\n".
            "- Clear about the value proposition\n".
            "- Confident but not overly salesy\n\n".
            'Focus on benefits rather than features, and include specific metrics or achievements where possible.';
    }
}
