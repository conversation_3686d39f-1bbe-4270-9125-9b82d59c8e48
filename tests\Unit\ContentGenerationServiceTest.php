<?php

namespace Tests\Unit;

use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use App\Services\OpenAiService;
use App\Services\PromptEngineeringService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class ContentGenerationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ContentGenerationService $service;

    protected OpenAiService $mockOpenAiService;

    protected PromptEngineeringService $mockPromptService;

    protected Project $project;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock services
        $this->mockOpenAiService = Mockery::mock(OpenAiService::class);
        $this->mockPromptService = Mockery::mock(PromptEngineeringService::class);

        // Create the service with mocked dependencies
        $this->service = new ContentGenerationService(
            $this->mockOpenAiService,
            $this->mockPromptService
        );

        // Create a test project
        $this->project = Project::factory()->create([
            'input_prompt' => 'A revolutionary app for tracking carbon footprint',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_generate_full_lean_canvas_validates_input(): void
    {
        // Test with null project
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid project provided for Lean Canvas generation');
        $this->service->generateFullLeanCanvas(new Project);
    }

    public function test_generate_full_lean_canvas_validates_prompt(): void
    {
        $project = Project::factory()->create(['input_prompt' => '']);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Project input prompt is required for Lean Canvas generation');
        $this->service->generateFullLeanCanvas($project);
    }

    public function test_generate_full_lean_canvas_skips_existing_content(): void
    {
        // Create existing content for problem section
        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_problem',
            'content_data' => ['problem' => 'Existing problem content'],
        ]);

        // Mock the prompt service for other sections
        $this->mockPromptService
            ->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        // Mock the OpenAI service for other sections
        $this->mockOpenAiService
            ->shouldReceive('generateContent')
            ->times(9) // 9 remaining sections
            ->andReturn('Generated content');

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('warning')->zeroOrMoreTimes();
        Log::shouldReceive('error')->zeroOrMoreTimes();

        $result = $this->service->generateFullLeanCanvas($this->project);

        $this->assertArrayHasKey('problem', $result);
        $this->assertInstanceOf(GeneratedContent::class, $result['problem']);
        $this->assertEquals('lean_canvas_problem', $result['problem']->content_type);
    }

    public function test_generate_full_lean_canvas_handles_partial_failures(): void
    {
        // Mock the prompt service
        $this->mockPromptService
            ->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        // Mock successful generation for some sections and failure for others
        $this->mockOpenAiService
            ->shouldReceive('generateContent')
            ->times(5)
            ->andReturn('Generated content');

        $this->mockOpenAiService
            ->shouldReceive('generateContent')
            ->times(5)
            ->andThrow(new Exception('API rate limit exceeded'));

        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('warning')->atLeast()->once();
        Log::shouldReceive('error')->atLeast()->once();

        $result = $this->service->generateFullLeanCanvas($this->project);

        // Should return the successful sections
        $this->assertGreaterThan(0, count($result));
        $this->assertLessThan(10, count($result)); // Less than total sections due to failures
    }

    public function test_generate_missing_lean_canvas_sections(): void
    {
        // Create existing content for some sections
        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_problem',
            'content_data' => ['problem' => 'Existing problem'],
        ]);

        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_solution',
            'content_data' => ['solution' => 'Existing solution'],
        ]);

        // Mock services for missing sections
        $this->mockPromptService
            ->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService
            ->shouldReceive('generateContent')
            ->atLeast()->times(8) // At least 8 missing sections
            ->andReturn('Generated content');

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateMissingLeanCanvasSections($this->project);

        // Should include both existing and newly generated content
        $this->assertGreaterThanOrEqual(10, count($result)); // At least all 10 sections

        // Check for both section keys and content type keys since the method merges both formats
        $hasProblems = isset($result['problem']) || isset($result['lean_canvas_problem']);
        $hasSolution = isset($result['solution']) || isset($result['lean_canvas_solution']);

        $this->assertTrue($hasProblems, 'Result should contain problem section');
        $this->assertTrue($hasSolution, 'Result should contain solution section');
    }

    public function test_get_lean_canvas_completion_status(): void
    {
        // Create content for 3 out of 10 sections
        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_problem',
        ]);

        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_solution',
        ]);

        GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'lean_canvas_unique_value_proposition',
        ]);

        $status = $this->service->getLeanCanvasCompletionStatus($this->project);

        $this->assertEquals(3, $status['completed']);
        $this->assertEquals(10, $status['total']);
        $this->assertEquals(30.0, $status['percentage']);
        $this->assertEquals(7, count($status['missing']));
        $this->assertContains('customer_segments', $status['missing']);
        $this->assertContains('channels', $status['missing']);
    }

    public function test_get_lean_canvas_completion_status_empty(): void
    {
        $status = $this->service->getLeanCanvasCompletionStatus($this->project);

        $this->assertEquals(0, $status['completed']);
        $this->assertEquals(10, $status['total']);
        $this->assertEquals(0.0, $status['percentage']);
        $this->assertEquals(10, count($status['missing']));
    }

    public function test_get_lean_canvas_completion_status_complete(): void
    {
        // Create all sections
        $sections = [
            'lean_canvas_problem',
            'lean_canvas_solution',
            'lean_canvas_unique_value_proposition',
            'lean_canvas_unfair_advantage',
            'lean_canvas_customer_segments',
            'lean_canvas_existing_alternatives',
            'lean_canvas_key_metrics',
            'lean_canvas_channels',
            'lean_canvas_cost_structure',
            'lean_canvas_revenue_streams',
        ];

        foreach ($sections as $contentType) {
            GeneratedContent::factory()->create([
                'project_id' => $this->project->id,
                'content_type' => $contentType,
            ]);
        }

        $status = $this->service->getLeanCanvasCompletionStatus($this->project);

        $this->assertEquals(10, $status['completed']);
        $this->assertEquals(10, $status['total']);
        $this->assertEquals(100.0, $status['percentage']);
        $this->assertEquals(0, count($status['missing']));
    }

    public function test_generate_lean_canvas_calls_generate_full_lean_canvas(): void
    {
        $project = Project::factory()->create();

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->times(10)
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateContent')
            ->times(10)
            ->andReturn('Generated content');

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateLeanCanvas($project);

        $this->assertIsArray($result);
        $this->assertCount(10, $result);
    }

    public function test_generate_critical_hypotheses_validates_input(): void
    {
        $project = new Project;

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid project provided for Critical Hypotheses generation');

        $this->service->generateCriticalHypotheses($project);
    }

    public function test_generate_critical_hypotheses_validates_prompt(): void
    {
        $project = Project::factory()->create(['input_prompt' => '']);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Project input prompt is required for Critical Hypotheses generation');

        $this->service->generateCriticalHypotheses($project);
    }

    public function test_generate_critical_hypotheses_creates_all_three_types(): void
    {
        $project = Project::factory()->create();

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->times(3)
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->times(3)
            ->andReturn([
                'hypothesis' => 'Test hypothesis',
                'criticality' => 'High',
                'testing_method' => 'Survey',
                'success_criteria' => 'Test criteria',
                'risk_level' => 'Medium',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateCriticalHypotheses($project);

        $this->assertCount(3, $result);
        $this->assertArrayHasKey('desirability', $result);
        $this->assertArrayHasKey('viability', $result);
        $this->assertArrayHasKey('feasibility', $result);

        // Check that GeneratedContent records were created
        $this->assertDatabaseHas('generated_contents', [
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_desirability',
        ]);
        $this->assertDatabaseHas('generated_contents', [
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_viability',
        ]);
        $this->assertDatabaseHas('generated_contents', [
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_feasibility',
        ]);
    }

    public function test_generate_critical_hypotheses_skips_existing_content(): void
    {
        $project = Project::factory()->create();

        // Create existing content for desirability
        GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_desirability',
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->times(2)
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->times(2) // Only for viability and feasibility
            ->andReturn([
                'hypothesis' => 'Test hypothesis',
                'criticality' => 'High',
                'testing_method' => 'Survey',
                'success_criteria' => 'Test criteria',
                'risk_level' => 'Medium',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateCriticalHypotheses($project);

        $this->assertCount(3, $result);
        $this->assertArrayHasKey('desirability', $result);
        $this->assertArrayHasKey('viability', $result);
        $this->assertArrayHasKey('feasibility', $result);
    }

    public function test_generate_critical_hypothesis_validates_type(): void
    {
        $project = Project::factory()->create();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid hypothesis type: invalid');

        $this->service->generateCriticalHypothesis($project, 'invalid');
    }

    public function test_generate_critical_hypothesis_creates_single_hypothesis(): void
    {
        $project = Project::factory()->create();

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->once()
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'hypothesis' => 'Desirability test hypothesis',
                'criticality' => 'High',
                'testing_method' => 'User interviews',
                'success_criteria' => '80% positive feedback',
                'risk_level' => 'Medium',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateCriticalHypothesis($project, 'desirability');

        $this->assertInstanceOf(GeneratedContent::class, $result);
        $this->assertEquals('critical_hypothesis_desirability', $result->content_type);
        $this->assertEquals($project->id, $result->project_id);

        $contentData = $result->content_data;
        $this->assertEquals('Desirability test hypothesis', $contentData['hypothesis']);
        $this->assertEquals('High', $contentData['criticality']);
        $this->assertEquals('desirability', $contentData['hypothesis_type']);
    }

    public function test_has_critical_hypotheses_returns_false_when_missing(): void
    {
        $project = Project::factory()->create();

        $result = $this->service->hasCriticalHypotheses($project);

        $this->assertFalse($result);
    }

    public function test_has_critical_hypotheses_returns_true_when_all_exist(): void
    {
        $project = Project::factory()->create();

        // Create all three hypothesis types
        GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_desirability',
        ]);
        GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_viability',
        ]);
        GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_feasibility',
        ]);

        $result = $this->service->hasCriticalHypotheses($project);

        $this->assertTrue($result);
    }

    public function test_get_critical_hypotheses_returns_existing_hypotheses(): void
    {
        $project = Project::factory()->create();

        $desirability = GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_desirability',
        ]);
        $viability = GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_viability',
        ]);

        $result = $this->service->getCriticalHypotheses($project);

        $this->assertCount(2, $result);
        $this->assertArrayHasKey('desirability', $result);
        $this->assertArrayHasKey('viability', $result);
        $this->assertEquals($desirability->id, $result['desirability']->id);
        $this->assertEquals($viability->id, $result['viability']->id);
    }

    public function test_regenerate_critical_hypothesis_deletes_and_recreates(): void
    {
        $project = Project::factory()->create();

        // Create existing hypothesis
        $existing = GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'critical_hypothesis_desirability',
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->once()
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'hypothesis' => 'New desirability hypothesis',
                'criticality' => 'Medium',
                'testing_method' => 'A/B testing',
                'success_criteria' => 'New criteria',
                'risk_level' => 'Low',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->regenerateCriticalHypothesis($project, 'desirability');

        // Check that old content was deleted
        $this->assertDatabaseMissing('generated_contents', [
            'id' => $existing->id,
        ]);

        // Check that new content was created
        $this->assertInstanceOf(GeneratedContent::class, $result);
        $this->assertEquals('critical_hypothesis_desirability', $result->content_type);
        $this->assertNotEquals($existing->id, $result->id);
    }

    public function test_generate_interview_questionnaire_creates_content(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint and providing eco-friendly recommendations',
        ]);

        $this->mockPromptService->shouldReceive('buildInterviewQuestionnairePrompt')
            ->once()
            ->with($project->input_prompt)
            ->andReturn('Generate interview questions for: A mobile app for tracking carbon footprint');

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->once()
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'introduction' => 'Test introduction',
                'questions' => [
                    ['section' => 'Problem Discovery', 'questions' => ['Question 1']],
                    ['section' => 'Solution Validation', 'questions' => ['Question 2']],
                ],
                'conclusion' => 'Test conclusion',
            ]);

        Log::shouldReceive('info')->twice();

        $result = $this->service->generateInterviewQuestionnaire($project);

        $this->assertInstanceOf(GeneratedContent::class, $result);
        $this->assertEquals($project->id, $result->project_id);
        $this->assertEquals('interview_questionnaire', $result->content_type);
        $this->assertIsArray($result->content_data);
        $this->assertArrayHasKey('introduction', $result->content_data);
        $this->assertArrayHasKey('questions', $result->content_data);
        $this->assertArrayHasKey('conclusion', $result->content_data);
        $this->assertArrayHasKey('generated_at', $result->content_data);
        $this->assertArrayHasKey('question_count', $result->content_data);
        $this->assertIsArray($result->content_data['questions']);
        $this->assertEquals(2, $result->content_data['question_count']);
    }

    public function test_generate_interview_questionnaire_returns_existing_content(): void
    {
        $project = Project::factory()->create();

        // Create existing content
        $existingContent = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => [
                'introduction' => 'Test introduction',
                'questions' => ['Question 1', 'Question 2'],
                'conclusion' => 'Test conclusion',
                'generated_at' => now()->toISOString(),
                'question_count' => 2,
            ],
        ]);

        $result = $this->service->generateInterviewQuestionnaire($project);

        $this->assertEquals($existingContent->id, $result->id);
    }

    public function test_has_interview_questionnaire_returns_correct_status(): void
    {
        $project = Project::factory()->create();
        // Initially should return false
        $this->assertFalse($this->service->hasInterviewQuestionnaire($project));

        // After creating content, should return true
        GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['introduction' => 'Test', 'questions' => [], 'conclusion' => 'Test'],
        ]);

        $this->assertTrue($this->service->hasInterviewQuestionnaire($project));
    }

    public function test_get_interview_questionnaire_returns_content(): void
    {
        $project = Project::factory()->create();
        // Initially should return null
        $this->assertNull($this->service->getInterviewQuestionnaire($project));

        // After creating content, should return the content
        $content = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['introduction' => 'Test', 'questions' => [], 'conclusion' => 'Test'],
        ]);

        $result = $this->service->getInterviewQuestionnaire($project);
        $this->assertEquals($content->id, $result->id);
    }

    public function test_regenerate_interview_questionnaire_deletes_and_creates_new(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint',
        ]);

        // Create existing content
        $existingContent = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['introduction' => 'Old', 'questions' => [], 'conclusion' => 'Old'],
        ]);

        $this->mockPromptService->shouldReceive('buildInterviewQuestionnairePrompt')
            ->once()
            ->with($project->input_prompt)
            ->andReturn('Generate interview questions for: A mobile app for tracking carbon footprint');

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->once()
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'introduction' => 'New introduction',
                'questions' => [
                    ['section' => 'Problem Discovery', 'questions' => ['New Question 1']],
                    ['section' => 'Solution Validation', 'questions' => ['New Question 2']],
                ],
                'conclusion' => 'New conclusion',
            ]);

        Log::shouldReceive('info')->twice();

        $result = $this->service->regenerateInterviewQuestionnaire($project);

        // Should be a new instance
        $this->assertNotEquals($existingContent->id, $result->id);
        $this->assertEquals('interview_questionnaire', $result->content_type);
        $this->assertEquals($project->id, $result->project_id);

        // Old content should be deleted
        $this->assertDatabaseMissing('generated_contents', ['id' => $existingContent->id]);
    }

    // Market Sizing Analysis Tests

    public function test_generate_market_sizing_analysis_validates_input(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid project provided for Market Sizing Analysis generation');

        $invalidProject = new Project();
        $this->service->generateMarketSizingAnalysis($invalidProject);
    }

    public function test_generate_market_sizing_analysis_validates_prompt(): void
    {
        $project = Project::factory()->create(['input_prompt' => '']);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Project input prompt is required for Market Sizing Analysis generation');

        $this->service->generateMarketSizingAnalysis($project);
    }

    public function test_generate_market_sizing_analysis_creates_all_three_types(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint and providing eco-friendly recommendations',
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->times(3)
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        // Mock responses for TAM, SAM, SOM
        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->times(3)
            ->andReturn([
                'market_size_value' => '$50 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Top-down approach using industry reports',
                'confidence_level' => 'High',
                'growth_rate' => '15% annually',
                'time_frame' => '2024-2029',
                'key_insights' => ['Growing environmental awareness', 'Regulatory support'],
                'market_description' => 'Global carbon tracking market',
                'data_sources' => ['Industry reports', 'Market research'],
                'assumptions' => ['Continued environmental focus', 'Technology adoption'],
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateMarketSizingAnalysis($project);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertArrayHasKey('tam', $result);
        $this->assertArrayHasKey('sam', $result);
        $this->assertArrayHasKey('som', $result);

        foreach (['tam', 'sam', 'som'] as $type) {
            $this->assertInstanceOf(GeneratedContent::class, $result[$type]);
            $this->assertEquals($project->id, $result[$type]->project_id);
            $this->assertEquals("market_sizing_{$type}", $result[$type]->content_type);
            $this->assertIsArray($result[$type]->content_data);
            $this->assertArrayHasKey('market_size_value', $result[$type]->content_data);
            $this->assertArrayHasKey('generated_at', $result[$type]->content_data);
        }
    }

    public function test_generate_market_sizing_analysis_skips_existing_content(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint',
        ]);

        // Create existing TAM analysis
        $existingTam = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'market_sizing_tam',
            'content_data' => [
                'market_size_value' => '$100 billion',
                'market_size_currency' => 'USD',
                'generated_at' => now()->toISOString(),
            ],
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->times(2) // Only for SAM and SOM
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->times(2) // Only for SAM and SOM
            ->andReturn([
                'market_size_value' => '$25 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Bottom-up approach',
                'confidence_level' => 'Medium',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->generateMarketSizingAnalysis($project);

        $this->assertCount(3, $result);
        $this->assertEquals($existingTam->id, $result['tam']->id); // Should be existing content
        $this->assertNotEquals($existingTam->id, $result['sam']->id); // Should be new content
        $this->assertNotEquals($existingTam->id, $result['som']->id); // Should be new content
    }

    public function test_generate_market_sizing_analysis_type_validates_type(): void
    {
        $project = Project::factory()->create();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid market sizing analysis type: invalid_type');

        $this->service->generateMarketSizingAnalysisType($project, 'invalid_type');
    }

    public function test_generate_market_sizing_analysis_type_creates_single_analysis(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint',
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->once()
            ->with('business_strategist')
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'market_size_value' => '$50 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Top-down approach',
                'confidence_level' => 'High',
                'growth_rate' => '15% annually',
                'time_frame' => '2024-2029',
                'key_insights' => ['Growing market', 'Strong demand'],
                'market_description' => 'Total addressable market for carbon tracking',
            ]);

        Log::shouldReceive('info')->twice();

        $result = $this->service->generateMarketSizingAnalysisType($project, 'tam');

        $this->assertInstanceOf(GeneratedContent::class, $result);
        $this->assertEquals($project->id, $result->project_id);
        $this->assertEquals('market_sizing_tam', $result->content_type);
        $this->assertIsArray($result->content_data);
        $this->assertArrayHasKey('market_size_value', $result->content_data);
        $this->assertEquals('$50 billion', $result->content_data['market_size_value']);
    }

    public function test_has_market_sizing_analysis_returns_false_when_missing(): void
    {
        $project = Project::factory()->create();
        $this->assertFalse($this->service->hasMarketSizingAnalysis($project));
    }

    public function test_has_market_sizing_analysis_returns_true_when_all_exist(): void
    {
        $project = Project::factory()->create();

        // Create all three types
        foreach (['tam', 'sam', 'som'] as $type) {
            GeneratedContent::create([
                'project_id' => $project->id,
                'content_type' => "market_sizing_{$type}",
                'content_data' => [
                    'market_size_value' => '$100 billion',
                    'market_size_currency' => 'USD',
                ],
            ]);
        }

        $this->assertTrue($this->service->hasMarketSizingAnalysis($project));
    }

    public function test_get_market_sizing_analysis_returns_existing_analyses(): void
    {
        $project = Project::factory()->create();

        $tamAnalysis = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'market_sizing_tam',
            'content_data' => [
                'market_size_value' => '$100 billion',
                'market_size_currency' => 'USD',
            ],
        ]);

        $samAnalysis = GeneratedContent::create([
            'project_id' => $project->id,
            'content_type' => 'market_sizing_sam',
            'content_data' => [
                'market_size_value' => '$50 billion',
                'market_size_currency' => 'USD',
            ],
        ]);

        $result = $this->service->getMarketSizingAnalysis($project);

        $this->assertCount(2, $result);
        $this->assertArrayHasKey('tam', $result);
        $this->assertArrayHasKey('sam', $result);
        $this->assertEquals($tamAnalysis->id, $result['tam']->id);
        $this->assertEquals($samAnalysis->id, $result['sam']->id);
    }

    public function test_regenerate_market_sizing_analysis_deletes_and_recreates(): void
    {
        $project = Project::factory()->create([
            'input_prompt' => 'A mobile app for tracking carbon footprint',
        ]);

        // Create existing analysis
        $existing = GeneratedContent::factory()->create([
            'project_id' => $project->id,
            'content_type' => 'market_sizing_tam',
            'content_data' => [
                'market_size_value' => '$100 billion',
                'market_size_currency' => 'USD',
            ],
        ]);

        $this->mockPromptService->shouldReceive('buildSystemPrompt')
            ->with('business_strategist')
            ->once()
            ->andReturn('You are a business strategist...');

        $this->mockOpenAiService->shouldReceive('generateStructuredContent')
            ->once()
            ->andReturn([
                'market_size_value' => '$75 billion',
                'market_size_currency' => 'USD',
                'calculation_method' => 'Updated approach',
                'confidence_level' => 'High',
                'growth_rate' => '12% annually',
                'time_frame' => '2024-2030',
                'key_insights' => ['Updated insights'],
                'market_description' => 'Updated market description',
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->service->regenerateMarketSizingAnalysis($project, 'tam');

        // Check that old content was deleted
        $this->assertDatabaseMissing('generated_contents', [
            'id' => $existing->id,
        ]);

        // Check that new content was created
        $this->assertInstanceOf(GeneratedContent::class, $result);
        $this->assertEquals('market_sizing_tam', $result->content_type);
        $this->assertNotEquals($existing->id, $result->id);
        $this->assertEquals('$75 billion', $result->content_data['market_size_value']);
    }
}
