<?php

namespace Tests\Unit;

use App\Jobs\GenerateInterviewQuestionnaireJob;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class GenerateInterviewQuestionnaireJobTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear any existing log expectations
        Log::spy();
    }

    public function test_job_generates_questionnaire_when_regenerate_is_true(): void
    {
        // Arrange
        $project = Project::factory()->create();

        $mockService = Mockery::mock(ContentGenerationService::class);
        $mockContent = GeneratedContent::factory()->make([
            'id' => 1,
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['questions' => ['What problem are you solving?'], 'question_count' => 1],
        ]);

        $mockService->shouldReceive('regenerateInterviewQuestionnaire')
            ->once()
            ->with($project)
            ->andReturn($mockContent);

        // Act
        $job = new GenerateInterviewQuestionnaireJob($project, true);
        $job->handle($mockService);

        // Assert
        Log::shouldHaveReceived('info')
            ->with('Interview questionnaire regenerated successfully', [
                'project_id' => $project->id,
                'content_id' => 1,
                'question_count' => 1,
            ]);

        $this->assertTrue(true); // Ensure test has assertion
    }

    public function test_job_generates_questionnaire_when_none_exists(): void
    {
        // Arrange
        $project = Project::factory()->create();

        $mockService = Mockery::mock(ContentGenerationService::class);
        $mockContent = GeneratedContent::factory()->make([
            'id' => 2,
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['questions' => ['What problem are you solving?'], 'question_count' => 1],
        ]);

        $mockService->shouldReceive('getInterviewQuestionnaire')
            ->once()
            ->with($project)
            ->andReturn(null);

        $mockService->shouldReceive('generateInterviewQuestionnaire')
            ->once()
            ->with($project)
            ->andReturn($mockContent);

        // Act
        $job = new GenerateInterviewQuestionnaireJob($project, false);
        $job->handle($mockService);

        // Assert
        Log::shouldHaveReceived('info')
            ->with('Interview questionnaire generated successfully', [
                'project_id' => $project->id,
                'content_id' => 2,
                'question_count' => 1,
            ]);

        $this->assertTrue(true); // Ensure test has assertion
    }

    public function test_job_skips_generation_when_questionnaire_exists_and_regenerate_is_false(): void
    {
        // Arrange
        $project = Project::factory()->create();

        $existingContent = GeneratedContent::factory()->make([
            'id' => 3,
            'project_id' => $project->id,
            'content_type' => 'interview_questionnaire',
            'content_data' => ['questions' => ['Existing question']],
        ]);

        $mockService = Mockery::mock(ContentGenerationService::class);
        $mockService->shouldReceive('getInterviewQuestionnaire')
            ->once()
            ->with($project)
            ->andReturn($existingContent);

        $mockService->shouldNotReceive('generateInterviewQuestionnaire');
        $mockService->shouldNotReceive('regenerateInterviewQuestionnaire');

        // Act
        $job = new GenerateInterviewQuestionnaireJob($project, false);
        $job->handle($mockService);

        // Assert
        Log::shouldHaveReceived('info')
            ->with('Interview questionnaire already exists, skipping generation', [
                'project_id' => $project->id,
                'content_id' => 3,
            ]);

        $this->assertTrue(true); // Ensure test has assertion
    }

    public function test_job_handles_exception_during_generation(): void
    {
        // Arrange
        $project = Project::factory()->create();

        $mockService = Mockery::mock(ContentGenerationService::class);
        $mockService->shouldReceive('regenerateInterviewQuestionnaire')
            ->once()
            ->with($project)
            ->andThrow(new Exception('API Error'));

        // Assert - The job should re-throw the exception, so we expect it to be thrown
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('API Error');

        // Act
        $job = new GenerateInterviewQuestionnaireJob($project, true);
        $job->handle($mockService);
    }

    public function test_job_has_correct_properties(): void
    {
        // Arrange
        $project = Project::factory()->create();

        // Act
        $job = new GenerateInterviewQuestionnaireJob($project, true);

        // Assert
        $this->assertEquals($project->id, $job->project->id);
        $this->assertTrue($job->regenerate);
        $this->assertEquals(3, $job->tries);
        $this->assertEquals(300, $job->timeout);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
