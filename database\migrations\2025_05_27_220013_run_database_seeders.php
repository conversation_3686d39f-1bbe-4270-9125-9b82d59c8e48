<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration runs all database seeders to populate the database
     * with initial data. Safe to run multiple times due to firstOrCreate usage.
     */
    public function up(): void
    {
        try {
            Log::info('🌱 Starting database seeding via migration...');

            // Run all seeders in the correct order
            Artisan::call('db:seed', [
                '--force' => true, // Skip confirmation in production
            ]);

            $output = Artisan::output();
            Log::info('✅ Database seeding completed successfully via migration');
            Log::info('Seeder output: ' . $output);

        } catch (\Exception $e) {
            Log::error('❌ Database seeding failed in migration: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            // Don't throw the exception to prevent migration failure
            // This allows the migration to complete even if seeding fails
            // You can check logs for seeding issues
        }
    }

    /**
     * Reverse the migrations.
     *
     * Note: We don't reverse seeding in down() method as it would
     * delete all data. Seeders are designed to be additive only.
     */
    public function down(): void
    {
        // Intentionally left empty
        // Reversing seeders would delete data, which is typically not desired
        Log::info('Migration rollback: Seeders are not reversed to preserve data');
    }
};
