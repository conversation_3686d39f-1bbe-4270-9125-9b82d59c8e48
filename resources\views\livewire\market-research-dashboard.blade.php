<div class="space-y-6">
    {{-- Header Messages --}}
    @if($errorMessage)
        <div class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-800 dark:text-red-200">{{ $errorMessage }}</p>
                </div>
            </div>
        </div>
    @endif

    @if($successMessage)
        <div class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-800 dark:text-green-200">{{ $successMessage }}</p>
                </div>
            </div>
        </div>
    @endif

    {{-- Research Form --}}
    <x-filament::section>
        <x-slot name="heading">
            Generate Market Research
        </x-slot>
        
        <x-slot name="description">
            Enter your target industry and region to generate comprehensive AI-powered market insights.
        </x-slot>

        <form wire:submit.prevent="researchMarket" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Target Industry
                    </label>
                    <input
                        type="text"
                        id="industry"
                        wire:model="industry"
                        placeholder="e.g., Fintech, Healthcare, E-commerce"
                        required
                        class="block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                    @error('industry') 
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="region" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Target Region
                    </label>
                    <input
                        type="text"
                        id="region"
                        wire:model="region"
                        placeholder="e.g., North America, Southeast Asia, Europe"
                        required
                        class="block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                    @error('region') 
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="flex gap-3">
                <x-filament::button
                    type="submit"
                    :disabled="$isLoading"
                    size="lg"
                    icon="heroicon-m-chart-bar"
                >
                    @if($isLoading)
                        <x-filament::loading-indicator class="h-4 w-4" />
                        Generating Research...
                    @else
                        Generate Research
                    @endif
                </x-filament::button>

                @if($hasData)
                    <x-filament::button
                        color="gray"
                        outlined
                        wire:click="confirmReset"
                        icon="heroicon-m-arrow-path"
                    >
                        New Research
                    </x-filament::button>
                @endif
            </div>
        </form>
    </x-filament::section>

    {{-- Previous Sessions (Auth Users Only) --}}
    @auth
        @if(count($previousSessions) > 0)
            <x-filament::section>
                <x-slot name="heading">
                    Previous Research Sessions
                </x-slot>

                <div class="grid gap-3">
                    @foreach($previousSessions as $session)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="flex-1">
                                <div class="font-medium">{{ $session['industry'] }} - {{ $session['region'] }}</div>
                                <div class="text-sm text-gray-500">
                                    {{ $session['created_at'] }} • {{ $session['completion_percentage'] }}% complete
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <x-filament::button
                                    color="gray"
                                    size="sm"
                                    wire:click="loadSession({{ $session['id'] }})"
                                    icon="heroicon-m-folder-open"
                                >
                                    Load
                                </x-filament::button>
                                <x-filament::button
                                    color="danger"
                                    size="sm"
                                    wire:click="deleteSession({{ $session['id'] }})"
                                    icon="heroicon-m-trash"
                                    wire:confirm="Are you sure you want to delete this research session?"
                                >
                                    Delete
                                </x-filament::button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </x-filament::section>
        @endif
    @endauth

    {{-- Dashboard Content --}}
    @if($hasData)
        {{-- Market Overview --}}
        <x-filament::section>
            <x-slot name="heading">
                Market Overview
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {{-- Market Attractiveness --}}
                <div class="text-center p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                        {{ $this->marketAttractivenessScore }}/10
                    </div>
                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                        Market Attractiveness
                    </div>
                    <div class="text-xs text-gray-500 mt-2">
                        {{ $this->marketAttractivenessStatement }}
                    </div>
                </div>

                {{-- Market Size --}}
                @if($this->formattedMarketSize)
                    <div class="p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
                        <div class="text-lg font-semibold text-green-600 dark:text-green-400 mb-3">Market Size</div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">TAM:</span>
                                <span class="font-medium">{{ $this->formattedMarketSize['tam'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">SAM:</span>
                                <span class="font-medium">{{ $this->formattedMarketSize['sam'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">SOM:</span>
                                <span class="font-medium">{{ $this->formattedMarketSize['som'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">CAGR:</span>
                                <span class="font-medium">{{ $this->formattedMarketSize['cagr'] }}</span>
                            </div>
                        </div>
                    </div>
                @endif

                {{-- Opportunity Zones Count --}}
                <div class="text-center p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
                        {{ $this->opportunityZonesCount }}
                    </div>
                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400 mt-1">
                        Opportunity Zones
                    </div>
                    <div class="text-xs text-gray-500 mt-2">
                        Strategic opportunities identified
                    </div>
                </div>
            </div>
        </x-filament::section>

        {{-- Opportunity Zones --}}
        @if(count($opportunityZones) > 0)
            <x-filament::section>
                <x-slot name="heading">
                    🎯 Opportunity Zones
                </x-slot>

                <div class="grid gap-4">
                    @foreach($opportunityZones as $index => $zone)
                        <div class="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $zone['title'] }}</h4>
                                    <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $zone['description'] }}</p>
                                </div>
                                <x-filament::button
                                    color="info"
                                    size="sm"
                                    wire:click="elaborateOpportunityZone({{ $index }})"
                                    icon="heroicon-m-light-bulb"
                                >
                                    Elaborate
                                </x-filament::button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </x-filament::section>
        @endif

        {{-- Customer Pain Points --}}
        @if(count($customerPainPoints) > 0)
            <x-filament::section>
                <x-slot name="heading">
                    😣 Customer Pain Points
                </x-slot>

                <div class="grid gap-4">
                    @foreach($customerPainPoints as $index => $painPoint)
                        <div class="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $painPoint['title'] }}</h4>
                                    <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $painPoint['description'] }}</p>
                                </div>
                                <x-filament::button
                                    color="success"
                                    size="sm"
                                    wire:click="brainstormPainPointSolutions({{ $index }})"
                                    icon="heroicon-m-light-bulb"
                                >
                                    Solutions
                                </x-filament::button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </x-filament::section>
        @endif

        {{-- SWOT Analysis --}}
        @if($this->hasSwotAnalysis)
            <x-filament::section>
                <x-slot name="heading">
                    📊 SWOT Analysis
                </x-slot>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {{-- Strengths --}}
                    @if(isset($swotAnalysis['strengths']))
                        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 mb-3">💪 Strengths</h4>
                            <ul class="space-y-2">
                                @foreach($swotAnalysis['strengths'] as $index => $strength)
                                    <li class="flex justify-between items-start group">
                                        <span class="text-green-700 dark:text-green-300 text-sm">• {{ $strength }}</span>
                                        <x-filament::button
                                            color="success"
                                            size="xs"
                                            wire:click="suggestSWOTActions('strengths', {{ $index }})"
                                            icon="heroicon-m-light-bulb"
                                            class="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
                                        >
                                            Actions
                                        </x-filament::button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    {{-- Weaknesses --}}
                    @if(isset($swotAnalysis['weaknesses']))
                        <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                            <h4 class="font-semibold text-red-800 dark:text-red-200 mb-3">⚠️ Weaknesses</h4>
                            <ul class="space-y-2">
                                @foreach($swotAnalysis['weaknesses'] as $index => $weakness)
                                    <li class="flex justify-between items-start group">
                                        <span class="text-red-700 dark:text-red-300 text-sm">• {{ $weakness }}</span>
                                        <x-filament::button
                                            color="danger"
                                            size="xs"
                                            wire:click="suggestSWOTActions('weaknesses', {{ $index }})"
                                            icon="heroicon-m-light-bulb"
                                            class="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
                                        >
                                            Actions
                                        </x-filament::button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    {{-- Opportunities --}}
                    @if(isset($swotAnalysis['opportunities']))
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">🚀 Opportunities</h4>
                            <ul class="space-y-2">
                                @foreach($swotAnalysis['opportunities'] as $index => $opportunity)
                                    <li class="flex justify-between items-start group">
                                        <span class="text-blue-700 dark:text-blue-300 text-sm">• {{ $opportunity }}</span>
                                        <x-filament::button
                                            color="info"
                                            size="xs"
                                            wire:click="suggestSWOTActions('opportunities', {{ $index }})"
                                            icon="heroicon-m-light-bulb"
                                            class="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
                                        >
                                            Actions
                                        </x-filament::button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    {{-- Threats --}}
                    @if(isset($swotAnalysis['threats']))
                        <div class="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 mb-3">⚡ Threats</h4>
                            <ul class="space-y-2">
                                @foreach($swotAnalysis['threats'] as $index => $threat)
                                    <li class="flex justify-between items-start group">
                                        <span class="text-orange-700 dark:text-orange-300 text-sm">• {{ $threat }}</span>
                                        <x-filament::button
                                            color="warning"
                                            size="xs"
                                            wire:click="suggestSWOTActions('threats', {{ $index }})"
                                            icon="heroicon-m-light-bulb"
                                            class="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
                                        >
                                            Actions
                                        </x-filament::button>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </x-filament::section>
        @endif

        {{-- Additional Insights Grid --}}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {{-- Strategic Implications --}}
            @if(count($strategicImplications) > 0)
                <x-filament::section>
                    <x-slot name="heading">
                        🎯 Strategic Implications
                    </x-slot>

                    <ul class="space-y-2">
                        @foreach($strategicImplications as $implication)
                            <li class="text-sm text-gray-700 dark:text-gray-300">• {{ $implication }}</li>
                        @endforeach
                    </ul>
                </x-filament::section>
            @endif

            {{-- Competitive Landscape --}}
            @if($this->hasCompetitiveLandscape)
                <x-filament::section>
                    <x-slot name="heading">
                        🏆 Competitive Landscape
                    </x-slot>

                    <div class="space-y-3">
                        @foreach($competitiveLandscape as $competitor)
                            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded">
                                <div class="font-medium">{{ $competitor['name'] ?? 'Unknown Company' }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">{{ $competitor['description'] ?? 'No description available' }}</div>
                            </div>
                        @endforeach
                    </div>
                </x-filament::section>
            @endif
        </div>

        {{-- Enablers & Barriers --}}
        @if(count($enablers) > 0 || count($barriers) > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{-- Enablers --}}
                @if(count($enablers) > 0)
                    <x-filament::section>
                        <x-slot name="heading">
                            ✅ Market Enablers
                        </x-slot>

                        <ul class="space-y-2">
                            @foreach($enablers as $enabler)
                                <li class="text-sm text-green-700 dark:text-green-300">• {{ $enabler }}</li>
                            @endforeach
                        </ul>
                    </x-filament::section>
                @endif

                {{-- Barriers --}}
                @if(count($barriers) > 0)
                    <x-filament::section>
                        <x-slot name="heading">
                            ⛔ Market Barriers
                        </x-slot>

                        <ul class="space-y-2">
                            @foreach($barriers as $barrier)
                                <li class="text-sm text-red-700 dark:text-red-300">• {{ $barrier }}</li>
                            @endforeach
                        </ul>
                    </x-filament::section>
                @endif
            </div>
        @endif
    @endif

    {{-- Interactive Modal --}}
    <x-filament::modal 
        id="interactive-modal"
        :visible="$showModal"
        wire:model="showModal"
        width="4xl"
    >
        <x-slot name="heading">
            {{ $modalTitle }}
        </x-slot>

        <div class="space-y-4">
            @if($isModalLoading)
                <div class="flex items-center justify-center py-8">
                    <x-filament::loading-indicator class="h-8 w-8" />
                    <span class="ml-2 text-gray-600 dark:text-gray-400">Generating insights...</span>
                </div>
            @elseif(is_array($modalContent) && isset($modalContent['error']))
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-700">{{ $modalContent['error'] }}</p>
                </div>
            @elseif($modalContent)
                @if($modalType === 'opportunity')
                    <div class="space-y-4">
                        @if(isset($modalContent['marketDynamics']))
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Market Dynamics</h4>
                                <p class="text-gray-700 dark:text-gray-300">{{ $modalContent['marketDynamics'] }}</p>
                            </div>
                        @endif
                        
                        @if(isset($modalContent['keyOpportunities']) && count($modalContent['keyOpportunities']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Key Opportunities</h4>
                                <ul class="space-y-2">
                                    @foreach($modalContent['keyOpportunities'] as $opportunity)
                                        <li class="text-gray-700 dark:text-gray-300">• {{ $opportunity }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        @if(isset($modalContent['implementationConsiderations']) && count($modalContent['implementationConsiderations']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Implementation Considerations</h4>
                                <ul class="space-y-2">
                                    @foreach($modalContent['implementationConsiderations'] as $consideration)
                                        <li class="text-gray-700 dark:text-gray-300">• {{ $consideration }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                @elseif($modalType === 'painpoint')
                    <div class="space-y-4">
                        @if(isset($modalContent['solutionCategories']) && count($modalContent['solutionCategories']) > 0)
                            @foreach($modalContent['solutionCategories'] as $category)
                                <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">{{ $category['category'] }}</h4>
                                    <ul class="space-y-1">
                                        @foreach($category['solutions'] as $solution)
                                            <li class="text-gray-700 dark:text-gray-300 text-sm">• {{ $solution }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endforeach
                        @endif
                        
                        @if(isset($modalContent['implementationPriority']) && count($modalContent['implementationPriority']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Implementation Priority</h4>
                                <ul class="space-y-2">
                                    @foreach($modalContent['implementationPriority'] as $priority)
                                        <li class="text-gray-700 dark:text-gray-300">• {{ $priority }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                @elseif($modalType === 'swot')
                    <div class="space-y-4">
                        @if(isset($modalContent['actionStrategies']) && count($modalContent['actionStrategies']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Action Strategies</h4>
                                <ul class="space-y-2">
                                    @foreach($modalContent['actionStrategies'] as $strategy)
                                        <li class="text-gray-700 dark:text-gray-300">• {{ $strategy }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        @if(isset($modalContent['implementationSteps']) && count($modalContent['implementationSteps']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Implementation Steps</h4>
                                <ol class="space-y-2">
                                    @foreach($modalContent['implementationSteps'] as $index => $step)
                                        <li class="text-gray-700 dark:text-gray-300">{{ $index + 1 }}. {{ $step }}</li>
                                    @endforeach
                                </ol>
                            </div>
                        @endif
                        
                        @if(isset($modalContent['successMetrics']) && count($modalContent['successMetrics']) > 0)
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">Success Metrics</h4>
                                <ul class="space-y-2">
                                    @foreach($modalContent['successMetrics'] as $metric)
                                        <li class="text-gray-700 dark:text-gray-300">• {{ $metric }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>
                @endif
            @endif
        </div>

        <x-slot name="footerActions">
            <x-filament::button
                color="gray"
                wire:click="closeModal"
            >
                Close
            </x-filament::button>
        </x-slot>
    </x-filament::modal>

    {{-- Reset Confirmation Modal --}}
    <x-filament::modal 
        id="reset-confirmation"
        :visible="$showResetConfirmation"
        wire:model="showResetConfirmation"
        width="md"
    >
        <x-slot name="heading">
            Confirm Reset
        </x-slot>

        <p class="text-gray-700 dark:text-gray-300">
            Are you sure you want to reset the dashboard? This will clear all current research data and allow you to start a new analysis.
        </p>

        <x-slot name="footerActions">
            <x-filament::button
                color="danger"
                wire:click="resetDashboard"
            >
                Yes, Reset Dashboard
            </x-filament::button>
            
            <x-filament::button
                color="gray"
                wire:click="cancelReset"
            >
                Cancel
            </x-filament::button>
        </x-slot>
    </x-filament::modal>
</div>
