<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="{{ $meta['viewport'] }}">
    <meta name="description" content="{{ $meta['description'] }}">
    <meta name="keywords" content="{{ $meta['keywords'] }}">
    <meta name="author" content="{{ $meta['author'] }}">
    <title>{{ $meta['title'] }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.8;
            color: #333;
            background: #fefefe;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            padding: 40px 0;
            border-bottom: 1px solid #eee;
            background: #fff;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: 400;
            color: #333;
            text-decoration: none;
            letter-spacing: -0.5px;
        }
        
        /* Hero Section */
        .hero {
            padding: 120px 0;
            text-align: center;
            background: #fff;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 30px;
            font-weight: 300;
            color: #222;
            line-height: 1.3;
            letter-spacing: -1px;
        }
        
        .hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 50px;
            color: #666;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 300;
        }
        
        /* Button Styles */
        .btn {
            display: inline-block;
            padding: 15px 40px;
            background: #333;
            color: white;
            text-decoration: none;
            font-size: 1rem;
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
            font-weight: 400;
        }
        
        .btn:hover {
            background: #555;
        }
        
        .btn-secondary {
            background: transparent;
            border: 1px solid #333;
            color: #333;
            margin-left: 20px;
        }
        
        .btn-secondary:hover {
            background: #333;
            color: white;
        }
        
        /* Section Styles */
        .section {
            padding: 100px 0;
        }
        
        .section h2 {
            font-size: 2.2rem;
            margin-bottom: 40px;
            color: #222;
            font-weight: 300;
            text-align: center;
            letter-spacing: -0.5px;
        }
        
        .section p {
            font-size: 1.1rem;
            color: #555;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        /* Problem & Solution */
        .problem-solution {
            background: #f9f9f9;
        }
        
        .content-block {
            margin-bottom: 80px;
        }
        
        .content-block:last-child {
            margin-bottom: 0;
        }
        
        .content-block h3 {
            font-size: 1.6rem;
            margin-bottom: 25px;
            color: #333;
            font-weight: 400;
        }
        
        /* Quote Style */
        .quote {
            font-size: 1.4rem;
            font-style: italic;
            color: #666;
            text-align: center;
            margin: 60px 0;
            line-height: 1.6;
        }
        
        .quote::before,
        .quote::after {
            content: '"';
            font-size: 2rem;
            color: #ccc;
        }
        
        /* Features List */
        .features-list {
            list-style: none;
            margin: 40px 0;
        }
        
        .features-list li {
            padding: 20px 0;
            border-bottom: 1px solid #eee;
            font-size: 1.1rem;
            color: #555;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .features-list li::before {
            content: '—';
            margin-right: 15px;
            color: #999;
        }
        
        /* CTA Section */
        .cta {
            text-align: center;
            background: #fff;
            border-top: 1px solid #eee;
        }
        
        .cta h2 {
            margin-bottom: 30px;
        }
        
        .cta p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            color: #666;
        }
        
        /* Footer */
        .footer {
            background: #f5f5f5;
            padding: 60px 0;
            text-align: center;
            border-top: 1px solid #eee;
        }
        
        .footer p {
            color: #888;
            font-size: 0.9rem;
        }
        
        /* Divider */
        .divider {
            border: none;
            border-top: 1px solid #eee;
            margin: 80px 0;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.2rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .section h2 {
                font-size: 1.8rem;
            }
            
            .btn-secondary {
                margin-left: 0;
                margin-top: 20px;
                display: block;
                text-align: center;
            }
            
            .container {
                padding: 0 15px;
            }
            
            .quote {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <a href="#" class="logo">
                {{ $content['storytelling']['startup_naming']['business_name'] ?? $content['storytelling']['startup_naming']['name'] ?? $meta['title'] }}
            </a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>{{ $content['storytelling']['startup_naming']['tagline'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'Simple. Effective. Elegant.' }}</h1>
            <p class="subtitle">
                {{ $content['storytelling']['elevator_pitch']['pitch'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? substr($project->input_prompt, 0, 200) . '...' }}
            </p>
            <div class="hero-actions">
                <a href="#contact" class="btn">Get Started</a>
                <a href="#about" class="btn btn-secondary">Learn More</a>
            </div>
        </div>
    </section>

    <!-- Problem & Solution Section -->
    <section class="section problem-solution" id="about">
        <div class="container">
            <div class="content-block">
                <h3>The Challenge</h3>
                <p>
                    @if(isset($content['lean_canvas']['problem']['content']))
                        {{ $content['lean_canvas']['problem']['content'] }}
                    @else
                        In today's complex world, people are looking for solutions that cut through the noise and deliver real value.
                    @endif
                </p>
            </div>
            
            <hr class="divider">
            
            <div class="content-block">
                <h3>Our Approach</h3>
                <p>
                    @if(isset($content['lean_canvas']['solution']['content']))
                        {{ $content['lean_canvas']['solution']['content'] }}
                    @else
                        We believe in simplicity without sacrificing power. Our solution focuses on what matters most.
                    @endif
                </p>
            </div>
            
            @if(isset($content['lean_canvas']['unique_value_proposition']['content']))
            <div class="quote">
                {{ $content['lean_canvas']['unique_value_proposition']['content'] }}
            </div>
            @endif
        </div>
    </section>

    <!-- Features Section -->
    <section class="section">
        <div class="container">
            <h2>What Makes Us Different</h2>
            
            <ul class="features-list">
                <li>
                    @if(isset($content['lean_canvas']['unfair_advantage']['content']))
                        {{ $content['lean_canvas']['unfair_advantage']['content'] }}
                    @else
                        Focused on simplicity and ease of use
                    @endif
                </li>
                <li>
                    @if(isset($content['lean_canvas']['channels']['content']))
                        Direct access through {{ $content['lean_canvas']['channels']['content'] }}
                    @else
                        Direct and personal customer relationships
                    @endif
                </li>
                <li>
                    @if(isset($content['lean_canvas']['key_metrics']['content']))
                        Proven results: {{ $content['lean_canvas']['key_metrics']['content'] }}
                    @else
                        Proven track record of success
                    @endif
                </li>
                <li>
                    @if(isset($content['lean_canvas']['cost_structure']['content']))
                        Transparent pricing with {{ $content['lean_canvas']['cost_structure']['content'] }}
                    @else
                        Transparent and fair pricing model
                    @endif
                </li>
            </ul>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section cta" id="contact">
        <div class="container">
            <h2>Ready to Begin?</h2>
            <p>
                @if(isset($content['storytelling']['elevator_pitch']['call_to_action']))
                    {{ $content['storytelling']['elevator_pitch']['call_to_action'] }}
                @else
                    Take the first step towards a better solution.
                @endif
            </p>
            <a href="#" class="btn">Get Started Today</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; {{ date('Y') }} {{ $content['storytelling']['startup_naming']['business_name'] ?? $meta['title'] }}. All rights reserved.</p>
        </div>
    </footer>
</body>
</html> 