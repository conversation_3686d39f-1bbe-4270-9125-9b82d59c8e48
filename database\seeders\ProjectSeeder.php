<?php

namespace Database\Seeders;

use App\Models\Account;
use App\Models\Project;
use Illuminate\Database\Seeder;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some accounts to assign projects to
        $accounts = Account::where('status', 'active')->take(5)->get();

        if ($accounts->isEmpty()) {
            $this->command->warn('No active accounts found. Please run AccountSeeder first.');

            return;
        }

        // Create realistic startup project ideas
        $projects = [
            [
                'account_id' => $accounts[0]->id,
                'input_prompt' => 'EcoTrack - A mobile app that helps individuals and families track their carbon footprint by monitoring daily activities like transportation, energy usage, and consumption habits. The app provides personalized recommendations to reduce environmental impact and connects users with local eco-friendly businesses and services.',
                'status' => 'completed',
            ],
            [
                'account_id' => $accounts[1]->id,
                'input_prompt' => 'SkillSwap - A peer-to-peer learning platform where professionals can exchange skills and knowledge. Users can offer to teach something they\'re expert in (like coding, design, marketing) in exchange for learning something new from another user. The platform includes video calls, progress tracking, and skill verification.',
                'status' => 'processing',
            ],
            [
                'account_id' => $accounts[2]->id,
                'input_prompt' => 'LocalFresh - A subscription service that connects consumers directly with local farmers and food producers. Customers receive weekly boxes of fresh, seasonal produce and artisanal foods from their region. The platform includes farmer profiles, seasonal recipes, and sustainability impact tracking.',
                'status' => 'pending',
            ],
            [
                'account_id' => $accounts[3]->id,
                'input_prompt' => 'MindfulWork - A workplace wellness platform that integrates mental health support into daily work routines. Features include guided meditation sessions, stress level monitoring, team wellness challenges, and anonymous peer support groups. Designed for remote and hybrid teams.',
                'status' => 'completed',
            ],
            [
                'account_id' => $accounts[4]->id,
                'input_prompt' => 'RetireEasy - A financial planning app specifically designed for gig economy workers and freelancers who don\'t have traditional employer-sponsored retirement plans. The app provides automated savings, investment recommendations, and retirement planning tools tailored to irregular income patterns.',
                'status' => 'processing',
            ],
            [
                'account_id' => $accounts[0]->id,
                'input_prompt' => 'PetConnect - A comprehensive pet care platform that connects pet owners with local veterinarians, pet sitters, dog walkers, and pet-friendly businesses. Includes health record tracking, appointment scheduling, emergency care finder, and a social network for pet owners.',
                'status' => 'pending',
            ],
            [
                'account_id' => $accounts[1]->id,
                'input_prompt' => 'StudyBuddy - An AI-powered study companion for college students that creates personalized study schedules, generates practice quizzes from course materials, facilitates study group formation, and provides academic performance analytics. Integrates with popular learning management systems.',
                'status' => 'completed',
            ],
            [
                'account_id' => $accounts[2]->id,
                'input_prompt' => 'HomeHelper - A platform that connects homeowners with trusted local service providers for maintenance, repairs, and improvements. Features include instant quotes, scheduling, progress tracking, quality assurance, and a review system. Focuses on transparency and fair pricing.',
                'status' => 'processing',
            ],
            [
                'account_id' => $accounts[3]->id,
                'input_prompt' => 'FitFlex - A personalized fitness app that adapts workout routines based on available time, equipment, fitness level, and physical limitations. Uses AI to modify exercises in real-time and includes integration with wearable devices for comprehensive health tracking.',
                'status' => 'pending',
            ],
            [
                'account_id' => $accounts[4]->id,
                'input_prompt' => 'CommunityShare - A neighborhood resource sharing platform where residents can lend, borrow, or share tools, equipment, and services within their local community. Includes item tracking, user verification, and community event coordination features.',
                'status' => 'completed',
            ],
        ];

        foreach ($projects as $projectData) {
            Project::create($projectData);
        }

        // Create additional random projects using factory
        Project::factory(10)->create([
            'account_id' => fn () => $accounts->random()->id,
        ]);

        $this->command->info('Created 20 projects (10 specific + 10 random)');
    }
}
