<?php

namespace App\Filament\App\Resources\MarketResearchSessionResource\Pages;

use App\Filament\App\Resources\MarketResearchSessionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;

class ViewMarketResearchSession extends ViewRecord
{
    protected static string $resource = MarketResearchSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('research_market')
                ->label('Research Market')
                ->icon('heroicon-o-magnifying-glass')
                ->color('primary')
                ->action(function () {
                    // Redirect to create new research session
                    return redirect()->route('filament.app.resources.market-research-sessions.create');
                }),
            
            Actions\Action::make('reset_dashboard')
                ->label('Reset Dashboard')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->requiresConfirmation()
                ->modalHeading('Reset Dashboard')
                ->modalDescription('Are you sure you want to reset this dashboard? This will clear all generated data.')
                ->modalSubmitActionLabel('Yes, Reset')
                ->action(function () {
                    // Reset the session data (you can customize this logic)
                    $this->record->update([
                        'market_attractiveness' => null,
                        'market_size' => null,
                        'opportunity_zones' => null,
                        'research_scope' => null,
                        'strategic_implications' => null,
                        'customer_pain_points' => null,
                        'competitive_landscape' => null,
                        'enablers_barriers' => null,
                        'swot_analysis' => null,
                    ]);
                    
                    Notification::make()
                        ->title('Dashboard Reset')
                        ->body('The market research dashboard has been reset successfully.')
                        ->success()
                        ->send();
                }),
            
            Actions\Action::make('print_save_pdf')
                ->label('Print / Save as PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action(function () {
                    // Open print dialog
                    $this->js('window.print();');
                    
                    Notification::make()
                        ->title('Print Dialog Opened')
                        ->body('Use your browser\'s print dialog to save as PDF or print.')
                        ->info()
                        ->send();
                }),
            
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil-square'),
            
            Actions\Action::make('duplicate')
                ->label('Duplicate')
                ->icon('heroicon-o-document-duplicate')
                ->color('warning')
                ->action(function () {
                    $newSession = $this->record->replicate();
                    $newSession->generated_at = now();
                    $newSession->save();
                    
                    Notification::make()
                        ->title('Session Duplicated')
                        ->body('A new market research session has been created based on this one.')
                        ->success()
                        ->send();
                        
                    return redirect()->route('filament.app.resources.market-research-sessions.view', ['record' => $newSession->id]);
                }),
        ];
    }
    
    protected function getHeaderWidgets(): array
    {
        return [
            // You can add custom widgets here if needed
        ];
    }
    
    public function getTitle(): string 
    {
        return "Market Research Dashboard - {$this->record->industry} in {$this->record->region}";
    }
    
    public function getSubheading(): string
    {
        return "Focus: {$this->record->industry} in {$this->record->region} • Generated on " . $this->record->generated_at->format('M j, Y');
    }
}
