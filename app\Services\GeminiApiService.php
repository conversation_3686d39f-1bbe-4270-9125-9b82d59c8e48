<?php

namespace App\Services;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GeminiApiService
{
    protected string $apiKey;
    protected string $baseUrl;
    protected string $model;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->baseUrl = config('services.gemini.base_url');
        $this->model = config('services.gemini.model');
        
        if (empty($this->apiKey)) {
            throw new \Exception('Gemini API key is not configured. Please set GEMINI_API_KEY in your .env file.');
        }
    }

    /**
     * Make a call to the Gemini API with the given prompt and schema.
     */
    public function callGeminiAPI(string $prompt, array $schema, array $options = []): ?array
    {
        try {
            $url = "{$this->baseUrl}/{$this->model}:generateContent";
            
            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'responseMimeType' => 'application/json',
                    'responseSchema' => $schema,
                    'temperature' => $options['temperature'] ?? 0.7,
                    'maxOutputTokens' => $options['maxOutputTokens'] ?? 8192,
                ]
            ];

            Log::info('Making Gemini API call', [
                'url' => $url,
                'prompt_length' => strlen($prompt),
                'schema_keys' => array_keys($schema['properties'] ?? [])
            ]);

            $response = Http::timeout(120)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($url, $payload)
                ->withUrlParameters(['key' => $this->apiKey]);

            if (!$response->successful()) {
                $this->handleApiError($response);
                return null;
            }

            $responseData = $response->json();
            
            if (empty($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                Log::error('Empty response from Gemini API', ['response' => $responseData]);
                return null;
            }

            $jsonString = $responseData['candidates'][0]['content']['parts'][0]['text'];
            $parsedData = json_decode($jsonString, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Failed to parse Gemini API response as JSON', [
                    'json_error' => json_last_error_msg(),
                    'response' => $jsonString
                ]);
                return null;
            }

            Log::info('Gemini API call successful', [
                'response_keys' => array_keys($parsedData)
            ]);

            return $parsedData;

        } catch (\Exception $e) {
            Log::error('Gemini API call failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get comprehensive market research data for the dashboard.
     */
    public function getDashboardCoreUpdate(string $industry, string $region): ?array
    {
        $cacheKey = "market_research_{$industry}_{$region}";
        
        // Check cache first (cache for 1 hour)
        if (Cache::has($cacheKey)) {
            Log::info('Returning cached market research data', ['industry' => $industry, 'region' => $region]);
            return Cache::get($cacheKey);
        }

        $prompt = $this->buildDashboardPrompt($industry, $region);
        $schema = $this->getDashboardSchema();
        
        $result = $this->callGeminiAPI($prompt, $schema, [
            'temperature' => 0.7,
            'maxOutputTokens' => 8192
        ]);

        if ($result) {
            // Cache the result for 1 hour
            Cache::put($cacheKey, $result, 3600);
        }

        return $result;
    }

    /**
     * Get elaboration on a specific opportunity zone.
     */
    public function getOpportunityZoneElaboration(string $industry, string $region, string $zoneTitle, string $zoneDescription): ?array
    {
        $prompt = "As a market research analyst, provide detailed elaboration on this specific opportunity zone for {$industry} in {$region}:

**Opportunity Zone:** {$zoneTitle}
**Description:** {$zoneDescription}

Please provide detailed analysis focusing on:
1. Potential challenges and risks
2. Key success factors for capitalizing on this opportunity
3. Innovative approaches or strategies to pursue this opportunity

Be specific and actionable in your recommendations.";

        $schema = [
            'type' => 'object',
            'properties' => [
                'potentialChallenges' => [
                    'type' => 'array',
                    'items' => ['type' => 'string'],
                    'description' => 'List of potential challenges and risks'
                ],
                'keySuccessFactors' => [
                    'type' => 'array',
                    'items' => ['type' => 'string'],
                    'description' => 'Key factors for success'
                ],
                'innovativeApproaches' => [
                    'type' => 'array',
                    'items' => ['type' => 'string'],
                    'description' => 'Innovative strategies and approaches'
                ]
            ],
            'required' => ['potentialChallenges', 'keySuccessFactors', 'innovativeApproaches']
        ];

        return $this->callGeminiAPI($prompt, $schema);
    }

    /**
     * Get solution concepts for a customer pain point.
     */
    public function getPainPointSolutions(string $industry, string $region, string $painTitle, string $painDescription): ?array
    {
        $prompt = "As a market research analyst and innovation consultant, brainstorm 2-3 high-level solution concepts for this customer pain point in {$industry} in {$region}:

**Pain Point:** {$painTitle}
**Description:** {$painDescription}

For each solution concept, provide:
1. The solution idea (brief but clear)
2. How it specifically helps address this pain point

Focus on practical, implementable solutions that could realistically address this market need.";

        $schema = [
            'type' => 'object',
            'properties' => [
                'solutionConcepts' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'solutionIdea' => ['type' => 'string'],
                            'howItHelps' => ['type' => 'string']
                        ],
                        'required' => ['solutionIdea', 'howItHelps']
                    ],
                    'description' => 'Array of solution concepts'
                ]
            ],
            'required' => ['solutionConcepts']
        ];

        return $this->callGeminiAPI($prompt, $schema);
    }

    /**
     * Get actionable strategies for SWOT items.
     */
    public function getSWOTActionSuggestions(string $industry, string $region, string $swotCategory, string $swotItem): ?array
    {
        $actionPrompts = [
            'strengths' => 'leverage and maximize this strength',
            'weaknesses' => 'address and mitigate this weakness',
            'opportunities' => 'capitalize on this opportunity',
            'threats' => 'defend against and minimize this threat'
        ];

        $actionType = $actionPrompts[strtolower($swotCategory)] ?? 'address this item';

        $prompt = "As a strategic business consultant, suggest 2-3 specific, actionable strategies to {$actionType} for {$industry} in {$region}:

**{$swotCategory}:** {$swotItem}

Provide concrete, implementable actions that a business could take. Be specific and practical.";

        $schema = [
            'type' => 'object',
            'properties' => [
                'suggestedActions' => [
                    'type' => 'array',
                    'items' => ['type' => 'string'],
                    'description' => 'List of actionable strategies'
                ]
            ],
            'required' => ['suggestedActions']
        ];

        return $this->callGeminiAPI($prompt, $schema);
    }

    /**
     * Build the comprehensive prompt for dashboard data.
     */
    protected function buildDashboardPrompt(string $industry, string $region): string
    {
        return "Act as a senior market research analyst. Provide a comprehensive market research overview for the {$industry} industry in {$region}. 

Your analysis should be thorough, data-informed, and actionable. Focus on providing insights that would be valuable for entrepreneurs, investors, and business strategists looking to enter or expand in this market.

Please provide detailed analysis across all the following areas:

1. **Market Attractiveness**: Assess the overall attractiveness of this market (score 0-100) with qualitative reasoning and key considerations.

2. **Market Size**: Provide conceptual estimates for TAM (Total Addressable Market), SAM (Serviceable Addressable Market), SOM (Serviceable Obtainable Market), and projected CAGR.

3. **Opportunity Zones**: Identify 3-5 specific opportunity areas within this market that show particular promise.

4. **Research Scope**: Define the primary sub-sectors, geographic focus areas, and key inquiry themes for this market.

5. **Strategic Implications**: Highlight the most important strategic considerations for businesses in this space.

6. **Customer Pain Points**: Identify the top unmet needs and pain points that customers face in this market.

7. **Competitive Landscape**: Provide an overview of key competitors, their strengths, and differentiation factors.

8. **Enablers & Barriers**: List the key factors that enable success and the main barriers to entry/growth.

9. **SWOT Analysis**: Conduct a comprehensive SWOT analysis for this market.

Be specific to the {$industry} industry and {$region} region. Provide actionable insights rather than generic statements.";
    }

    /**
     * Get the JSON schema for dashboard data.
     */
    protected function getDashboardSchema(): array
    {
        return [
            'type' => 'object',
            'properties' => [
                'marketAttractiveness' => [
                    'type' => 'object',
                    'properties' => [
                        'score' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                        'statement' => ['type' => 'string'],
                        'keyConsiderations' => [
                            'type' => 'array',
                            'items' => [
                                'type' => 'object',
                                'properties' => [
                                    'consideration' => ['type' => 'string'],
                                    'type' => ['type' => 'string', 'enum' => ['positive', 'neutral', 'negative']]
                                ],
                                'required' => ['consideration', 'type']
                            ]
                        ]
                    ],
                    'required' => ['score', 'statement', 'keyConsiderations']
                ],
                'marketSize' => [
                    'type' => 'object',
                    'properties' => [
                        'tam' => ['type' => 'string'],
                        'sam' => ['type' => 'string'],
                        'som' => ['type' => 'string'],
                        'cagr' => ['type' => 'string']
                    ],
                    'required' => ['tam', 'sam', 'som', 'cagr']
                ],
                'opportunityZones' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'title' => ['type' => 'string'],
                            'description' => ['type' => 'string']
                        ],
                        'required' => ['title', 'description']
                    ]
                ],
                'researchScope' => [
                    'type' => 'object',
                    'properties' => [
                        'primarySubSectors' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'coreGeographicFocus' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'keyInquiryThemes' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ]
                    ],
                    'required' => ['primarySubSectors', 'coreGeographicFocus', 'keyInquiryThemes']
                ],
                'strategicImplications' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'title' => ['type' => 'string'],
                            'description' => ['type' => 'string']
                        ],
                        'required' => ['title', 'description']
                    ]
                ],
                'customerPainPoints' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'title' => ['type' => 'string'],
                            'description' => ['type' => 'string']
                        ],
                        'required' => ['title', 'description']
                    ]
                ],
                'competitiveLandscape' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'competitorName' => ['type' => 'string'],
                            'strength' => ['type' => 'string'],
                            'differentiation' => ['type' => 'string']
                        ],
                        'required' => ['competitorName', 'strength', 'differentiation']
                    ]
                ],
                'keyEnablersAndBarriers' => [
                    'type' => 'object',
                    'properties' => [
                        'enablers' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'barriers' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ]
                    ],
                    'required' => ['enablers', 'barriers']
                ],
                'marketSWOT' => [
                    'type' => 'object',
                    'properties' => [
                        'strengths' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'weaknesses' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'opportunities' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ],
                        'threats' => [
                            'type' => 'array',
                            'items' => ['type' => 'string']
                        ]
                    ],
                    'required' => ['strengths', 'weaknesses', 'opportunities', 'threats']
                ]
            ],
            'required' => [
                'marketAttractiveness',
                'marketSize',
                'opportunityZones',
                'researchScope',
                'strategicImplications',
                'customerPainPoints',
                'competitiveLandscape',
                'keyEnablersAndBarriers',
                'marketSWOT'
            ]
        ];
    }

    /**
     * Handle API errors and log them appropriately.
     */
    protected function handleApiError(Response $response): void
    {
        $statusCode = $response->status();
        $responseBody = $response->body();

        $errorMessage = "Gemini API error (HTTP {$statusCode})";
        
        try {
            $errorData = $response->json();
            if (isset($errorData['error']['message'])) {
                $errorMessage .= ": " . $errorData['error']['message'];
            }
        } catch (\Exception $e) {
            $errorMessage .= ": " . $responseBody;
        }

        Log::error($errorMessage, [
            'status_code' => $statusCode,
            'response_body' => $responseBody
        ]);

        // Handle specific error types
        switch ($statusCode) {
            case 401:
                throw new \Exception('Invalid Gemini API key. Please check your GEMINI_API_KEY configuration.');
            case 400:
                throw new \Exception('Bad request to Gemini API. Please check your request format.');
            case 429:
                throw new \Exception('Gemini API rate limit exceeded. Please try again later.');
            case 500:
            case 502:
            case 503:
                throw new \Exception('Gemini API server error. Please try again later.');
            default:
                throw new \Exception($errorMessage);
        }
    }
} 