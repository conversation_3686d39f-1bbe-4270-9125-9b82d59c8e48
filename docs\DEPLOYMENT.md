# Deployment Guide

This document provides comprehensive deployment strategies, server configuration, and operational procedures for the Venture Discovery Platform.

## 🚀 Deployment Overview

### Deployment Strategy
The Venture Discovery Platform supports multiple deployment strategies to accommodate different infrastructure requirements and scaling needs.

**Supported Deployment Methods:**
- **Traditional Server Deployment** (VPS/Dedicated)
- **Docker Containerization** (Recommended)
- **Cloud Platform Deployment** (AWS, DigitalOcean, etc.)
- **Managed Laravel Hosting** (Laravel Forge, Vapor)

### Infrastructure Requirements

**Minimum System Requirements:**
- **CPU**: 2 cores (4 cores recommended)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 20GB SSD (50GB recommended)
- **Network**: 100Mbps connection

**Software Requirements:**
- **PHP**: 8.2 or higher
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Web Server**: Nginx or Apache
- **Queue Worker**: Redis (recommended) or Database
- **Process Manager**: Supervisor
- **SSL Certificate**: Required for production

## 🐳 Docker Deployment (Recommended)

### Docker Compose Configuration

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: venture-discovery-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./storage/app/public:/var/www/public/storage
    networks:
      - venture-discovery
    depends_on:
      - database
      - redis
    environment:
      - APP_ENV=production
      - APP_DEBUG=false

  nginx:
    image: nginx:alpine
    container_name: venture-discovery-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx:/etc/nginx/conf.d
      - ./docker/ssl:/etc/ssl/certs
    networks:
      - venture-discovery
    depends_on:
      - app

  database:
    image: mysql:8.0
    container_name: venture-discovery-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: venture_discovery
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - dbdata:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - venture-discovery

  redis:
    image: redis:7-alpine
    container_name: venture-discovery-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
    networks:
      - venture-discovery

  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: venture-discovery-queue
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - venture-discovery
    depends_on:
      - database
      - redis
    environment:
      - APP_ENV=production

  reverb:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: venture-discovery-reverb
    restart: unless-stopped
    command: php artisan reverb:start --host=0.0.0.0 --port=8080
    working_dir: /var/www
    volumes:
      - ./:/var/www
    ports:
      - "8080:8080"
    networks:
      - venture-discovery
    depends_on:
      - database
      - redis
    environment:
      - APP_ENV=production

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local

networks:
  venture-discovery:
    driver: bridge
```

### Dockerfile

**Dockerfile:**
```dockerfile
FROM php:8.2-fpm

# Set working directory
WORKDIR /var/www

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libmcrypt-dev \
    libgd-dev \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    git \
    curl \
    supervisor

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy existing application directory contents
COPY . /var/www

# Copy existing application directory permissions
COPY --chown=www-data:www-data . /var/www

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install and build frontend assets
RUN npm install && npm run build

# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage \
    && chmod -R 755 /var/www/bootstrap/cache

# Copy supervisor configuration
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose port 9000 and start php-fpm server
EXPOSE 9000
CMD ["php-fpm"]
```

### Nginx Configuration

**docker/nginx/default.conf:**
```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/public;
    index index.php index.html index.htm;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/certs/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # WebSocket proxy for Laravel Reverb
    location /app/ {
        proxy_pass http://reverb:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static file caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Deployment Commands

**Deploy with Docker Compose:**
```bash
# Clone repository
git clone https://github.com/your-username/venture-discovery.git
cd venture-discovery

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Build and start containers
docker-compose up -d --build

# Generate application key
docker-compose exec app php artisan key:generate

# Run migrations
docker-compose exec app php artisan migrate --force

# Seed database
docker-compose exec app php artisan db:seed --force

# Create storage link
docker-compose exec app php artisan storage:link

# Cache configuration
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache
docker-compose exec app php artisan view:cache
```

## 🖥️ Traditional Server Deployment

### Server Setup (Ubuntu 22.04)

**1. System Updates:**
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install software-properties-common -y
```

**2. Install PHP 8.2:**
```bash
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-xml php8.2-gd \
    php8.2-curl php8.2-mbstring php8.2-zip php8.2-bcmath \
    php8.2-redis php8.2-intl -y
```

**3. Install Nginx:**
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

**4. Install MySQL:**
```bash
sudo apt install mysql-server -y
sudo mysql_secure_installation
```

**5. Install Redis:**
```bash
sudo apt install redis-server -y
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

**6. Install Composer:**
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

**7. Install Node.js:**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Application Deployment

**1. Clone and Setup:**
```bash
# Create web directory
sudo mkdir -p /var/www/venture-discovery
cd /var/www/venture-discovery

# Clone repository
sudo git clone https://github.com/your-username/venture-discovery.git .

# Set permissions
sudo chown -R www-data:www-data /var/www/venture-discovery
sudo chmod -R 755 /var/www/venture-discovery/storage
sudo chmod -R 755 /var/www/venture-discovery/bootstrap/cache
```

**2. Install Dependencies:**
```bash
# PHP dependencies
sudo -u www-data composer install --no-dev --optimize-autoloader

# Frontend dependencies
sudo -u www-data npm install
sudo -u www-data npm run build
```

**3. Environment Configuration:**
```bash
# Copy environment file
sudo -u www-data cp .env.example .env

# Generate application key
sudo -u www-data php artisan key:generate

# Edit environment variables
sudo nano .env
```

**4. Database Setup:**
```bash
# Create database
sudo mysql -u root -p
CREATE DATABASE venture_discovery;
CREATE USER 'venture_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON venture_discovery.* TO 'venture_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Run migrations
sudo -u www-data php artisan migrate --force

# Seed database
sudo -u www-data php artisan db:seed --force
```

**5. Optimize Application:**
```bash
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache
sudo -u www-data php artisan storage:link
```

### Nginx Configuration

**/etc/nginx/sites-available/venture-discovery:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/venture-discovery/public;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    # Static file caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

**Enable Site:**
```bash
sudo ln -s /etc/nginx/sites-available/venture-discovery /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Process Management (Supervisor)

**Install Supervisor:**
```bash
sudo apt install supervisor -y
```

**Queue Worker Configuration:**
**/etc/supervisor/conf.d/venture-discovery-worker.conf:**
```ini
[program:venture-discovery-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/venture-discovery/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/venture-discovery/storage/logs/worker.log
stopwaitsecs=3600
```

**Laravel Reverb Configuration:**
**/etc/supervisor/conf.d/venture-discovery-reverb.conf:**
```ini
[program:venture-discovery-reverb]
process_name=%(program_name)s
command=php /var/www/venture-discovery/artisan reverb:start
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/venture-discovery/storage/logs/reverb.log
```

**Start Services:**
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start venture-discovery-worker:*
sudo supervisorctl start venture-discovery-reverb
```

## ☁️ Cloud Platform Deployment

### AWS Deployment

**1. EC2 Instance Setup:**
- Launch Ubuntu 22.04 LTS instance
- Configure security groups (HTTP, HTTPS, SSH)
- Attach Elastic IP
- Follow traditional server deployment steps

**2. RDS Database:**
```bash
# Create RDS MySQL instance
# Update .env with RDS endpoint
DB_HOST=your-rds-endpoint.amazonaws.com
DB_DATABASE=venture_discovery
DB_USERNAME=admin
DB_PASSWORD=your-secure-password
```

**3. ElastiCache Redis:**
```bash
# Create ElastiCache Redis cluster
# Update .env with Redis endpoint
REDIS_HOST=your-redis-cluster.cache.amazonaws.com
REDIS_PORT=6379
```

**4. S3 Storage (Optional):**
```bash
# Configure S3 for file storage
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=venture-discovery-storage
```

### DigitalOcean Deployment

**1. Droplet Creation:**
```bash
# Create Ubuntu 22.04 droplet
# Configure firewall rules
# Follow traditional server deployment
```

**2. Managed Database:**
```bash
# Create managed MySQL database
# Update .env with connection details
```

**3. Spaces Storage (Optional):**
```bash
# Configure DigitalOcean Spaces
FILESYSTEM_DISK=spaces
DO_SPACES_KEY=your-spaces-key
DO_SPACES_SECRET=your-spaces-secret
DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com
DO_SPACES_REGION=nyc3
DO_SPACES_BUCKET=venture-discovery
```

## 🔧 Environment Configuration

### Production Environment Variables

**.env (Production):**
```env
# Application
APP_NAME="Venture Discovery Platform"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=venture_discovery
DB_USERNAME=venture_user
DB_PASSWORD=secure_password

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Queue
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Broadcasting
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your-app-id
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
REVERB_HOST=your-domain.com
REVERB_PORT=443
REVERB_SCHEME=https

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Security
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

### Security Configuration

**1. File Permissions:**
```bash
# Set proper permissions
sudo chown -R www-data:www-data /var/www/venture-discovery
sudo chmod -R 755 /var/www/venture-discovery
sudo chmod -R 775 /var/www/venture-discovery/storage
sudo chmod -R 775 /var/www/venture-discovery/bootstrap/cache
```

**2. Environment File Security:**
```bash
# Secure .env file
sudo chmod 600 /var/www/venture-discovery/.env
sudo chown www-data:www-data /var/www/venture-discovery/.env
```

**3. Firewall Configuration:**
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 3306  # MySQL (if needed)
sudo ufw allow 6379  # Redis (if needed)
```

## 📊 Monitoring and Logging

### Application Monitoring

**1. Laravel Telescope (Development):**
```bash
# Install Telescope for debugging
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

**2. Log Management:**
```bash
# Configure log rotation
sudo nano /etc/logrotate.d/venture-discovery

/var/www/venture-discovery/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

**3. Health Checks:**
```bash
# Create health check endpoint
# Monitor application status
curl https://your-domain.com/health
```

### Performance Monitoring

**1. Server Monitoring:**
```bash
# Install htop for system monitoring
sudo apt install htop -y

# Monitor processes
htop
```

**2. Database Monitoring:**
```bash
# Monitor MySQL performance
sudo mysql -u root -p
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
```

**3. Queue Monitoring:**
```bash
# Monitor queue status
php artisan queue:monitor
php artisan horizon:status  # If using Horizon
```

## 🔄 Backup and Recovery

### Database Backup

**Automated Backup Script:**
```bash
#!/bin/bash
# /usr/local/bin/backup-venture-discovery.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/venture-discovery"
DB_NAME="venture_discovery"
DB_USER="venture_user"
DB_PASS="secure_password"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
```

**Schedule Backup:**
```bash
# Add to crontab
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/backup-venture-discovery.sh
```

### Application Backup

**File Backup Script:**
```bash
#!/bin/bash
# Backup application files

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/venture-discovery"
APP_DIR="/var/www/venture-discovery"

# Create backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='storage/logs' \
    --exclude='storage/framework/cache' \
    $APP_DIR

echo "Application backup completed: $BACKUP_DIR/app_backup_$DATE.tar.gz"
```

## 🚀 Deployment Automation

### GitHub Actions Deployment

**.github/workflows/deploy.yml:**
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/venture-discovery
          git pull origin main
          composer install --no-dev --optimize-autoloader
          npm install && npm run build
          php artisan migrate --force
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          sudo supervisorctl restart venture-discovery-worker:*
          sudo systemctl reload nginx
```

### Zero-Downtime Deployment

**Deployment Script:**
```bash
#!/bin/bash
# zero-downtime-deploy.sh

APP_DIR="/var/www/venture-discovery"
RELEASES_DIR="$APP_DIR/releases"
CURRENT_RELEASE=$(date +%Y%m%d_%H%M%S)
RELEASE_DIR="$RELEASES_DIR/$CURRENT_RELEASE"

# Create release directory
mkdir -p $RELEASE_DIR

# Clone latest code
git clone https://github.com/your-username/venture-discovery.git $RELEASE_DIR

# Install dependencies
cd $RELEASE_DIR
composer install --no-dev --optimize-autoloader
npm install && npm run build

# Copy environment file
cp $APP_DIR/.env $RELEASE_DIR/.env

# Run migrations
php artisan migrate --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link

# Update symlink
ln -nfs $RELEASE_DIR $APP_DIR/current

# Restart services
sudo supervisorctl restart venture-discovery-worker:*
sudo systemctl reload nginx

# Cleanup old releases (keep last 5)
cd $RELEASES_DIR
ls -t | tail -n +6 | xargs rm -rf

echo "Deployment completed: $CURRENT_RELEASE"
```

## 🔍 Troubleshooting

### Common Issues

**1. Permission Issues:**
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
```

**2. Queue Not Processing:**
```bash
# Check queue worker status
sudo supervisorctl status venture-discovery-worker:*

# Restart queue workers
sudo supervisorctl restart venture-discovery-worker:*
```

**3. WebSocket Connection Issues:**
```bash
# Check Reverb status
sudo supervisorctl status venture-discovery-reverb

# Check Reverb logs
tail -f storage/logs/reverb.log
```

**4. Database Connection Issues:**
```bash
# Test database connection
php artisan tinker
DB::connection()->getPdo();
```

### Performance Optimization

**1. OPcache Configuration:**
```ini
; /etc/php/8.2/fpm/conf.d/10-opcache.ini
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
opcache.save_comments=1
opcache.fast_shutdown=1
```

**2. PHP-FPM Optimization:**
```ini
; /etc/php/8.2/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

This comprehensive deployment guide ensures a secure, scalable, and maintainable production environment for the Venture Discovery Platform. 