<?php

namespace App\Livewire;

use App\Jobs\GenerateLeanCanvasSection;
use App\Models\Project;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class LeanCanvasDisplay extends Component
{
    public ?Project $project = null;

    public array $sections = [];

    public array $loadingStates = [];

    public array $errors = [];

    public bool $showModal = false;

    public string $modalTitle = '';

    public string $modalContent = '';

    // Define all Lean Canvas sections
    public array $sectionConfig = [
        'problem' => [
            'title' => 'Problem',
            'key' => 'B',
            'content_type' => 'lean_canvas_problem',
            'description' => 'Top 1-3 problems',
        ],
        'solution' => [
            'title' => 'Solution',
            'key' => 'D',
            'content_type' => 'lean_canvas_solution',
            'description' => 'Top 3 features',
        ],
        'unique_value_proposition' => [
            'title' => 'Unique Value Prop.',
            'key' => 'E',
            'content_type' => 'lean_canvas_unique_value_proposition',
            'description' => 'Single, clear message',
        ],
        'unfair_advantage' => [
            'title' => 'Unfair Advantage',
            'key' => 'J',
            'content_type' => 'lean_canvas_unfair_advantage',
            'description' => "Can't be copied",
        ],
        'customer_segments' => [
            'title' => 'Customer Segments',
            'key' => 'A',
            'content_type' => 'lean_canvas_customer_segments',
            'description' => 'Target customers',
        ],
        'existing_alternatives' => [
            'title' => 'Existing Alternatives',
            'key' => 'C',
            'content_type' => 'lean_canvas_existing_alternatives',
            'description' => 'List how these problems are solved today',
        ],
        'key_metrics' => [
            'title' => 'Key Metrics',
            'key' => 'I',
            'content_type' => 'lean_canvas_key_metrics',
            'description' => 'Key numbers that tell you how your business is doing',
        ],
        'channels' => [
            'title' => 'Channels',
            'key' => 'F',
            'content_type' => 'lean_canvas_channels',
            'description' => 'Path to customers',
        ],
        'cost_structure' => [
            'title' => 'Cost Structure',
            'key' => 'H',
            'content_type' => 'lean_canvas_cost_structure',
            'description' => 'Customer acquisition costs, distribution costs, hosting, people, etc.',
        ],
        'revenue_streams' => [
            'title' => 'Revenue Streams',
            'key' => 'G',
            'content_type' => 'lean_canvas_revenue_streams',
            'description' => 'Revenue model, life time value, revenue, gross margin',
        ],
    ];

    public function mount($record = null)
    {
        $this->project = $record instanceof Project ? $record : $this->getProject();
        $this->loadAllSections();
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            // Try to get from Filament context or throw error
            throw new \Exception('Project not found');
        }

        return $this->project;
    }

    public function loadAllSections()
    {
        if (! $this->project) {
            return;
        }

        foreach ($this->sectionConfig as $sectionKey => $config) {
            $content = $this->project->generatedContents()
                ->where('content_type', $config['content_type'])
                ->latest()
                ->first();

            $this->sections[$sectionKey] = $content;
            $this->loadingStates[$sectionKey] = false;
            $this->errors[$sectionKey] = null;
        }
    }

    public function generateSection($sectionKey)
    {
        if (! isset($this->sectionConfig[$sectionKey])) {
            return;
        }

        // Check if content already exists
        $existingContent = $this->project->generatedContents()
            ->where('content_type', $this->sectionConfig[$sectionKey]['content_type'])
            ->latest()
            ->first();

        if ($existingContent) {
            Log::info('Section already exists, skipping generation', [
                'project_id' => $this->project->id,
                'section' => $sectionKey,
            ]);

            return;
        }

        $this->loadingStates[$sectionKey] = true;
        $this->errors[$sectionKey] = null;

        try {
            // Dispatch the job for background processing
            GenerateLeanCanvasSection::dispatch(
                $this->project,
                $sectionKey,
                false // isRegeneration = false
            );

            Log::info('Dispatched Lean Canvas section generation job', [
                'project_id' => $this->project->id,
                'section' => $sectionKey,
                'content_type' => $this->sectionConfig[$sectionKey]['content_type'],
            ]);

        } catch (\Exception $e) {
            $this->errors[$sectionKey] = 'Failed to start generation: '.$e->getMessage();
            $this->loadingStates[$sectionKey] = false;

            Log::error('Failed to dispatch Lean Canvas section generation job', [
                'project_id' => $this->project->id,
                'section' => $sectionKey,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function regenerateSection($sectionKey)
    {
        if (! isset($this->sectionConfig[$sectionKey])) {
            return;
        }

        $this->loadingStates[$sectionKey] = true;
        $this->errors[$sectionKey] = null;

        try {
            // Delete existing content
            $this->project->generatedContents()
                ->where('content_type', $this->sectionConfig[$sectionKey]['content_type'])
                ->delete();

            $this->sections[$sectionKey] = null;

            // Dispatch the job for background regeneration
            GenerateLeanCanvasSection::dispatch(
                $this->project,
                $sectionKey,
                true // isRegeneration = true
            );

            Log::info('Dispatched Lean Canvas section regeneration job', [
                'project_id' => $this->project->id,
                'section' => $sectionKey,
                'content_type' => $this->sectionConfig[$sectionKey]['content_type'],
            ]);

        } catch (\Exception $e) {
            $this->errors[$sectionKey] = 'Failed to start regeneration: '.$e->getMessage();
            $this->loadingStates[$sectionKey] = false;

            Log::error('Failed to dispatch Lean Canvas section regeneration job', [
                'project_id' => $this->project->id,
                'section' => $sectionKey,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function showFullText($sectionKey)
    {
        if (! isset($this->sectionConfig[$sectionKey])) {
            return;
        }

        $content = $this->getSectionContent($sectionKey);
        if (! $content) {
            return;
        }

        $this->modalTitle = $this->sectionConfig[$sectionKey]['title'];
        $this->modalContent = $content;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->modalTitle = '';
        $this->modalContent = '';
    }

    public function getSectionContent($sectionKey): ?string
    {
        $content = $this->sections[$sectionKey] ?? null;
        if (! $content) {
            return null;
        }

        $data = $content->content_data;

        // Try different possible keys for the content
        $possibleKeys = [
            $sectionKey,
            str_replace('_', ' ', $sectionKey),
            $this->sectionConfig[$sectionKey]['title'],
            'content',
            'text',
        ];

        foreach ($possibleKeys as $key) {
            if (isset($data[$key])) {
                return $data[$key];
            }
        }

        // If no specific key found, return the first string value
        foreach ($data as $value) {
            if (is_string($value) && strlen($value) > 10) {
                return $value;
            }
        }

        return null;
    }

    public function getGeneratedAt($sectionKey): ?string
    {
        $content = $this->sections[$sectionKey] ?? null;
        if (! $content) {
            return null;
        }

        /** @var \App\Models\GeneratedContent $content */
        return $content->created_at->diffForHumans();
    }

    public function getHighLevelConcept(): string
    {
        if (! $this->project) {
            return 'High-level concept for your startup idea.';
        }

        // Extract a short concept from the project prompt
        $prompt = $this->project->input_prompt;

        // Try to extract the main concept (first sentence or up to 100 characters)
        $concept = explode('.', $prompt)[0];
        if (strlen($concept) > 100) {
            $concept = substr($concept, 0, 97).'...';
        }

        return $concept.'.';
    }

    /**
     * Poll for updates to check if content generation is complete
     */
    public function pollForUpdates()
    {
        $this->loadAllSections();

        // Check if all content has been generated
        if ($this->hasAllContentGenerated()) {
            $this->dispatch('stop-polling');
        }
    }

    /**
     * Check if all content has been generated
     */
    public function hasAllContentGenerated(): bool
    {
        foreach ($this->sectionConfig as $sectionKey => $config) {
            if (empty($this->sections[$sectionKey])) {
                return false;
            }
        }

        return true;
    }

    public function render()
    {
        return view('livewire.lean-canvas-display');
    }
}
