<?php

return [
    /*
    |--------------------------------------------------------------------------
    | OpenAI API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for OpenAI API integration.
    | You can configure the API key, organization, default models, and
    | other settings here.
    |
    */

    'api_key' => env('OPENAI_API_KEY'),

    'organization' => env('OPENAI_ORGANIZATION'),

    'default_model' => env('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini'),

    'models' => [
        'chat' => env('OPENAI_CHAT_MODEL', 'gpt-4o-mini'),
        'completion' => env('OPENAI_COMPLETION_MODEL', 'gpt-3.5-turbo-instruct'),
        'embedding' => env('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
    ],

    'limits' => [
        'max_tokens' => (int) env('OPENAI_MAX_TOKENS', 2000),
        'temperature' => (float) env('OPENAI_TEMPERATURE', 0.7),
        'top_p' => (float) env('OPENAI_TOP_P', 1.0),
        'frequency_penalty' => (float) env('OPENAI_FREQUENCY_PENALTY', 0.0),
        'presence_penalty' => (float) env('OPENAI_PRESENCE_PENALTY', 0.0),
    ],

    'retry' => [
        'max_attempts' => (int) env('OPENAI_MAX_RETRY_ATTEMPTS', 3),
        'delay_ms' => (int) env('OPENAI_RETRY_DELAY_MS', 1000),
        'backoff_multiplier' => (float) env('OPENAI_BACKOFF_MULTIPLIER', 2.0),
    ],

    'cache' => [
        'enabled' => env('OPENAI_CACHE_ENABLED', true),
        'ttl' => (int) env('OPENAI_CACHE_TTL', 3600), // 1 hour
        'prefix' => env('OPENAI_CACHE_PREFIX', 'openai'),
    ],

    'logging' => [
        'enabled' => env('OPENAI_LOGGING_ENABLED', true),
        'channel' => env('OPENAI_LOG_CHANNEL', 'single'),
        'level' => env('OPENAI_LOG_LEVEL', 'info'),
    ],

    'cost_tracking' => [
        'enabled' => env('OPENAI_COST_TRACKING_ENABLED', true),
        'pricing' => [
            'gpt-4o' => [
                'input' => 0.0025,  // per 1K tokens
                'output' => 0.01,   // per 1K tokens
            ],
            'gpt-4o-mini' => [
                'input' => 0.00015, // per 1K tokens
                'output' => 0.0006, // per 1K tokens
            ],
            'gpt-4-turbo' => [
                'input' => 0.01,    // per 1K tokens
                'output' => 0.03,   // per 1K tokens
            ],
            'gpt-3.5-turbo' => [
                'input' => 0.0005,  // per 1K tokens
                'output' => 0.0015, // per 1K tokens
            ],
        ],
    ],
];
