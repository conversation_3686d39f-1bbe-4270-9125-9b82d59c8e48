<?php

namespace Tests\Unit;

use App\Services\OpenAiService;
use Exception;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class OpenAiServiceTest extends TestCase
{
    public function test_constructor_throws_exception_when_api_key_missing(): void
    {
        Config::set('openai.api_key', null);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('OpenAI API key is not configured');

        new OpenAiService;
    }

    public function test_count_tokens_returns_approximate_count(): void
    {
        Config::set('openai.api_key', 'test-key');
        $service = new OpenAiService;

        $text = 'This is a test string with multiple words';
        $expectedTokens = (int) ceil(strlen($text) / 4);

        $result = $service->countTokens($text);

        $this->assertEquals($expectedTokens, $result);
    }

    public function test_service_can_be_instantiated_with_valid_config(): void
    {
        Config::set('openai', [
            'api_key' => 'test-api-key',
            'organization' => 'test-org',
            'models' => [
                'chat' => 'gpt-4o-mini',
                'completion' => 'gpt-3.5-turbo-instruct',
                'embedding' => 'text-embedding-3-small',
            ],
            'limits' => [
                'max_tokens' => 2000,
                'temperature' => 0.7,
                'top_p' => 1.0,
                'frequency_penalty' => 0.0,
                'presence_penalty' => 0.0,
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay_ms' => 1000,
                'backoff_multiplier' => 2.0,
            ],
            'cache' => [
                'enabled' => true,
                'ttl' => 3600,
                'prefix' => 'openai',
            ],
            'logging' => [
                'enabled' => true,
                'channel' => 'single',
                'level' => 'info',
            ],
            'cost_tracking' => [
                'enabled' => true,
                'pricing' => [
                    'gpt-4o-mini' => [
                        'input' => 0.00015,
                        'output' => 0.0006,
                    ],
                ],
            ],
        ]);

        $service = new OpenAiService;
        $this->assertInstanceOf(OpenAiService::class, $service);
    }

    public function test_service_is_registered_in_container(): void
    {
        Config::set('openai.api_key', 'test-key');

        $service = app(OpenAiService::class);
        $this->assertInstanceOf(OpenAiService::class, $service);

        // Test alias
        $aliasService = app('openai');
        $this->assertInstanceOf(OpenAiService::class, $aliasService);
    }

    public function test_extract_json_from_markdown_works_correctly(): void
    {
        $service = app(OpenAiService::class);

        // Test JSON wrapped in markdown code blocks
        $markdownContent = "```json\n{\n    \"hypothesis\": \"Test hypothesis\",\n    \"criticality\": \"High\"\n}\n```";

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('extractJsonFromMarkdown');
        $method->setAccessible(true);

        $result = $method->invoke($service, $markdownContent);

        $this->assertJson($result);
        $decoded = json_decode($result, true);
        $this->assertEquals('Test hypothesis', $decoded['hypothesis']);
        $this->assertEquals('High', $decoded['criticality']);
    }

    public function test_extract_json_from_markdown_handles_plain_json(): void
    {
        $service = app(OpenAiService::class);

        // Test plain JSON without markdown
        $plainJson = '{"hypothesis": "Test hypothesis", "criticality": "High"}';

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('extractJsonFromMarkdown');
        $method->setAccessible(true);

        $result = $method->invoke($service, $plainJson);

        $this->assertJson($result);
        $decoded = json_decode($result, true);
        $this->assertEquals('Test hypothesis', $decoded['hypothesis']);
        $this->assertEquals('High', $decoded['criticality']);
    }
}
