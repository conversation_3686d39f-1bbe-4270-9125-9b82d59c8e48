<?php

namespace App\Filament\App\Resources\ProjectResource\Pages;

use App\Filament\App\Resources\ProjectResource;
use App\Jobs\GenerateCriticalHypotheses;
use App\Jobs\GenerateInterviewQuestionnaireJob;
use App\Jobs\GenerateLeanCanvasSection;
use App\Jobs\GenerateMarketSizingAnalysisJob;
use App\Jobs\GenerateStorytellingContentJob;
use App\Models\Account;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateProject extends CreateRecord
{
    protected static string $resource = ProjectResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        /** @var Account|null $account */
        $account = auth('account')->user();

        if (!$account) {
            throw new \Exception('User must be authenticated to create a project');
        }

        $data['account_id'] = $account->id;
        $data['status'] = 'pending';

        return $data;
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Project created successfully!')
            ->body('We\'ll start analyzing your startup idea and generating business assets.');
    }

    protected function afterCreate(): void
    {
        $record = $this->getRecord();
        $this->dispatchContentGenerationJobs($record);
    }

    /**
     * Dispatch all content generation jobs for a project
     * Made public for testing purposes
     */
    public function dispatchContentGenerationJobs($record): void
    {
        // Ensure we have a Project model
        if (! $record instanceof \App\Models\Project) {
            Log::error('Expected Project model in afterCreate', [
                'actual_type' => get_class($record),
            ]);

            return;
        }

        // Define all Lean Canvas sections to generate
        $leanCanvasSections = [
            'problem',
            'solution',
            'unique_value_proposition',
            'customer_segments',
            'existing_alternatives',
            'key_metrics',
            'channels',
            'unfair_advantage',
            'cost_structure',
            'revenue_streams',
        ];

        // Dispatch Lean Canvas section generation jobs
        foreach ($leanCanvasSections as $sectionKey) {
            try {
                GenerateLeanCanvasSection::dispatch($record, $sectionKey, false);

                Log::info('Lean Canvas section generation job dispatched for new project', [
                    'project_id' => $record->id,
                    'section' => $sectionKey,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to dispatch Lean Canvas section generation job for new project', [
                    'project_id' => $record->id,
                    'section' => $sectionKey,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Dispatch Critical Hypotheses generation job
        try {
            GenerateCriticalHypotheses::dispatch($record);

            Log::info('Critical hypotheses generation job dispatched for new project', [
                'project_id' => $record->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch critical hypotheses generation job for new project', [
                'project_id' => $record->id,
                'error' => $e->getMessage(),
            ]);
        }

        // Dispatch Interview Questionnaire generation job
        try {
            GenerateInterviewQuestionnaireJob::dispatch($record, false);

            Log::info('Interview questionnaire generation job dispatched for new project', [
                'project_id' => $record->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch interview questionnaire generation job for new project', [
                'project_id' => $record->id,
                'error' => $e->getMessage(),
            ]);
        }

        // Dispatch Storytelling Content generation job
        try {
            GenerateStorytellingContentJob::dispatch($record, false);

            Log::info('Storytelling content generation job dispatched for new project', [
                'project_id' => $record->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch storytelling content generation job for new project', [
                'project_id' => $record->id,
                'error' => $e->getMessage(),
            ]);
        }

        // Dispatch Market Sizing Analysis generation job
        try {
            GenerateMarketSizingAnalysisJob::dispatch($record);

            Log::info('Market sizing analysis generation job dispatched for new project', [
                'project_id' => $record->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch market sizing analysis generation job for new project', [
                'project_id' => $record->id,
                'error' => $e->getMessage(),
            ]);
        }

        Log::info('All content generation jobs dispatched for new project', [
            'project_id' => $record->id,
            'total_jobs' => 14, // 10 Lean Canvas sections + Critical Hypotheses + Interview + Storytelling + Market Sizing
        ]);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
