<?php

namespace App\Filament\App\Widgets;

use App\Models\Account;
use App\Models\Project;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProjectStatsOverview extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        /** @var Account|null $account */
        $account = auth('account')->user();

        if (!$account) {
            return [];
        }

        $totalProjects = Project::where('account_id', $account->id)->count();
        $pendingProjects = Project::where('account_id', $account->id)->where('status', 'pending')->count();
        $processingProjects = Project::where('account_id', $account->id)->where('status', 'processing')->count();
        $completedProjects = Project::where('account_id', $account->id)->where('status', 'completed')->count();

        return [
            Stat::make('Total Projects', $totalProjects)
                ->description('All your startup projects')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('Pending Projects', $pendingProjects)
                ->description('Waiting to be processed')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Processing', $processingProjects)
                ->description('Currently being analyzed')
                ->descriptionIcon('heroicon-m-arrow-path')
                ->color('info'),

            Stat::make('Completed Projects', $completedProjects)
                ->description('Ready business assets')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
        ];
    }
}
