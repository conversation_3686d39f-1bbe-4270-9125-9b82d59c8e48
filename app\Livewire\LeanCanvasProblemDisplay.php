<?php

namespace App\Livewire;

use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Livewire\Component;

class LeanCanvasProblemDisplay extends Component
{
    public ?Project $project = null;

    public ?GeneratedContent $problemContent = null;

    public bool $isGenerating = false;

    public ?string $error = null;

    public function mount($record = null): void
    {
        // Initialize the project from the passed record
        if ($record instanceof Project) {
            $this->project = $record;
        } elseif (is_array($record) && isset($record['id'])) {
            $this->project = Project::find($record['id']);
        } elseif (is_numeric($record)) {
            $this->project = Project::find($record);
        }

        if ($this->project) {
            $this->loadProblemContent();
        }
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            throw new \Exception('Project not initialized in Livewire component');
        }

        return $this->project;
    }

    public function loadProblemContent(): void
    {
        $this->problemContent = $this->getProject()->getGeneratedContent('lean_canvas_problem');
    }

    public function generateProblemSection(): void
    {
        $this->isGenerating = true;
        $this->error = null;

        try {
            $contentService = app(ContentGenerationService::class);
            $this->problemContent = $contentService->generateProblemSection($this->getProject());

            $this->dispatch('problem-section-generated', [
                'message' => 'Problem section generated successfully!',
            ]);
        } catch (\Exception $e) {
            $this->error = 'Failed to generate problem section: '.$e->getMessage();

            $this->dispatch('problem-section-error', [
                'message' => $this->error,
            ]);
        } finally {
            $this->isGenerating = false;
        }
    }

    public function regenerateProblemSection(): void
    {
        $this->isGenerating = true;
        $this->error = null;

        try {
            $contentService = app(ContentGenerationService::class);
            $this->problemContent = $contentService->regenerateContent($this->getProject(), 'lean_canvas_problem');

            $this->dispatch('problem-section-regenerated', [
                'message' => 'Problem section regenerated successfully!',
            ]);
        } catch (\Exception $e) {
            $this->error = 'Failed to regenerate problem section: '.$e->getMessage();

            $this->dispatch('problem-section-error', [
                'message' => $this->error,
            ]);
        } finally {
            $this->isGenerating = false;
        }
    }

    public function getProblemText(): ?string
    {
        return $this->problemContent?->getContentValue('problem');
    }

    public function getGeneratedAt(): ?string
    {
        $generatedAt = $this->problemContent?->getContentValue('generated_at');

        if ($generatedAt) {
            return \Carbon\Carbon::parse($generatedAt)->diffForHumans();
        }

        return null;
    }

    public function render()
    {
        return view('livewire.lean-canvas-problem-display');
    }
}
