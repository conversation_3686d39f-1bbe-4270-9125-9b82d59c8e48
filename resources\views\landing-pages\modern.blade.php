<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="{{ $meta['viewport'] }}">
    <meta name="description" content="{{ $meta['description'] }}">
    <meta name="keywords" content="{{ $meta['keywords'] }}">
    <meta name="author" content="{{ $meta['author'] }}">
    <title>{{ $meta['title'] }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: #ffffff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e2e8f0;
            z-index: 1000;
            padding: 16px 0;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            text-decoration: none;
        }
        
        /* Hero Section */
        .hero {
            padding: 140px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 24px;
            font-weight: 800;
            line-height: 1.2;
        }
        
        .hero .subtitle {
            font-size: 1.25rem;
            margin-bottom: 40px;
            opacity: 0.95;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Button Styles */
        .btn {
            display: inline-block;
            padding: 16px 32px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        
        .btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
        }
        
        .btn-secondary {
            background: transparent;
            border: 2px solid #ffffff;
            color: #ffffff;
            margin-left: 16px;
            box-shadow: none;
        }
        
        .btn-secondary:hover {
            background: #ffffff;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        /* Section Styles */
        .section {
            padding: 100px 0;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #2d3748;
            font-weight: 700;
        }
        
        .section-subtitle {
            font-size: 1.2rem;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* Problem & Solution */
        .problem-solution {
            background: #f7fafc;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }
        
        .column h3 {
            font-size: 1.8rem;
            margin-bottom: 24px;
            color: #2d3748;
            font-weight: 600;
        }
        
        .column p {
            font-size: 1.1rem;
            color: #4a5568;
            line-height: 1.7;
        }
        
        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        /* Stats Section */
        .stats {
            background: #2d3748;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 8px;
            color: #ff6b6b;
            font-weight: 800;
        }
        
        .stat-item p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        /* CTA Section */
        .cta {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .cta h2 {
            color: white;
            margin-bottom: 24px;
        }
        
        .cta p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.95;
        }
        
        /* Footer */
        .footer {
            background: #1a202c;
            color: white;
            padding: 60px 0 40px;
            text-align: center;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .section h2 {
                font-size: 2rem;
            }
            
            .btn-secondary {
                margin-left: 0;
                margin-top: 16px;
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }
            
            .container {
                padding: 0 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="container">
            <div class="nav">
                <a href="#" class="logo">
                    {{ $content['storytelling']['startup_naming']['business_name'] ?? $content['storytelling']['startup_naming']['name'] ?? $meta['title'] }}
                </a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>{{ $content['storytelling']['startup_naming']['tagline'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'Transform Your Idea Into Reality' }}</h1>
                <p class="subtitle">
                    {{ $content['storytelling']['elevator_pitch']['pitch'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? substr($project->input_prompt, 0, 200) . '...' }}
                </p>
                <div class="hero-actions">
                    <a href="#contact" class="btn">Get Started</a>
                    <a href="#features" class="btn btn-secondary">Learn More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem & Solution Section -->
    <section class="section problem-solution">
        <div class="container">
            <div class="two-column">
                <div class="column">
                    <h3>The Problem</h3>
                    <p>
                        @if(isset($content['lean_canvas']['problem']['content']))
                            {{ $content['lean_canvas']['problem']['content'] }}
                        @else
                            Every great solution starts with understanding a real problem that people face every day.
                        @endif
                    </p>
                </div>
                <div class="column">
                    <h3>Our Solution</h3>
                    <p>
                        @if(isset($content['lean_canvas']['solution']['content']))
                            {{ $content['lean_canvas']['solution']['content'] }}
                        @else
                            We've developed an innovative approach that addresses this challenge in a way that's both effective and user-friendly.
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section" id="features">
        <div class="container">
            <div class="section-header">
                <h2>Why Choose Us</h2>
                <p class="section-subtitle">
                    @if(isset($content['lean_canvas']['unfair_advantage']['content']))
                        {{ $content['lean_canvas']['unfair_advantage']['content'] }}
                    @else
                        Discover what makes our solution unique and why customers choose us over the competition.
                    @endif
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h3>Fast & Efficient</h3>
                    <p>Get results quickly with our streamlined approach designed for maximum efficiency.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>Secure & Reliable</h3>
                    <p>Your data and privacy are protected with enterprise-grade security measures.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💡</div>
                    <h3>Innovative Approach</h3>
                    <p>Cutting-edge technology combined with user-centered design for optimal results.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    @if(isset($content['lean_canvas']['key_metrics']))
    <section class="section stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>10K+</h3>
                    <p>Happy Customers</p>
                </div>
                <div class="stat-item">
                    <h3>99%</h3>
                    <p>Satisfaction Rate</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>Support Available</p>
                </div>
                <div class="stat-item">
                    <h3>50+</h3>
                    <p>Countries Served</p>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="section cta" id="contact">
        <div class="container">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of satisfied customers who have transformed their business with our solution.</p>
            <a href="#" class="btn">Start Your Journey Today</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; {{ date('Y') }} {{ $content['storytelling']['startup_naming']['business_name'] ?? $meta['title'] }}. All rights reserved.</p>
        </div>
    </footer>
</body>
</html> 