#!/bin/bash

# Venture Discovery - Database Reseeding Script
# This script provides a convenient way to refresh the database with fresh seed data

echo "🌱 Venture Discovery - Database Reseeding"
echo "=========================================="
echo ""

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: This script must be run from the Laravel project root directory"
    exit 1
fi

# Ask for confirmation
echo "⚠️  This will drop all existing data and reseed the database."
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo ""
echo "🔄 Starting database refresh..."

# Clear caches
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Fresh migration with seeders
echo "🗃️  Running fresh migration with seeders..."
php artisan migrate:fresh --seed

# Check if successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Database reseeding completed successfully!"
    echo ""
    echo "🔑 Login Credentials:"
    echo "   🔐 Admin Panel: <EMAIL> / password"
    echo "   🔐 Super Admin: <EMAIL> / password"
    echo "   🔐 Demo Admin: <EMAIL> / password"
    echo "   👤 App Users: <EMAIL> / password"
    echo "               <EMAIL> / password"
    echo "               <EMAIL> / password"
    echo "               <EMAIL> / password"
    echo ""
    echo "🚀 You can now access the application with fresh data!"
else
    echo ""
    echo "❌ Database reseeding failed. Please check the error messages above."
    exit 1
fi 