<?php

namespace App\Livewire;

use App\Jobs\GenerateCriticalHypotheses;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class CriticalHypothesesDisplay extends Component
{
    public ?Project $project = null;

    public array $hypotheses = [];

    public array $loadingStates = [
        'desirability' => false,
        'viability' => false,
        'feasibility' => false,
        'all' => false,
    ];

    public array $errorMessages = [];

    public bool $showSuccessMessage = false;

    public string $successMessage = '';

    protected ?ContentGenerationService $contentService = null;

    public function mount(?Project $project = null): void
    {
        $this->project = $project;
        $this->loadExistingHypotheses();
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            throw new Exception('Project not available');
        }

        return $this->project;
    }

    protected function getContentService(): ContentGenerationService
    {
        if (! $this->contentService) {
            $this->contentService = app(ContentGenerationService::class);
        }

        return $this->contentService;
    }

    public function loadExistingHypotheses(): void
    {
        try {
            $project = $this->getProject();
            $this->hypotheses = $this->getContentService()->getCriticalHypotheses($project);
            $this->clearErrors();
        } catch (Exception $e) {
            Log::error('Failed to load existing critical hypotheses', [
                'project_id' => $this->project?->id,
                'error' => $e->getMessage(),
            ]);
            $this->setError('general', 'Failed to load existing hypotheses: '.$e->getMessage());
        }
    }

    public function generateAllHypotheses(): void
    {
        $this->loadingStates['all'] = true;
        $this->clearErrors();
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateCriticalHypotheses::dispatch($project);

            $this->showSuccess('Critical hypotheses generation started! This may take a few moments.');

            Log::info('Dispatched critical hypotheses generation job', [
                'project_id' => $project->id,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to dispatch critical hypotheses generation job', [
                'project_id' => $this->project?->id,
                'error' => $e->getMessage(),
            ]);
            $this->setError('all', 'Failed to start generation: '.$e->getMessage());
        } finally {
            $this->loadingStates['all'] = false;
        }
    }

    public function generateHypothesis(string $type): void
    {
        if (! in_array($type, ['desirability', 'viability', 'feasibility'])) {
            $this->setError($type, 'Invalid hypothesis type');

            return;
        }

        $this->loadingStates[$type] = true;
        $this->clearError($type);
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateCriticalHypotheses::dispatch($project, $type, false);

            $this->showSuccess(ucfirst($type).' hypothesis generation started!');

            Log::info('Dispatched critical hypothesis generation job', [
                'project_id' => $project->id,
                'hypothesis_type' => $type,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to dispatch critical hypothesis generation job', [
                'project_id' => $this->project?->id,
                'hypothesis_type' => $type,
                'error' => $e->getMessage(),
            ]);
            $this->setError($type, 'Failed to start generation: '.$e->getMessage());
        } finally {
            $this->loadingStates[$type] = false;
        }
    }

    public function regenerateHypothesis(string $type): void
    {
        if (! in_array($type, ['desirability', 'viability', 'feasibility'])) {
            $this->setError($type, 'Invalid hypothesis type');

            return;
        }

        $this->loadingStates[$type] = true;
        $this->clearError($type);
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateCriticalHypotheses::dispatch($project, $type, true);

            $this->showSuccess(ucfirst($type).' hypothesis regeneration started!');

            Log::info('Dispatched critical hypothesis regeneration job', [
                'project_id' => $project->id,
                'hypothesis_type' => $type,
            ]);
        } catch (Exception $e) {
            Log::error('Failed to dispatch critical hypothesis regeneration job', [
                'project_id' => $this->project?->id,
                'hypothesis_type' => $type,
                'error' => $e->getMessage(),
            ]);
            $this->setError($type, 'Failed to start regeneration: '.$e->getMessage());
        } finally {
            $this->loadingStates[$type] = false;
        }
    }

    public function hasHypothesis(string $type): bool
    {
        return isset($this->hypotheses[$type]) && $this->hypotheses[$type] instanceof GeneratedContent;
    }

    public function hasAllHypotheses(): bool
    {
        return $this->hasHypothesis('desirability') &&
               $this->hasHypothesis('viability') &&
               $this->hasHypothesis('feasibility');
    }

    public function hasAnyHypotheses(): bool
    {
        return $this->hasHypothesis('desirability') ||
               $this->hasHypothesis('viability') ||
               $this->hasHypothesis('feasibility');
    }

    public function getHypothesesList(): array
    {
        $list = [];

        foreach (['desirability', 'viability', 'feasibility'] as $type) {
            if ($this->hasHypothesis($type)) {
                $data = $this->getHypothesisData($type);
                $list[] = [
                    'type' => $type,
                    'text' => $data['hypothesis'] ?? 'No hypothesis available',
                    'criticality' => $data['criticality'] ?? 'Unknown',
                    'testing_method' => $data['testing_method'] ?? 'Not specified',
                ];
            }
        }

        return $list;
    }

    public function getHypothesisData(string $type): ?array
    {
        if (! $this->hasHypothesis($type)) {
            return null;
        }

        return $this->hypotheses[$type]->content_data;
    }

    public function getHypothesisText(string $type): string
    {
        $data = $this->getHypothesisData($type);

        return $data['hypothesis'] ?? 'No hypothesis available';
    }

    public function getHypothesisCriticality(string $type): string
    {
        $data = $this->getHypothesisData($type);

        return $data['criticality'] ?? 'Unknown';
    }

    public function getHypothesisTestingMethod(string $type): string
    {
        $data = $this->getHypothesisData($type);

        return $data['testing_method'] ?? 'Not specified';
    }

    public function getHypothesisSuccessCriteria(string $type): string
    {
        $data = $this->getHypothesisData($type);

        return $data['success_criteria'] ?? 'Not specified';
    }

    public function getHypothesisRiskLevel(string $type): string
    {
        $data = $this->getHypothesisData($type);

        return $data['risk_level'] ?? 'Unknown';
    }

    public function getHypothesisTimestamp(string $type): ?string
    {
        if (! $this->hasHypothesis($type)) {
            return null;
        }

        return $this->hypotheses[$type]->created_at?->diffForHumans();
    }

    public function getCriticalityColor(string $criticality): string
    {
        return match (strtolower($criticality)) {
            'high' => 'text-red-600 dark:text-red-400',
            'medium' => 'text-yellow-600 dark:text-yellow-400',
            'low' => 'text-green-600 dark:text-green-400',
            default => 'text-gray-600 dark:text-gray-400',
        };
    }

    public function getRiskLevelColor(string $riskLevel): string
    {
        return match (strtolower($riskLevel)) {
            'high' => 'text-red-600 dark:text-red-400',
            'medium' => 'text-yellow-600 dark:text-yellow-400',
            'low' => 'text-green-600 dark:text-green-400',
            default => 'text-gray-600 dark:text-gray-400',
        };
    }

    public function getHypothesisTypeIcon(string $type): string
    {
        return match ($type) {
            'desirability' => '❤️',
            'viability' => '💰',
            'feasibility' => '⚙️',
            default => '📋',
        };
    }

    public function getHypothesisTypeDescription(string $type): string
    {
        return match ($type) {
            'desirability' => 'Do customers want this?',
            'viability' => 'Can we build a sustainable business?',
            'feasibility' => 'Can we actually build this?',
            default => 'Unknown hypothesis type',
        };
    }

    private function setError(string $key, string $message): void
    {
        $this->errorMessages[$key] = $message;
    }

    private function clearError(string $key): void
    {
        unset($this->errorMessages[$key]);
    }

    private function clearErrors(): void
    {
        $this->errorMessages = [];
    }

    private function showSuccess(string $message): void
    {
        $this->successMessage = $message;
        $this->showSuccessMessage = true;

        // Auto-hide after 5 seconds
        $this->dispatch('hide-success-message');
    }

    public function hideSuccessMessage(): void
    {
        $this->showSuccessMessage = false;
        $this->successMessage = '';
    }

    /**
     * Poll for updates to check if content generation is complete
     */
    public function pollForUpdates()
    {
        $this->loadExistingHypotheses();

        // Check if all content has been generated
        if ($this->hasAllHypotheses()) {
            $this->dispatch('stop-polling');
        }
    }

    public function render()
    {
        return view('livewire.critical-hypotheses-display');
    }
}
