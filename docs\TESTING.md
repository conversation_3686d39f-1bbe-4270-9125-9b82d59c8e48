# Testing Guide

This document provides comprehensive testing strategies, guidelines, and best practices for the Venture Discovery Platform.

## 🧪 Testing Philosophy

### Testing Pyramid
The Venture Discovery Platform follows the testing pyramid approach:

```
                    /\
                   /  \
                  /    \
                 / E2E  \     ← Few, High-Value
                /________\
               /          \
              / Integration \   ← Some, Critical Paths
             /______________\
            /                \
           /   Unit Tests     \  ← Many, Fast & Focused
          /____________________\
```

### Testing Principles
- **Fast Feedback**: Unit tests provide immediate feedback
- **Confidence**: Integration tests ensure components work together
- **User Focus**: Feature tests validate user workflows
- **Maintainable**: Tests should be easy to understand and modify

## 🏗️ Test Structure

### Directory Organization
```
tests/
├── Feature/                    # End-to-end feature tests
│   ├── Admin/                 # Admin panel functionality
│   │   ├── AccountManagementTest.php
│   │   └── UserManagementTest.php
│   └── App/                   # Main application features
│       ├── AuthenticationTest.php
│       ├── ProjectManagementTest.php
│       ├── ContentGenerationTest.php
│       └── LivewireComponentTest.php
├── Unit/                      # Unit tests for individual components
│   ├── Models/
│   ├── Services/
│   ├── Jobs/
│   └── Events/
├── Datasets/                  # Pest datasets for parameterized tests
└── Helpers/                   # Test helper functions and traits
```

## 🔧 Testing Framework Setup

### Pest PHP Configuration
The platform uses **Pest PHP** as the primary testing framework for its expressive syntax and Laravel integration.

**Key Features:**
- Expressive test syntax
- Built-in Laravel integration
- Parallel test execution
- Snapshot testing
- Architectural testing

### Test Environment Configuration

**Environment Variables:**
```env
# Testing Database
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Queue Configuration
QUEUE_CONNECTION=sync

# Mail Configuration
MAIL_MAILER=array

# OpenAI Testing
OPENAI_API_KEY=test_key
OPENAI_MODEL=gpt-3.5-turbo

# Broadcasting
BROADCAST_DRIVER=log
```

## 🧪 Unit Testing

### Model Testing

**Testing Eloquent Models:**
```php
<?php

use App\Models\Project;
use App\Models\Account;

it('creates a project with required attributes', function () {
    $account = Account::factory()->create();
    
    $project = Project::factory()->create([
        'account_id' => $account->id,
        'title' => 'Test Startup',
        'description' => 'A test startup idea',
    ]);
    
    expect($project)
        ->title->toBe('Test Startup')
        ->description->toBe('A test startup idea')
        ->account_id->toBe($account->id)
        ->status->toBe('pending');
});

it('has many generated content items', function () {
    $project = Project::factory()
        ->hasGeneratedContent(3)
        ->create();
    
    expect($project->generatedContent)->toHaveCount(3);
});

it('calculates completion percentage correctly', function () {
    $project = Project::factory()->create();
    
    // Create some generated content
    $project->generatedContent()->createMany([
        ['content_type' => 'lean_canvas_problem', 'content' => 'Test'],
        ['content_type' => 'lean_canvas_solution', 'content' => 'Test'],
    ]);
    
    expect($project->getCompletionPercentage())->toBeGreaterThan(0);
});
```

### Service Testing

**Testing Business Logic Services:**
```php
<?php

use App\Services\ContentGenerationService;
use App\Services\OpenAiService;
use App\Models\Project;

beforeEach(function () {
    $this->openAiService = Mockery::mock(OpenAiService::class);
    $this->contentService = new ContentGenerationService($this->openAiService);
});

it('generates lean canvas section successfully', function () {
    $project = Project::factory()->create();
    
    $this->openAiService
        ->shouldReceive('generateContent')
        ->once()
        ->andReturn([
            'content' => 'Generated problem statement',
            'tokens_used' => 150
        ]);
    
    $result = $this->contentService->generateLeanCanvasSection(
        $project, 
        'problem'
    );
    
    expect($result)
        ->toBeArray()
        ->toHaveKey('content')
        ->toHaveKey('tokens_used');
});

it('handles openai service failures gracefully', function () {
    $project = Project::factory()->create();
    
    $this->openAiService
        ->shouldReceive('generateContent')
        ->once()
        ->andThrow(new Exception('API Error'));
    
    expect(fn() => $this->contentService->generateLeanCanvasSection($project, 'problem'))
        ->toThrow(Exception::class, 'API Error');
});
```

### Job Testing

**Testing Background Jobs:**
```php
<?php

use App\Jobs\GenerateLeanCanvasSection;
use App\Models\Project;
use App\Services\ContentGenerationService;

it('processes lean canvas generation job', function () {
    $project = Project::factory()->create();
    
    $contentService = Mockery::mock(ContentGenerationService::class);
    $contentService
        ->shouldReceive('generateLeanCanvasSection')
        ->once()
        ->with($project, 'problem')
        ->andReturn(['content' => 'Generated content']);
    
    $this->app->instance(ContentGenerationService::class, $contentService);
    
    $job = new GenerateLeanCanvasSection($project, 'problem');
    $job->handle();
    
    expect($project->fresh()->generatedContent)
        ->toHaveCount(1);
});

it('retries job on failure', function () {
    $project = Project::factory()->create();
    
    $job = new GenerateLeanCanvasSection($project, 'problem');
    
    expect($job->tries)->toBe(3);
    expect($job->backoff())->toBe([1, 5, 10]);
});
```

## 🔗 Integration Testing

### Database Integration

**Testing Model Relationships:**
```php
<?php

use App\Models\Account;
use App\Models\Project;
use App\Models\GeneratedContent;

it('maintains referential integrity', function () {
    $account = Account::factory()->create();
    $project = Project::factory()->create(['account_id' => $account->id]);
    
    GeneratedContent::factory()->create([
        'project_id' => $project->id,
        'content_type' => 'lean_canvas_problem'
    ]);
    
    // Test cascade deletion
    $account->delete();
    
    expect(Project::find($project->id))->toBeNull();
    expect(GeneratedContent::where('project_id', $project->id)->count())->toBe(0);
});
```

### External Service Integration

**Testing OpenAI Service Integration:**
```php
<?php

use App\Services\OpenAiService;

it('communicates with openai api successfully', function () {
    if (!config('services.openai.api_key')) {
        $this->markTestSkipped('OpenAI API key not configured');
    }
    
    $service = app(OpenAiService::class);
    
    $response = $service->generateContent(
        'Generate a simple business problem statement for a food delivery app',
        'lean_canvas_problem'
    );
    
    expect($response)
        ->toBeArray()
        ->toHaveKey('content')
        ->toHaveKey('tokens_used');
        
    expect($response['content'])->toBeString()->not->toBeEmpty();
    expect($response['tokens_used'])->toBeInt()->toBeGreaterThan(0);
})->group('integration', 'external');

it('handles rate limiting gracefully', function () {
    $service = app(OpenAiService::class);
    
    // Mock rate limit response
    Http::fake([
        'api.openai.com/*' => Http::response(
            ['error' => ['message' => 'Rate limit exceeded']], 
            429
        )
    ]);
    
    expect(fn() => $service->generateContent('test prompt', 'test_type'))
        ->toThrow(Exception::class);
})->group('integration');
```

## 🎭 Feature Testing

### Authentication Testing

**Testing Multi-Guard Authentication:**
```php
<?php

use App\Models\User;
use App\Models\Account;

describe('Admin Authentication', function () {
    it('allows admin users to access admin panel', function () {
        $user = User::factory()->create();
        
        $this->actingAs($user, 'web')
            ->get('/admin')
            ->assertOk();
    });
    
    it('redirects unauthenticated users to login', function () {
        $this->get('/admin')
            ->assertRedirect('/admin/login');
    });
});

describe('App Authentication', function () {
    it('allows app users to access app panel', function () {
        $account = Account::factory()->create();
        
        $this->actingAs($account, 'account')
            ->get('/app')
            ->assertOk();
    });
    
    it('blocks inactive accounts', function () {
        $account = Account::factory()->create(['status' => 'blocked']);
        
        $this->actingAs($account, 'account')
            ->get('/app')
            ->assertForbidden();
    });
});
```

### Project Management Testing

**Testing Project CRUD Operations:**
```php
<?php

use App\Models\Account;
use App\Models\Project;

beforeEach(function () {
    $this->account = Account::factory()->create();
    $this->actingAs($this->account, 'account');
});

it('creates a new project', function () {
    $projectData = [
        'title' => 'New Startup Idea',
        'description' => 'A revolutionary app concept',
        'target_market' => 'Young professionals',
    ];
    
    $this->post('/app/projects', $projectData)
        ->assertRedirect();
    
    $this->assertDatabaseHas('projects', [
        'title' => 'New Startup Idea',
        'account_id' => $this->account->id,
    ]);
});

it('prevents creating projects with invalid data', function () {
    $this->post('/app/projects', [])
        ->assertSessionHasErrors(['title', 'description']);
});

it('shows only user projects', function () {
    $userProject = Project::factory()->create(['account_id' => $this->account->id]);
    $otherProject = Project::factory()->create();
    
    $response = $this->get('/app/projects');
    
    $response->assertSee($userProject->title);
    $response->assertDontSee($otherProject->title);
});
```

### Content Generation Testing

**Testing AI Content Generation Flow:**
```php
<?php

use App\Models\Project;
use App\Jobs\GenerateLeanCanvasSection;
use Illuminate\Support\Facades\Queue;

beforeEach(function () {
    $this->account = Account::factory()->create();
    $this->actingAs($this->account, 'account');
    Queue::fake();
});

it('dispatches content generation jobs', function () {
    $project = Project::factory()->create(['account_id' => $this->account->id]);
    
    $this->post("/app/projects/{$project->id}/generate/lean-canvas")
        ->assertOk();
    
    Queue::assertPushed(GenerateLeanCanvasSection::class, 10); // 10 sections
});

it('prevents unauthorized content generation', function () {
    $project = Project::factory()->create(); // Different account
    
    $this->post("/app/projects/{$project->id}/generate/lean-canvas")
        ->assertForbidden();
});

it('shows generation progress', function () {
    $project = Project::factory()->create(['account_id' => $this->account->id]);
    
    // Simulate partial completion
    $project->generatedContent()->create([
        'content_type' => 'lean_canvas_problem',
        'content' => 'Test problem',
    ]);
    
    $response = $this->get("/app/projects/{$project->id}");
    
    $response->assertSee('10%'); // 1 of 10 sections complete
});
```

## 🎨 Frontend Testing

### Livewire Component Testing

**Testing Interactive Components:**
```php
<?php

use App\Livewire\LeanCanvasDisplay;
use App\Models\Project;
use Livewire\Livewire;

it('displays lean canvas sections', function () {
    $project = Project::factory()->create();
    
    $project->generatedContent()->create([
        'content_type' => 'lean_canvas_problem',
        'content' => 'Customer acquisition challenges',
    ]);
    
    Livewire::test(LeanCanvasDisplay::class, ['project' => $project])
        ->assertSee('Customer acquisition challenges')
        ->assertSee('Problem'); // Section title
});

it('regenerates content section', function () {
    $project = Project::factory()->create();
    
    Queue::fake();
    
    Livewire::test(LeanCanvasDisplay::class, ['project' => $project])
        ->call('regenerateSection', 'problem')
        ->assertDispatched('content-generation-started');
    
    Queue::assertPushed(GenerateLeanCanvasSection::class);
});

it('updates in real-time when content is generated', function () {
    $project = Project::factory()->create();
    
    $component = Livewire::test(LeanCanvasDisplay::class, ['project' => $project]);
    
    // Simulate content generation completion
    $project->generatedContent()->create([
        'content_type' => 'lean_canvas_problem',
        'content' => 'New problem statement',
    ]);
    
    $component->dispatch('content-updated', $project->id)
        ->assertSee('New problem statement');
});
```

### Filament Resource Testing

**Testing Admin Panel Resources:**
```php
<?php

use App\Filament\Resources\AccountResource;
use App\Models\User;
use App\Models\Account;

beforeEach(function () {
    $this->admin = User::factory()->create();
    $this->actingAs($this->admin, 'web');
});

it('lists accounts in admin panel', function () {
    $accounts = Account::factory()->count(3)->create();
    
    $this->get(AccountResource::getUrl('index'))
        ->assertOk()
        ->assertSee($accounts[0]->name)
        ->assertSee($accounts[1]->name)
        ->assertSee($accounts[2]->name);
});

it('creates new account through admin panel', function () {
    $accountData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'status' => 'active',
    ];
    
    $this->post(AccountResource::getUrl('store'), $accountData)
        ->assertRedirect();
    
    $this->assertDatabaseHas('accounts', [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
    ]);
});
```

## 📊 Performance Testing

### Database Performance

**Testing Query Performance:**
```php
<?php

use Illuminate\Support\Facades\DB;

it('loads projects with minimal queries', function () {
    $account = Account::factory()
        ->hasProjects(10)
        ->create();
    
    DB::enableQueryLog();
    
    $this->actingAs($account, 'account')
        ->get('/app/projects');
    
    $queries = DB::getQueryLog();
    
    // Should not exceed reasonable query count
    expect(count($queries))->toBeLessThan(5);
});

it('eager loads relationships efficiently', function () {
    $project = Project::factory()
        ->hasGeneratedContent(10)
        ->create();
    
    DB::enableQueryLog();
    
    $loadedProject = Project::with('generatedContent')->find($project->id);
    $content = $loadedProject->generatedContent->toArray();
    
    $queries = DB::getQueryLog();
    
    // Should use only 2 queries (project + content)
    expect(count($queries))->toBe(2);
});
```

### API Performance

**Testing Response Times:**
```php
<?php

it('responds to project listing within acceptable time', function () {
    $account = Account::factory()
        ->hasProjects(50)
        ->create();
    
    $this->actingAs($account, 'account');
    
    $start = microtime(true);
    $this->get('/app/projects');
    $duration = microtime(true) - $start;
    
    expect($duration)->toBeLessThan(1.0); // Less than 1 second
});
```

## 🔒 Security Testing

### Authentication Security

**Testing Security Measures:**
```php
<?php

it('prevents sql injection in login', function () {
    $maliciousInput = "admin'; DROP TABLE accounts; --";
    
    $this->post('/app/login', [
        'email' => $maliciousInput,
        'password' => 'password',
    ])->assertSessionHasErrors();
    
    // Verify table still exists
    expect(Schema::hasTable('accounts'))->toBeTrue();
});

it('prevents xss in project descriptions', function () {
    $account = Account::factory()->create();
    $this->actingAs($account, 'account');
    
    $xssPayload = '<script>alert("XSS")</script>';
    
    $this->post('/app/projects', [
        'title' => 'Test Project',
        'description' => $xssPayload,
        'target_market' => 'Test Market',
    ]);
    
    $response = $this->get('/app/projects');
    
    // Should be escaped
    $response->assertDontSee('<script>');
    $response->assertSee('&lt;script&gt;');
});
```

### Authorization Testing

**Testing Access Control:**
```php
<?php

it('prevents cross-account data access', function () {
    $account1 = Account::factory()->create();
    $account2 = Account::factory()->create();
    
    $project = Project::factory()->create(['account_id' => $account1->id]);
    
    $this->actingAs($account2, 'account')
        ->get("/app/projects/{$project->id}")
        ->assertForbidden();
});
```

## 🚀 Continuous Integration Testing

### GitHub Actions Configuration

**Test Workflow:**
```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        options: --health-cmd="mysqladmin ping" --health-interval=10s
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          
      - name: Install dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader
        
      - name: Copy environment file
        run: cp .env.example .env.testing
        
      - name: Generate application key
        run: php artisan key:generate --env=testing
        
      - name: Run migrations
        run: php artisan migrate --env=testing
        
      - name: Run tests
        run: php artisan test --parallel
```

### Test Coverage

**Coverage Configuration:**
```xml
<!-- phpunit.xml -->
<coverage>
    <include>
        <directory suffix=".php">./app</directory>
    </include>
    <exclude>
        <directory>./app/Console</directory>
        <file>./app/Http/Kernel.php</file>
    </exclude>
    <report>
        <html outputDirectory="coverage-html"/>
        <text outputFile="coverage.txt"/>
    </report>
</coverage>
```

## 📋 Testing Checklist

### Pre-Deployment Testing

**Manual Testing Checklist:**
- [ ] User registration and login flows
- [ ] Project creation and management
- [ ] Content generation for all types
- [ ] Real-time updates and notifications
- [ ] Admin panel functionality
- [ ] Error handling and edge cases
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

### Automated Testing Coverage

**Required Test Coverage:**
- [ ] All models have unit tests
- [ ] All services have comprehensive tests
- [ ] All jobs are tested with mocks
- [ ] All Livewire components are tested
- [ ] All API endpoints are tested
- [ ] Authentication flows are tested
- [ ] Authorization rules are tested
- [ ] Database relationships are tested

## 🔧 Testing Tools and Utilities

### Custom Test Helpers

**Project Factory Enhancements:**
```php
<?php

// tests/Helpers/ProjectTestHelper.php
class ProjectTestHelper
{
    public static function createCompleteProject(Account $account): Project
    {
        $project = Project::factory()->create(['account_id' => $account->id]);
        
        // Generate all lean canvas sections
        $sections = [
            'problem', 'solution', 'key_metrics', 'unique_value_proposition',
            'unfair_advantage', 'channels', 'customer_segments', 'cost_structure',
            'revenue_streams', 'key_partnerships'
        ];
        
        foreach ($sections as $section) {
            $project->generatedContent()->create([
                'content_type' => "lean_canvas_{$section}",
                'content' => "Test {$section} content",
            ]);
        }
        
        return $project;
    }
}
```

### Test Data Management

**Seeder for Testing:**
```php
<?php

// database/seeders/TestDataSeeder.php
class TestDataSeeder extends Seeder
{
    public function run(): void
    {
        if (app()->environment('testing')) {
            Account::factory()
                ->count(5)
                ->hasProjects(3)
                ->create();
        }
    }
}
```

## 📈 Test Metrics and Monitoring

### Key Testing Metrics

**Track These Metrics:**
- Test execution time
- Test coverage percentage
- Number of failing tests
- Test reliability (flaky test detection)
- Performance test results

### Continuous Monitoring

**Automated Alerts:**
- Test failure notifications
- Coverage drop alerts
- Performance regression detection
- Security vulnerability scanning

This comprehensive testing strategy ensures the Venture Discovery Platform maintains high quality, security, and performance standards throughout its development lifecycle. 