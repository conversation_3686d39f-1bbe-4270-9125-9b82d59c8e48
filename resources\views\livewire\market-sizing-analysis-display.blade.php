<div class="space-y-6">
    {{-- Success Message --}}
    @if($showSuccessMessage)
        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center justify-between">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 dark:text-green-200 font-medium">{{ $successMessage }}</span>
            </div>
            <button wire:click="hideSuccessMessage" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    @endif

    {{-- Error Messages --}}
    @if(!empty($errorMessages))
        @foreach($errorMessages as $errorMessage)
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-red-800 dark:text-red-200">{{ $errorMessage }}</span>
                </div>
            </div>
        @endforeach
    @endif

    {{-- Main Content --}}
    @if($this->hasAnyAnalyses())
        {{-- Status Badge --}}
        <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
                Generated
            </span>
        </div>

        {{-- Market Sizing Analysis Cards --}}
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            @php
                $analysesList = $this->getAnalysesList();
            @endphp
            
            @foreach($analysesList as $analysis)
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-4">
                    {{-- Header --}}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    @if($analysis['type'] === 'tam')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    @elseif($analysis['type'] === 'sam')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                    @else
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    @endif
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ strtoupper($analysis['type']) }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $analysis['title'] }}</p>
                            </div>
                        </div>
                        
                        {{-- Edit Button --}}
                        <button 
                            wire:click="regenerateAnalysis('{{ $analysis['type'] }}')" 
                            wire:loading.attr="disabled"
                            wire:target="regenerateAnalysis('{{ $analysis['type'] }}')"
                            class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 font-medium"
                        >
                            <span wire:loading.remove wire:target="regenerateAnalysis('{{ $analysis['type'] }}')">Edit</span>
                            <span wire:loading wire:target="regenerateAnalysis('{{ $analysis['type'] }}')">...</span>
                        </button>
                    </div>

                    {{-- Market Size Value --}}
                    <div class="text-center py-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div class="text-3xl font-bold text-gray-900 dark:text-white">
                            {{ $this->formatMarketSize($analysis['market_size_value'], $analysis['market_size_currency']) }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            Market Size
                        </div>
                    </div>

                    {{-- Key Details --}}
                    <div class="space-y-3">
                        {{-- Confidence Level --}}
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Confidence:</span>
                            <span class="text-sm font-medium {{ $this->getConfidenceLevelColor($analysis['confidence_level']) }}">
                                {{ $analysis['confidence_level'] }}
                            </span>
                        </div>

                        {{-- Growth Rate --}}
                        @if($analysis['growth_rate'] !== 'N/A')
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Growth Rate:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $analysis['growth_rate'] }}
                                </span>
                            </div>
                        @endif

                        {{-- Time Frame --}}
                        @if($analysis['time_frame'] !== 'N/A')
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Time Frame:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $analysis['time_frame'] }}
                                </span>
                            </div>
                        @endif
                    </div>

                    {{-- Market Description --}}
                    @if($analysis['market_description'] !== 'N/A')
                        <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
                            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                                {{ Str::limit($analysis['market_description'], 120) }}
                            </p>
                        </div>
                    @endif

                    {{-- Key Insights --}}
                    @if(!empty($analysis['key_insights']) && is_array($analysis['key_insights']))
                        <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Insights:</h4>
                            <ul class="space-y-1">
                                @foreach(array_slice($analysis['key_insights'], 0, 3) as $insight)
                                    <li class="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                                        <span class="text-blue-500 mr-2">•</span>
                                        <span>{{ Str::limit($insight, 80) }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        {{-- Detailed Analysis Section --}}
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Analysis Overview</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($analysesList as $analysis)
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ $analysis['title'] }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $analysis['description'] }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        {{-- Empty State --}}
        <div class="text-center py-12">
            <div class="text-gray-400 dark:text-gray-500 mb-4">
                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No market sizing analysis yet</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Generate market sizing analysis to understand your Total Addressable Market (TAM), Serviceable Addressable Market (SAM), and Serviceable Obtainable Market (SOM).
            </p>
            
            {{-- Manual Generate Button in Empty State --}}
            <button 
                wire:click="generateAllAnalyses"
                wire:loading.attr="disabled"
                wire:target="generateAllAnalyses"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                <span wire:loading.remove wire:target="generateAllAnalyses">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Generate All Analyses
                </span>
                <span wire:loading wire:target="generateAllAnalyses" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                </span>
            </button>
        </div>
    @endif

    {{-- Loading State --}}
    @if($loadingStates['all'] || array_filter($loadingStates))
        <div class="text-center py-8">
            <svg class="animate-spin mx-auto h-8 w-8 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Generating market sizing analysis...</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This may take a few moments while we analyze your market opportunity.</p>
        </div>
    @endif

    <!-- Polling Script -->
    <script>
        document.addEventListener('livewire:init', () => {
            let pollingInterval;
            
            // Start polling when component loads
            function startPolling() {
                pollingInterval = setInterval(() => {
                    @this.pollForUpdates();
                }, 5000); // Poll every 5 seconds
            }
            
            // Stop polling when all content is generated
            Livewire.on('stop-polling', () => {
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = null;
                }
            });
            
            // Start polling immediately if there's any loading state or missing content
            @if(collect($loadingStates)->contains(true) || !$this->hasAllAnalyses())
                startPolling();
            @endif
        });
    </script>
</div> 