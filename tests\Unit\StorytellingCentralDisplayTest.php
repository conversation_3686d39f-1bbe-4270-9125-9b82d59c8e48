<?php

namespace Tests\Unit;

use App\Livewire\StorytellingCentralDisplay;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Mockery;
use Tests\TestCase;

class StorytellingCentralDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected Project $project;

    protected ContentGenerationService $mockContentService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create project
        $this->project = Project::factory()->create([
            'input_prompt' => 'EcoTrack - A mobile app that helps individuals and families track their carbon footprint',
        ]);

        $this->mockContentService = Mockery::mock(ContentGenerationService::class);
        $this->app->instance(ContentGenerationService::class, $this->mockContentService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_component_mounts_with_project(): void
    {
        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->assertSet('project', $this->project);
        $component->assertSet('brandWheel', null);
        $component->assertSet('startupNaming', null);
        $component->assertSet('elevatorPitch', null);
    }

    public function test_component_loads_existing_content(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
            'content_data' => [
                'mission' => 'To help people reduce their environmental impact',
                'vision' => 'A world where everyone tracks and reduces their carbon footprint',
                'values' => ['Sustainability', 'Transparency', 'Innovation'],
                'personality' => 'Friendly, knowledgeable, and encouraging',
                'tone_of_voice' => 'Professional yet approachable',
                'brand_promise' => 'Making environmental responsibility accessible and actionable',
            ],
        ]);

        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
            'content_data' => [
                'suggestions' => [
                    [
                        'name' => 'EcoTrack',
                        'rationale' => 'Clearly describes the app\'s purpose',
                        'domain_availability' => 'available',
                        'trademark_considerations' => 'medium risk',
                    ],
                    [
                        'name' => 'CarbonCompass',
                        'rationale' => 'Suggests guidance in navigation',
                        'domain_availability' => 'available',
                        'trademark_considerations' => 'low risk',
                    ],
                ],
            ],
        ]);

        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
            'content_data' => [
                'pitch_30_seconds' => 'EcoTrack helps families reduce their carbon footprint through daily tracking.',
                'pitch_60_seconds' => 'EcoTrack is a mobile app that helps individuals and families track their carbon footprint by monitoring daily activities like transportation, energy usage, and consumption habits.',
                'pitch_90_seconds' => 'EcoTrack is a comprehensive mobile app that empowers individuals and families to track, understand, and reduce their carbon footprint through intelligent monitoring of daily activities.',
                'key_points' => ['Easy tracking', 'Personalized recommendations', 'Local business connections'],
                'call_to_action' => 'Join the movement towards sustainable living',
            ],
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        // Check that content is loaded by ID rather than object equality
        $this->assertEquals($brandWheel->id, $component->get('brandWheel')->id);
        $this->assertEquals($startupNaming->id, $component->get('startupNaming')->id);
        $this->assertEquals($elevatorPitch->id, $component->get('elevatorPitch')->id);
    }

    public function test_generate_all_content_success(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);
        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
        ]);
        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
        ]);

        $this->mockContentService
            ->shouldReceive('generateAllStorytellingContent')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn([
                'brand_wheel' => $brandWheel,
                'startup_naming' => $startupNaming,
                'elevator_pitch' => $elevatorPitch,
            ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('generateAllContent');

        $this->assertEquals($brandWheel->id, $component->get('brandWheel')->id);
        $this->assertEquals($startupNaming->id, $component->get('startupNaming')->id);
        $this->assertEquals($elevatorPitch->id, $component->get('elevatorPitch')->id);
        $component->assertSet('error', null);
    }

    public function test_generate_brand_wheel_success(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);

        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn($brandWheel);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('generateBrandWheel');

        $this->assertEquals($brandWheel->id, $component->get('brandWheel')->id);
        $component->assertSet('isLoadingBrandWheel', false);
        $component->assertSet('error', null);
    }

    public function test_generate_startup_naming_success(): void
    {
        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
        ]);

        $this->mockContentService
            ->shouldReceive('generateStartupNaming')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn($startupNaming);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('generateStartupNaming');

        $this->assertEquals($startupNaming->id, $component->get('startupNaming')->id);
        $component->assertSet('isLoadingStartupNaming', false);
        $component->assertSet('error', null);
    }

    public function test_generate_elevator_pitch_success(): void
    {
        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
        ]);

        $this->mockContentService
            ->shouldReceive('generateElevatorPitch')
            ->once()
            ->with(Mockery::type(Project::class))
            ->andReturn($elevatorPitch);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('generateElevatorPitch');

        $this->assertEquals($elevatorPitch->id, $component->get('elevatorPitch')->id);
        $component->assertSet('isLoadingElevatorPitch', false);
        $component->assertSet('error', null);
    }

    public function test_regenerate_brand_wheel_success(): void
    {
        $newBrandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
        ]);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with(Mockery::type(Project::class), 'brand_wheel')
            ->andReturn($newBrandWheel);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('regenerateBrandWheel');

        $this->assertEquals($newBrandWheel->id, $component->get('brandWheel')->id);
        $component->assertSet('isRegeneratingBrandWheel', false);
        $component->assertSet('error', null);
    }

    public function test_regenerate_startup_naming_success(): void
    {
        $newStartupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
        ]);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with(Mockery::type(Project::class), 'startup_naming')
            ->andReturn($newStartupNaming);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('regenerateStartupNaming');

        $this->assertEquals($newStartupNaming->id, $component->get('startupNaming')->id);
        $component->assertSet('isRegeneratingStartupNaming', false);
        $component->assertSet('error', null);
    }

    public function test_regenerate_elevator_pitch_success(): void
    {
        $newElevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
        ]);

        $this->mockContentService
            ->shouldReceive('regenerateStorytellingContent')
            ->once()
            ->with(Mockery::type(Project::class), 'elevator_pitch')
            ->andReturn($newElevatorPitch);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('regenerateElevatorPitch');

        $this->assertEquals($newElevatorPitch->id, $component->get('elevatorPitch')->id);
        $component->assertSet('isRegeneratingElevatorPitch', false);
        $component->assertSet('error', null);
    }

    public function test_toggle_full_elevator_pitch(): void
    {
        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->assertSet('showFullElevatorPitch', false);

        $component->call('toggleFullElevatorPitch');

        $component->assertSet('showFullElevatorPitch', true);

        $component->call('toggleFullElevatorPitch');

        $component->assertSet('showFullElevatorPitch', false);
    }

    public function test_has_brand_wheel_returns_correct_boolean(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
            'content_data' => ['mission' => 'Test mission'],
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasBrandWheel());
    }

    public function test_has_startup_naming_returns_correct_boolean(): void
    {
        $startupNaming = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'startup_naming',
            'content_data' => ['suggestions' => [['name' => 'TestName']]],
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasStartupNaming());
    }

    public function test_has_elevator_pitch_returns_correct_boolean(): void
    {
        $elevatorPitch = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'elevator_pitch',
            'content_data' => ['pitch_30_seconds' => 'Test pitch'],
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasElevatorPitch());
    }

    public function test_has_any_content_returns_correct_boolean(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
            'content_data' => ['mission' => 'Test mission'],
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $this->assertTrue($component->instance()->hasAnyContent());
    }

    public function test_get_generated_at_returns_formatted_date(): void
    {
        $brandWheel = GeneratedContent::factory()->create([
            'project_id' => $this->project->id,
            'content_type' => 'brand_wheel',
            'content_data' => [
                'mission' => 'Test mission',
            ],
            'created_at' => '2024-01-15 10:30:00',
        ]);

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $generatedAt = $component->instance()->getGeneratedAt('brand_wheel');
        $this->assertStringContainsString('Jan', $generatedAt);
        $this->assertStringContainsString('2024', $generatedAt);
    }

    public function test_error_handling_in_generation(): void
    {
        $this->mockContentService
            ->shouldReceive('generateBrandWheel')
            ->once()
            ->andThrow(new \Exception('Generation failed'));

        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->call('generateBrandWheel');

        $component->assertSet('isLoadingBrandWheel', false);
        $component->assertSet('error', 'Failed to generate brand wheel. Please try again.');
    }

    public function test_component_renders_successfully(): void
    {
        $component = Livewire::test(StorytellingCentralDisplay::class, ['project' => $this->project]);

        $component->assertStatus(200);
    }
}
