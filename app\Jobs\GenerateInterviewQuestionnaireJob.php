<?php

namespace App\Jobs;

use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateInterviewQuestionnaireJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Project $project;

    public bool $regenerate;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(Project $project, bool $regenerate = false)
    {
        $this->project = $project;
        $this->regenerate = $regenerate;
    }

    /**
     * Execute the job.
     */
    public function handle(ContentGenerationService $contentService): void
    {
        try {
            Log::info('Starting interview questionnaire generation job', [
                'project_id' => $this->project->id,
                'project_prompt' => substr($this->project->input_prompt, 0, 100),
                'regenerate' => $this->regenerate,
                'attempt' => $this->attempts(),
            ]);

            if ($this->regenerate) {
                $questionnaire = $contentService->regenerateInterviewQuestionnaire($this->project);
                Log::info('Interview questionnaire regenerated successfully', [
                    'project_id' => $this->project->id,
                    'content_id' => $questionnaire->id,
                    'question_count' => $questionnaire->content_data['question_count'] ?? 0,
                ]);
            } else {
                // Check if questionnaire already exists
                $existingQuestionnaire = $contentService->getInterviewQuestionnaire($this->project);

                if ($existingQuestionnaire) {
                    Log::info('Interview questionnaire already exists, skipping generation', [
                        'project_id' => $this->project->id,
                        'content_id' => $existingQuestionnaire->id,
                    ]);

                    return;
                }

                $questionnaire = $contentService->generateInterviewQuestionnaire($this->project);
                Log::info('Interview questionnaire generated successfully', [
                    'project_id' => $this->project->id,
                    'content_id' => $questionnaire->id,
                    'question_count' => $questionnaire->content_data['question_count'] ?? 0,
                ]);
            }

        } catch (Exception $e) {
            Log::error('Failed to generate interview questionnaire in job', [
                'project_id' => $this->project->id,
                'regenerate' => $this->regenerate,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw the exception to trigger retry logic
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Interview questionnaire generation job failed permanently', [
            'project_id' => $this->project->id,
            'regenerate' => $this->regenerate,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // You could send a notification to the user here
        // or update a status field in the project model
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        // Exponential backoff: 30 seconds, 2 minutes, 8 minutes
        return [30, 120, 480];
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        // Retry for up to 1 hour
        return now()->addHour();
    }
}
