<?php

namespace App\Events;

use App\Models\GeneratedContent;
use App\Models\Project;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LeanCanvasSectionGenerated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Project $project,
        public string $sectionKey,
        public ?GeneratedContent $generatedContent = null,
        public bool $success = true,
        public ?string $errorMessage = null
    ) {
        //
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('project.'.$this->project->id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'lean-canvas-section-generated';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $data = [
            'project_id' => $this->project->id,
            'section_key' => $this->sectionKey,
            'success' => $this->success,
            'timestamp' => now()->toISOString(),
        ];

        if ($this->success && $this->generatedContent) {
            $data['content'] = [
                'id' => $this->generatedContent->id,
                'content_data' => $this->generatedContent->content_data,
                'created_at' => $this->generatedContent->created_at->toISOString(),
                'updated_at' => $this->generatedContent->updated_at->toISOString(),
            ];
        }

        if (! $this->success && $this->errorMessage) {
            $data['error'] = $this->errorMessage;
        }

        return $data;
    }

    /**
     * Determine if this event should be broadcast.
     */
    public function shouldBroadcast(): bool
    {
        return true;
    }
}
