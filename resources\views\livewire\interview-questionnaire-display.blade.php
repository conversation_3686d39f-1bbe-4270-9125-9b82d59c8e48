<div>
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        @if($this->hasQuestionnaire())
            <div class="flex items-center space-x-2">
                <!-- Question Count Badge -->
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    {{ $this->getQuestionCount() }} questions
                </span>
                
                <!-- Status Badge -->
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    ✓ Generated
                </span>
            </div>
        @endif
    </div>

    <!-- Error Message -->
    @if($error)
        <div class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-800 dark:text-red-200">{{ $error }}</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Content -->
    @if($this->hasQuestionnaire())
        <!-- Questionnaire Content -->
        <div class="space-y-6">
            <!-- Introduction -->
            @if(isset($questionnaire->content_data['introduction']))
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                    <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Introduction</h4>
                    <p class="text-sm text-blue-800 dark:text-blue-200">{{ $questionnaire->content_data['introduction'] }}</p>
                </div>
            @endif

            <!-- Questions Preview/Full View -->
            @if(isset($questionnaire->content_data['questions']) && is_array($questionnaire->content_data['questions']))
                <div class="space-y-4">
                    @if($showFullQuestionnaire)
                        <!-- Full Questionnaire View -->
                        @foreach($questionnaire->content_data['questions'] as $sectionIndex => $section)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                @if(isset($section['section_title']))
                                    <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
                                        {{ $section['section_title'] }}
                                    </h4>
                                @endif
                                
                                @if(isset($section['questions']) && is_array($section['questions']))
                                    <ol class="space-y-2 list-decimal list-inside">
                                        @foreach($section['questions'] as $question)
                                            <li class="text-sm text-gray-700 dark:text-gray-300">{{ $question }}</li>
                                        @endforeach
                                    </ol>
                                @elseif(is_string($section))
                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $section }}</p>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <!-- Preview Mode -->
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">Question Preview</h4>
                            <div class="space-y-2">
                                @php
                                    $previewCount = 0;
                                    $maxPreview = 3;
                                @endphp
                                @foreach($questionnaire->content_data['questions'] as $section)
                                    @if($previewCount >= $maxPreview) @break @endif
                                    
                                    @if(isset($section['questions']) && is_array($section['questions']))
                                        @foreach($section['questions'] as $question)
                                            @if($previewCount >= $maxPreview) @break @endif
                                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                                {{ $previewCount + 1 }}. {{ $question }}
                                            </p>
                                            @php $previewCount++; @endphp
                                        @endforeach
                                    @elseif(is_string($section))
                                        @if($previewCount < $maxPreview)
                                            <p class="text-sm text-gray-700 dark:text-gray-300">
                                                {{ $previewCount + 1 }}. {{ $section }}
                                            </p>
                                            @php $previewCount++; @endphp
                                        @endif
                                    @endif
                                @endforeach
                                
                                @if($this->getQuestionCount() > $maxPreview)
                                    <p class="text-sm text-gray-500 dark:text-gray-400 italic">
                                        ... and {{ $this->getQuestionCount() - $maxPreview }} more questions
                                    </p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Conclusion -->
            @if(isset($questionnaire->content_data['conclusion']))
                <div class="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Conclusion</h4>
                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $questionnaire->content_data['conclusion'] }}</p>
                </div>
            @endif

            <!-- Metadata -->
            <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-700">
                <span>Generated: {{ $this->getGeneratedAt() }}</span>
                <span>{{ $this->getQuestionCount() }} total questions</span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <!-- Toggle View Button -->
            <button 
                wire:click="toggleFullView"
                class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
                @if($showFullQuestionnaire)
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                    Show Preview
                @else
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    Show All Questions
                @endif
            </button>

            <!-- Download Button -->
            <button 
                wire:click="downloadQuestionnaire"
                class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download
            </button>

            <!-- Regenerate Button -->
            <button 
                wire:click="regenerateQuestionnaire"
                wire:loading.attr="disabled"
                wire:target="regenerateQuestionnaire"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                <div wire:loading.remove wire:target="regenerateQuestionnaire" class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Regenerate
                </div>
                <div wire:loading wire:target="regenerateQuestionnaire" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Regenerating...
                </div>
            </button>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No questionnaire generated</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Generate a customer interview questionnaire to validate your business assumptions.
            </p>
            <div class="mt-6">
                <button 
                    wire:click="generateQuestionnaire"
                    wire:loading.attr="disabled"
                    wire:target="generateQuestionnaire"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <div wire:loading.remove wire:target="generateQuestionnaire" class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Generate Questionnaire
                    </div>
                    <div wire:loading wire:target="generateQuestionnaire" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating...
                    </div>
                </button>
            </div>
        </div>
    @endif
</div>

<!-- Download File JavaScript -->
<script>
    document.addEventListener('livewire:init', () => {
        let pollingInterval;
        
        // Download file functionality
        Livewire.on('download-file', (event) => {
            const { content, filename, mimeType } = event;
            const blob = new Blob([content], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        });
        
        // Polling functionality
        function startPolling() {
            pollingInterval = setInterval(() => {
                @this.pollForUpdates();
            }, 5000); // Poll every 5 seconds
        }
        
        // Stop polling when content is generated
        Livewire.on('stop-polling', () => {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
        });
        
        // Start polling immediately if there's any loading state or missing content
        @if($isLoading || $isRegenerating || !$this->hasQuestionnaire())
            startPolling();
        @endif
    });
</script>
