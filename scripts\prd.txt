<context>
# Overview
This document outlines the Product Requirements (PRD) for "Venture Discovery AI" a web application designed to help entrepreneurs and startup founders rapidly generate foundational business, validation, and storytelling assets based on a single idea prompt. VisionCraft AI leverages OpenAI's generative capabilities to transform a user's initial concept into a structured set of documents and plans, accelerating the early stages of startup development.

The core problem VisionCraft AI solves is the time-consuming and often daunting process of initial business planning, hypothesis generation, and brand narrative creation. Many founders struggle with where to start or how to articulate their vision comprehensively. This tool is for aspiring entrepreneurs, startup founders, product managers, and freelance developers who need to quickly outline and validate their business ideas. Its value lies in providing a structured, AI-powered jumpstart, enabling users to move from idea to a tangible set of initial plans and hypotheses efficiently.

# Core Features

1.  **User Authentication & Management**
    *   **What it does:** Allows users to register for a new account, log in to an existing account, and manage their profile.
    *   **Why it's important:** Secures user data, personalizes the experience, and allows users to save and revisit their generated projects.
    *   **How it works:** Standard email/password registration and login system. User data (profiles, generated projects) will be stored in a database.

2.  **Project Creation via Prompt Input**
    *   **What it does:** Provides a central textarea on the main dashboard where users can input their startup idea or business concept as a prompt.
    *   **Why it's important:** This is the primary input mechanism that drives all content generation.
    *   **How it works:** User types their idea into a form. On submission, this prompt is sent to the backend to initiate the content generation process.

3.  **AI-Powered Content Generation Engine**
    *   **What it does:** Uses the user's input prompt to interact with OpenAI APIs and generate a predefined set of business, validation, and storytelling artifacts.
    *   **Why it's important:** This is the core value proposition, automating the creation of foundational startup documents.
    *   **How it works:**
        *   The backend receives the user's main prompt.
        *   A series of specialized sub-prompts are dynamically constructed or selected, tailored for each required output (e.g., Lean Canvas sections, hypotheses, brand mission).
        *   These sub-prompts are sent to the OpenAI API (e.g., GPT-4, GPT-3.5-turbo).
        *   The responses from OpenAI are parsed, processed, and stored in relation to the user's project.
        *   The system will generate content for three main categories, each with specific deliverables:
            *   **Business Prototype:**
                *   Lean Canvas (all sections: Problem, Solution, Unique Value Proposition, Unfair Advantage, Customer Segments, Existing Alternatives, Key Metrics, Channels, Cost Structure, Revenue Streams, High Level Concept)
            *   **Validation:**
                *   Critical Hypotheses (Desirability, Viability, Feasibility)
                *   Validation Experiments Content:
                    *   Customer Interview Questionnaire
                    *   Landing Page (Key messages, structure, call to action)
            *   **Storytelling Central:**
                *   Brand Wheel (Mission, Vision, Values, Brand Personality)
                *   Startup Naming (Suggestions for a startup name)
                *   Elevator Pitch

4.  **Dashboard & Content Display**
    *   **What it does:** Presents the generated content to the user in an organized and visually appealing manner, structured into the three main categories.
    *   **Why it's important:** Allows users to easily access, review, and utilize the generated information.
    *   **How it works:**
        *   **Main Dashboard:** After generation (or when viewing a project), users see sections for Business Prototype, Validation, and Storytelling Central.
        *   **Business Prototype Section:**
            *   Displays a title "Business Prototype" and a subtitle.
            *   **Lean Canvas:** Presented as a grid of cards, where each card represents a section of the Lean Canvas (e.g., Problem, Solution, Customer Segments). A "Details" button (top right of the Lean Canvas group) will likely navigate to a page showing the full, combined Lean Canvas. (Ref: img1)
        *   **Validation Section:**
            *   Displays a title "Validation" and a subtitle.
            *   **Critical Hypotheses:** Presented as a list, with each hypothesis having a title, criticality level (High, Medium, Low), and a suggested testing method. (Ref: img2)
            *   **Validation Experiments:**
                *   Displays a title "Validation Experiments" and a subtitle.
                *   Presented as cards for "Customer Interview" and "Landing Page". Each card will have a "Details" button. (Ref: img3)
                *   Clicking "Details" on "Customer Interview" navigates to a page displaying the generated "Interview Questionnaire". (Ref: img5)
                *   Clicking "Details" on "Landing Page" navigates to a page/modal displaying the generated landing page content/structure.
        *   **Storytelling Central Section:**
            *   Displays a title "Storytelling Central" and a subtitle.
            *   Presents Brand Wheel (Mission, Vision, Brand Values, Brand Personality), Startup Naming, and Elevator Pitch in a structured layout (e.g., tabs or distinct content blocks). A "Details" button might allow viewing this section in more detail or as a consolidated report. (Ref: img4)

5.  **Project Management (Basic)**
    *   **What it does:** Allows users to view a list of their past projects (created from prompts) and select one to view its generated content.
    *   **Why it's important:** Enables users to retain and refer back to their work.
    *   **How it works:** A user-specific list of projects, likely accessible from their dashboard or a dedicated "My Projects" page.

# User Experience

*   **User Personas:**
    *   **Aspiring Annie:** A non-technical individual with a business idea, needs help structuring her thoughts and creating initial planning documents. Wants a quick, easy way to get started.
    *   **Startup Sam:** A founder with some experience, looking to quickly prototype ideas and generate hypotheses for validation before investing significant resources. Values speed and structured output.
    *   **Product Manager Pat:** Needs to quickly outline new product initiatives or features, including defining customer problems, value propositions, and initial validation steps.
    *   **Freelancer Fred:** A developer or designer who wants to offer "idea to initial plan" services to clients, using the tool to accelerate delivery.

*   **Key User Flows:**
    1.  **Registration & Login:** User visits site -> Clicks Register -> Enters details -> Confirms email (optional) -> Logs in. / User visits site -> Clicks Login -> Enters credentials -> Accesses dashboard.
    2.  **New Project Creation & Content Generation:** User logs in -> Navigates to main dashboard -> Enters startup idea in prompt textarea -> Clicks "Generate" -> System processes and calls OpenAI -> User sees a loading/progress indicator -> Generated content is displayed in structured sections (Business Prototype, Validation, Storytelling).
    3.  **Viewing Generated Content:**
        *   User navigates to Lean Canvas section -> Views grid of canvas components. Clicks "Details" for full view.
        *   User navigates to Validation section -> Views list of Critical Hypotheses.
        *   User navigates to Validation Experiments -> Clicks "Details" on "Customer Interview" -> Views Interview Questionnaire page.
        *   User navigates to Storytelling Central -> Views Brand Wheel, Naming, Elevator Pitch.
    4.  **Accessing Past Projects:** User logs in -> Navigates to "My Projects" (or similar) -> Clicks on a project -> Views previously generated content.

*   **UI/UX Considerations:**
    *   **Clean and Intuitive Interface:** Mimic the clarity and simplicity of the example images. Minimalist design.
    *   **Clear Visual Hierarchy:** Differentiate between main sections and sub-components.
    *   **Responsive Design:** Accessible on desktop and tablet devices. Mobile view is secondary but good to consider.
    *   **Progress Indication:** Provide feedback during the content generation phase as it might take some time.
    *   **Readability:** Use clear fonts and sufficient spacing for generated text content.
    *   **Navigation:** Easy navigation between the main dashboard, project views, and detailed content pages (like the Interview Questionnaire).
    *   **"Generated" Badges:** Use "Generated" badges as shown in the images to indicate AI-created content where appropriate.
    *   **Consistency:** Maintain a consistent design language across all views and components.

</context>
<PRD>
# Technical Architecture

*   **System Components:**
    *   **Frontend:** Built with Laravel Blade templates, Livewire components for dynamic interactions (e.g., prompt submission, content display updates). Standard HTML, CSS, JavaScript.
    *   **Backend:** Laravel 12 framework.
        *   **API Layer:** RESTful or GraphQL APIs for frontend-backend communication if Livewire isn't sufficient for all interactions. Primarily internal APIs for Livewire.
        *   **Controller Layer:** Handles incoming HTTP requests, orchestrates service calls.
        *   **Service Layer:** Contains business logic, including:
            *   Prompt processing and transformation.
            *   Orchestration of OpenAI API calls for various content types.
            *   Parsing and structuring of OpenAI responses.
        *   **OpenAI Integration Client:** A dedicated service/client for managing communication with OpenAI APIs (handling API keys, request formatting, error handling).
    *   **Database:** MySQL or PostgreSQL.
    *   **Admin Panel:** FilamentPHP 3 for administrative tasks (user management, viewing generated content, analytics if any).
    *   **Task Queuing (Optional but Recommended):** Laravel Queues (e.g., with Redis or Database driver) for handling potentially long-running OpenAI generation tasks asynchronously to improve user experience.

*   **Data Models (Illustrative):**
    *   `User`: (id, name, email, password, timestamps)
    *   `Project`: (id, user_id, input_prompt, status, timestamps) - Represents a single user-initiated idea generation.
    *   `GeneratedContent`: (id, project_id, content_type, content_data (JSON), timestamps)
        *   `content_type`: Enum (e.g., 'lean_canvas_problem', 'lean_canvas_solution', 'critical_hypothesis', 'interview_questionnaire', 'brand_mission', etc.)
        *   `content_data`: JSONB field to store the structured output from OpenAI for each specific piece of content.
        *   Alternatively, have separate tables for major structured items:
            *   `LeanCanvasSection`: (id, project_id, section_name, content)
            *   `CriticalHypothesis`: (id, project_id, hypothesis_text, criticality, testing_method)
            *   `InterviewQuestionnaire`: (id, project_id, questions (JSON))
            *   `BrandWheel`: (id, project_id, mission, vision, values, personality)
            *   `StartupName`: (id, project_id, name_suggestion, rationale)
            *   `ElevatorPitch`: (id, project_id, pitch_text)

*   **APIs and Integrations:**
    *   **OpenAI API:** Primary external integration. Requires secure management of API keys.
    *   **Internal APIs (if not solely Livewire):** For fetching project data, generated content, etc.

*   **Infrastructure Requirements:**
    *   Web server (e.g., Nginx, Apache).
    *   PHP-FPM.
    *   Database server.
    *   Cache server (e.g., Redis for session, caching, queues).
    *   Potentially a separate worker server for processing queues if that approach is taken.
    *   SSL certificate for HTTPS.

# Development Roadmap

*   **Phase 1: MVP - Core Generation & Display**
    1.  **Foundation & Setup:**
        *   Confirm Laravel 12, FilamentPHP 3, Livewire, PEST, PINT, PHPStan setup is operational.
        *   Implement `composer full` command (with `composer test`, `composer format`, `composer check` sub-commands).
    2.  **User Authentication:**
        *   Basic User registration (email, password).
        *   User login and logout.
        *   Filament resource for User management.
    3.  **Project & Prompt Input:**
        *   Data model for `Project`.
        *   Livewire component for prompt input textarea on the main dashboard.
        *   Controller/Service logic to create a `Project` record.
    4.  **OpenAI Integration - Single Content Type (Proof of Concept):**
        *   Service to connect to OpenAI API.
        *   Implement generation for *one* section of the Lean Canvas (e.g., "Problem") based on the user prompt.
        *   Store the generated content.
    5.  **Basic Content Display - Single Content Type:**
        *   Livewire component to display the generated "Problem" section for the current project.
    6.  **Expand to Full Lean Canvas Generation:**
        *   Develop prompt engineering logic for all 9+ sections of the Lean Canvas.
        *   Orchestrate multiple OpenAI calls.
        *   Store all Lean Canvas sections.
        *   Develop the grid display for Lean Canvas (as per img1, initially without "Details" functionality leading to a separate page, just display all parts).
    7.  **Generate Critical Hypotheses:**
        *   Prompt engineering for Critical Hypotheses (Desirability, Viability, Feasibility).
        *   Store hypotheses.
        *   Display Critical Hypotheses in a list (as per img2).
    8.  **Generate Interview Questionnaire:**
        *   Prompt engineering for Interview Questionnaire.
        *   Store questionnaire.
        *   Display Interview Questionnaire on a dedicated page, accessible via a simple link/button (placeholder for "Details" on "Customer Interview" card).

*   **Phase 2: Completing Core Features & UI Polish**
    1.  **Full Business Prototype Section:**
        *   Implement the "Details" view for the full Lean Canvas (if not done by just showing all cards clearly in Phase 1).
        *   UI for "Business Prototype" title and subtitle.
    2.  **Full Validation Section:**
        *   Implement content generation for Landing Page (key messages, structure).
        *   Store Landing Page content.
        *   Create card display for "Validation Experiments" (Customer Interview, Landing Page) (as per img3).
        *   Link "Details" on "Customer Interview" card to the existing Interview Questionnaire page.
        *   Implement "Details" view for Landing Page content.
        *   UI for "Validation" and "Validation Experiments" titles/subtitles.
    3.  **Full Storytelling Central Section:**
        *   Implement content generation for Brand Wheel (Mission, Vision, Values, Personality), Startup Naming, and Elevator Pitch.
        *   Store generated content.
        *   Develop UI for Storytelling Central (as per img4, potentially using tabs or distinct blocks).
        *   UI for "Storytelling Central" title and subtitle.
    4.  **Basic Project Listing:**
        *   A simple page where users can see a list of their projects (by input prompt or a generated title) and click to view the generated content.
    5.  **UI/UX Refinements:**
        *   Implement loading states/progress indicators during generation.
        *   Ensure responsive design for key views.
        *   General styling and layout improvements based on provided images.
        *   "Generated" badges where appropriate.

*   **Future Enhancements (Post-MVP):**
    *   **Asynchronous Generation with Queues:** Move OpenAI calls to background jobs for better UX.
    *   **Content Editing:** Allow users to edit the generated content.
    *   **Content Export:** Allow users to export generated content (e.g., PDF, DOCX).
    *   **More Sophisticated Project Management:** Project naming, tagging, archiving.
    *   **Template Customization:** Allow users to choose different styles or focuses for generation.
    *   **Team/Collaboration Features:** Allow multiple users to collaborate on a project.
    *   **Advanced Analytics (Admin):** Track usage, popular features, etc.
    *   **Integration with other tools:** (e.g., export Lean Canvas to a Miro board).
    *   **More granular "Details" views** for each Lean Canvas block if needed.

# Logical Dependency Chain

1.  **Foundation & Setup:** (Laravel, Filament, Dev tools, `composer full`).
2.  **User Authentication:** (Registration, Login) - Essential for associating data.
3.  **Project Creation & Prompt Input:** (Core mechanism to start the process).
4.  **OpenAI Integration (Core):** (Service to call OpenAI, secure API key handling).
5.  **Single Content Generation & Display (MVP slice):**
    *   Generate *one* Lean Canvas section (e.g., "Problem").
    *   Basic display of this section. (This validates the end-to-end flow for one piece of content).
6.  **Full Lean Canvas Generation & Grid Display:** (Builds upon the single content generation, expands to the full Business Prototype MVP). This provides a significant visible output early.
7.  **Critical Hypotheses Generation & List Display:** (Another key deliverable for Validation).
8.  **Interview Questionnaire Generation & Page Display:** (Completes a core part of Validation Experiments).
9.  **Landing Page Content Generation & Display:** (Completes Validation Experiments).
10. **Storytelling Central Content Generation & Display:** (Brand Wheel, Naming, Pitch).
11. **Dashboard Structure & Navigation:** (Putting all the pieces together in the main views with proper titles, subtitles, and navigation between "Details" and main sections).
12. **Basic Project Listing:** (Allow users to access multiple generated projects).
13. **UI/UX Polish & Refinements:** (Loading states, responsiveness, visual consistency).
14. **Filament Admin Panel Features:** (User management can be done in parallel or early, other admin views as needed).

*This order prioritizes getting a visible, usable end-to-end flow for a core piece of content first, then expanding feature by feature, ensuring the frontend display keeps pace with backend generation capabilities.*

# Risks and Mitigations

*   **Risk: OpenAI API Costs & Rate Limits**
    *   **Mitigation:** Monitor API usage closely. Implement caching for identical prompts (if applicable). Optimize prompt length and number of calls. Consider allowing users to bring their own API keys in a future version or have tiered plans. Be transparent about potential costs if they are passed to users.
*   **Risk: Quality and Relevance of AI-Generated Content**
    *   **Mitigation:** Extensive prompt engineering and testing. Provide clear guidance to users on how to write effective input prompts. Allow users to regenerate or (in future) edit content. Set realistic expectations that the AI provides a "first draft" or "starting point."
*   **Risk: Prompt Engineering Complexity**
    *   **Mitigation:** Allocate sufficient time for developing and refining prompts for each of the diverse content types. This is a core R&D effort. Start with simpler outputs and iterate.
*   **Risk: Managing Multiple Asynchronous OpenAI Calls**
    *   **Mitigation:** For MVP, synchronous calls might be acceptable for a single user if total generation time is within a reasonable limit (e.g., < 30-60 seconds). For scalability and better UX, plan for and implement background job processing (Laravel Queues) in Phase 2 or as soon as performance dictates.
*   **Risk: Scope Creep for MVP**
    *   **Mitigation:** Strictly adhere to the defined MVP features. Defer non-essential features (editing, export, advanced project management) to future phases. Focus on delivering the core generation and display workflow.
*   **Risk: User Experience with Potentially Long Generation Times**
    *   **Mitigation:** Clear progress indicators, optimistic UI updates where possible. Consider streaming output if feasible with OpenAI APIs, or at least breaking down the generation into visible stages. Prioritize implementing asynchronous processing.
*   **Risk: Data Storage and Structure for Diverse Content**
    *   **Mitigation:** Design a flexible database schema (e.g., using JSONB fields for `content_data` or a well-thought-out set of related tables) that can accommodate various structured and unstructured text outputs.

# Appendix

*   **Research Findings:** (To be populated with findings from gozigzag.com analysis, user interviews, competitive analysis if conducted).
*   **OpenAI API Version:** Specify target OpenAI model(s) (e.g., GPT-4, GPT-3.5-turbo) and any specific API versions.
*   **Detailed Prompt Examples:** (Internal document for developers, containing initial drafts of prompts for each generated item).
*   **Image References:** (The provided images img1-img5 serve as visual guides for UI layout and content structure).
</PRD>