<?php

namespace Database\Factories;

use App\Models\Account;
use App\Models\AccountHistory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountHistory>
 */
class AccountHistoryFactory extends Factory
{
    protected $model = AccountHistory::class;

    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'action' => fake()->randomElement(['blocked', 'unblocked']),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Set the action to blocked.
     */
    public function blocked(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => 'blocked',
            'description' => fake()->sentence(),
        ]);
    }

    /**
     * Set the action to unblocked.
     */
    public function unblocked(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => 'unblocked',
            'description' => null,
        ]);
    }
}
