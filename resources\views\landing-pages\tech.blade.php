<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="{{ $meta['viewport'] }}">
    <meta name="description" content="{{ $meta['description'] }}">
    <meta name="keywords" content="{{ $meta['keywords'] }}">
    <meta name="author" content="{{ $meta['author'] }}">
    <title>{{ $meta['title'] }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            line-height: 1.6;
            color: #e2e8f0;
            background: #0a0a0a;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(45deg, transparent 24%, rgba(0, 255, 255, 0.05) 25%, rgba(0, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.05) 75%, rgba(0, 255, 255, 0.05) 76%, transparent 77%),
                linear-gradient(-45deg, transparent 24%, rgba(255, 0, 255, 0.05) 25%, rgba(255, 0, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 0, 255, 0.05) 75%, rgba(255, 0, 255, 0.05) 76%, transparent 77%);
            background-size: 60px 60px;
            animation: tech-grid 20s linear infinite;
            z-index: -1;
        }
        
        @keyframes tech-grid {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #00ffff;
            z-index: 1000;
            padding: 16px 0;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00ffff;
            text-decoration: none;
            text-shadow: 0 0 10px #00ffff;
            font-family: 'JetBrains Mono', monospace;
        }
        
        /* Hero Section */
        .hero {
            padding: 140px 0 100px;
            text-align: center;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 24px;
            font-weight: 700;
            line-height: 1.2;
            color: #ffffff;
            text-shadow: 0 0 20px #00ffff;
            font-family: 'JetBrains Mono', monospace;
        }
        
        .hero .subtitle {
            font-size: 1.25rem;
            margin-bottom: 40px;
            color: #b0b0b0;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Button Styles */
        .btn {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            color: #000;
            text-decoration: none;
            border-radius: 0;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'JetBrains Mono', monospace;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
        }
        
        .btn-secondary {
            background: transparent;
            border: 2px solid #00ffff;
            color: #00ffff;
            margin-left: 16px;
            box-shadow: none;
        }
        
        .btn-secondary:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }
        
        /* Section Styles */
        .section {
            padding: 100px 0;
            position: relative;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }
        
        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #ffffff;
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
            text-shadow: 0 0 10px #00ffff;
        }
        
        .section-subtitle {
            font-size: 1.2rem;
            color: #b0b0b0;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* Problem & Solution */
        .problem-solution {
            background: #111111;
            border-top: 1px solid #333;
            border-bottom: 1px solid #333;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }
        
        .column {
            padding: 40px;
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid #00ffff;
            position: relative;
        }
        
        .column::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 49%, #00ffff 50%, transparent 51%);
            opacity: 0.1;
            pointer-events: none;
        }
        
        .column h3 {
            font-size: 1.8rem;
            margin-bottom: 24px;
            color: #00ffff;
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }
        
        .column p {
            font-size: 1.1rem;
            color: #e2e8f0;
            line-height: 1.7;
        }
        
        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border: 1px solid #333;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .feature-card:hover::before {
            opacity: 1;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
            border-color: #00ffff;
            box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-size: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .feature-card h3 {
            color: #00ffff;
            margin-bottom: 16px;
            font-family: 'JetBrains Mono', monospace;
            position: relative;
            z-index: 1;
        }
        
        .feature-card p {
            color: #b0b0b0;
            position: relative;
            z-index: 1;
        }
        
        /* Stats Section */
        .stats {
            background: #000000;
            border-top: 1px solid #00ffff;
            border-bottom: 1px solid #00ffff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3rem;
            margin-bottom: 8px;
            color: #00ffff;
            font-weight: 800;
            font-family: 'JetBrains Mono', monospace;
            text-shadow: 0 0 20px #00ffff;
        }
        
        .stat-item p {
            font-size: 1.1rem;
            color: #b0b0b0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* CTA Section */
        .cta {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            text-align: center;
            position: relative;
        }
        
        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.1) 0%, transparent 70%);
        }
        
        .cta-content {
            position: relative;
            z-index: 1;
        }
        
        .cta h2 {
            color: #ffffff;
            margin-bottom: 24px;
            text-shadow: 0 0 20px #ff00ff;
        }
        
        .cta p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            color: #b0b0b0;
        }
        
        /* Footer */
        .footer {
            background: #000000;
            padding: 60px 0 40px;
            text-align: center;
            border-top: 1px solid #333;
        }
        
        .footer p {
            color: #666;
            font-family: 'JetBrains Mono', monospace;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .two-column {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .section h2 {
                font-size: 2rem;
            }
            
            .btn-secondary {
                margin-left: 0;
                margin-top: 16px;
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }
            
            .container {
                padding: 0 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="container">
            <div class="nav">
                <a href="#" class="logo">
                    &gt;{{ $content['storytelling']['startup_naming']['business_name'] ?? $content['storytelling']['startup_naming']['name'] ?? $meta['title'] }}_
                </a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>&gt; {{ $content['storytelling']['startup_naming']['tagline'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'NEXT.GEN.SOLUTION' }}</h1>
                <p class="subtitle">
                    {{ $content['storytelling']['elevator_pitch']['pitch'] ?? $content['lean_canvas']['unique_value_proposition']['content'] ?? 'Cutting-edge technology that transforms your digital experience through innovative automation and intelligent systems.' }}
                </p>
                <div class="hero-actions">
                    <a href="#contact" class="btn">INITIALIZE</a>
                    <a href="#features" class="btn btn-secondary">SCAN_FEATURES</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem & Solution Section -->
    <section class="section problem-solution">
        <div class="container">
            <div class="two-column">
                <div class="column">
                    <h3>&gt; PROBLEM_DETECTED</h3>
                    <p>
                        @if(isset($content['lean_canvas']['problem']['content']))
                            {{ $content['lean_canvas']['problem']['content'] }}
                        @else
                            Legacy systems and outdated processes create inefficiencies that limit potential and growth in the digital ecosystem.
                        @endif
                    </p>
                </div>
                <div class="column">
                    <h3>&gt; SOLUTION_ACTIVE</h3>
                    <p>
                        @if(isset($content['lean_canvas']['solution']['content']))
                            {{ $content['lean_canvas']['solution']['content'] }}
                        @else
                            Our advanced platform leverages cutting-edge algorithms and intelligent automation to optimize performance and deliver measurable results.
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section" id="features">
        <div class="container">
            <div class="section-header">
                <h2>&gt; CORE_FEATURES</h2>
                <p class="section-subtitle">
                    @if(isset($content['lean_canvas']['unfair_advantage']['content']))
                        {{ $content['lean_canvas']['unfair_advantage']['content'] }}
                    @else
                        Advanced capabilities designed for maximum efficiency and unparalleled performance.
                    @endif
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>HIGH_PERFORMANCE</h3>
                    <p>Optimized algorithms delivering lightning-fast processing and real-time results.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>SECURE_PROTOCOL</h3>
                    <p>Military-grade encryption and advanced security measures protecting your data.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>AI_INTEGRATION</h3>
                    <p>Machine learning capabilities that adapt and evolve with your specific needs.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    @if(isset($content['lean_canvas']['key_metrics']))
    <section class="section stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>99.9%</h3>
                    <p>UPTIME_GUARANTEE</p>
                </div>
                <div class="stat-item">
                    <h3>10K+</h3>
                    <p>ACTIVE_USERS</p>
                </div>
                <div class="stat-item">
                    <h3>&lt;1MS</h3>
                    <p>RESPONSE_TIME</p>
                </div>
                <div class="stat-item">
                    <h3>24/7</h3>
                    <p>SYSTEM_MONITOR</p>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- CTA Section -->
    <section class="section cta" id="contact">
        <div class="container">
            <div class="cta-content">
                <h2>&gt; READY_TO_DEPLOY?</h2>
                <p>Join the next generation of innovators using our cutting-edge technology platform.</p>
                <a href="#" class="btn">EXECUTE_NOW</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; {{ date('Y') }} {{ $content['storytelling']['startup_naming']['business_name'] ?? $meta['title'] }}. ALL_RIGHTS_RESERVED.</p>
        </div>
    </footer>
</body>
</html> 