<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string $phone
 * @property string|null $avatar
 * @property string $status
 * @property string|null $block_reason
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Database\Factories\AccountFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Account query()
 * @method static \Illuminate\Database\Eloquent\Builder|Account withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Account onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Account find(int $id)
 * @method static Account create(array $attributes = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Account where($column, $operator = null, $value = null, $boolean = 'and')
 */
class Account extends Authenticatable implements FilamentUser, HasName
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'avatar',
        'status',
        'block_reason',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function histories(): HasMany
    {
        return $this->hasMany(AccountHistory::class);
    }

    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    public function block(string $reason): void
    {
        if (empty(trim($reason))) {
            throw new \InvalidArgumentException('Block reason cannot be empty');
        }

        $this->update([
            'status' => 'blocked',
            'block_reason' => $reason,
        ]);

        $this->histories()->create([
            'action' => 'blocked',
            'description' => $reason,
        ]);
    }

    public function unblock(): void
    {
        $this->update([
            'status' => 'active',
            'block_reason' => null,
        ]);

        $this->histories()->create([
            'action' => 'unblocked',
        ]);
    }

    public static function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:accounts,email'],
            'phone' => ['required', 'string', 'max:255'],
            'password' => ['required', 'string', 'min:8'],
            'status' => ['required', 'in:active,blocked'],
            'block_reason' => ['nullable', 'string', 'max:65535'],
        ];
    }

    public function canAccessPanel(Panel $panel): bool
    {
        // Only allow active accounts to access Filament panels
        return $this->status === 'active';
    }

    public function getFilamentName(): string
    {
        return $this->name;
    }
}
