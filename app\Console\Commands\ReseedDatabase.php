<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ReseedDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:reseed {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Drop all tables, run migrations, and seed the database with fresh data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🌱 Venture Discovery - Database Reseeding');
        $this->info('==========================================');
        $this->newLine();

        // Check if we should ask for confirmation
        if (! $this->option('force')) {
            $this->warn('⚠️  This will drop all existing data and reseed the database.');

            if (! $this->confirm('Are you sure you want to continue?', false)) {
                $this->error('❌ Operation cancelled.');

                return Command::FAILURE;
            }
        }

        $this->newLine();
        $this->info('🔄 Starting database refresh...');

        // Clear caches
        $this->info('🧹 Clearing caches...');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');

        // Fresh migration with seeders
        $this->info('🗃️  Running fresh migration with seeders...');

        $exitCode = Artisan::call('migrate:fresh', ['--seed' => true]);

        if ($exitCode === 0) {
            $this->newLine();
            $this->info('✅ Database reseeding completed successfully!');
            $this->newLine();

            // Display summary
            $this->displaySummary();

            $this->newLine();
            $this->info('🚀 You can now access the application with fresh data!');

            return Command::SUCCESS;
        } else {
            $this->newLine();
            $this->error('❌ Database reseeding failed. Please check the error messages above.');

            return Command::FAILURE;
        }
    }

    /**
     * Display a summary of the seeded data and login credentials.
     */
    private function displaySummary(): void
    {
        $this->info('📊 Summary:');
        $this->line('   • 7 admin users created (for admin panel access)');
        $this->line('   • 21 accounts created (app users including entrepreneurs)');
        $this->line('   • 20 projects created with realistic startup ideas');
        $this->line('   • Generated content created for applicable projects');

        $this->newLine();
        $this->info('🔑 Login Credentials:');
        $this->line('   <fg=yellow>🔐 Admin Panel:</> <EMAIL> / password');
        $this->line('   <fg=yellow>🔐 Super Admin:</> <EMAIL> / password');
        $this->line('   <fg=yellow>🔐 Demo Admin:</> <EMAIL> / password');
        $this->line('   <fg=green>👤 App Users:</> <EMAIL> / password');
        $this->line('               <EMAIL> / password');
        $this->line('               <EMAIL> / password');
        $this->line('               <EMAIL> / password');
    }
}
