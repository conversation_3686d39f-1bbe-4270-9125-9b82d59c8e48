# Venture Discovery

**Venture Discovery** is a web application designed to empower entrepreneurs and startup founders. By inputting a single idea prompt, users can rapidly generate foundational business, validation, and storytelling assets. This project leverages OpenAI's generative capabilities to transform an initial concept into a structured set of documents and plans, significantly accelerating the early stages of startup development.

The development of Venture Discovery itself is an experiment in AI-assisted software engineering. This README outlines the process and tools used to build the application with the help of AI agents.

## Fast Run 

just run this command for run or update

```bash
sh run.sh
```

## Our AI-Driven Development Workflow

We employ a systematic approach to leverage AI in building Venture Discovery. Here's a breakdown of the key stages and tools:

### 1. Product Requirements Document (PRD) Generation
   - **Goal:** To establish a clear, comprehensive specification of the software to be built, which serves as the primary input for the AI development agent.
   - **Process:**
     1.  Define the core requirements and functionalities of Venture Discovery.
     2.  Craft a detailed prompt (see `scripts/google-ai-prompt.txt`) designed to elicit a thorough PRD.
     3.  Utilize **[Google AI Studio](https://aistudio.google.com/)** with the **Gemini 2.5 Pro Model** to generate the PRD based on the crafted prompt.
   - **Outcome:** A detailed PRD that guides the AI agent in subsequent development tasks.

### 2. Establishing the Starter Kit / Boilerplate
   - **Goal:** To provide the AI agent with a pre-configured, foundational codebase. This accelerates development by handling initial setup and ensuring consistency.
   - **Tech Stack for the Starter Kit:**
     -   **Backend Framework:** Laravel 12
     -   **Admin Panel:** FilamentPHP 3
     -   **User Management:** Filament Users Plugin
     -   **Testing:** PEST (PHP Testing Framework)
     -   **Code Formatting:** Pint
     -   **Static Analysis:** PHPStan
   - **Outcome:** A ready-to-use project skeleton that the AI can immediately start building upon.

### 3. Generating Codebase Context with Repomix
   - **Goal:** To provide the AI agent with an up-to-date understanding of the existing codebase structure and content, enabling it to make informed decisions and generate relevant code.
   - **Tool:** **[Repomix](https://github.com/yamadashy/repomix)**
   - **Process:**
     1.  Run the command `repomix .` in the project root.
     2.  This generates an `repomix-output.xml` file, which represents the current state of the codebase.
     3.  This XML file **must be provided as context** to the AI agent with each new prompt or development task.
     4.  **Important:** Regenerate this file using `repomix .` after any significant code changes to keep the AI's context accurate.
   - **Outcome:** An XML context file that helps the AI "understand" the project's current state.

### 4. Managing AI Tasks with Task Master
   - **Goal:** To structure the AI's work, track progress, and manage the development lifecycle when delegating tasks to an AI agent. This helps in breaking down complex features into manageable units.
   - **Tool:** **[Task Master](https://www.task-master.dev/)**
   - **Process:**
     1.  After setting up the starter kit, initialize Task Master using `task-master init`.
     2.  This sets up the necessary files and server (e.g., MCP server) for Task Master.
     3.  Use Task Master to define, assign, and monitor development tasks for the AI agent.
   - **Outcome:** A structured task management system tailored for AI-driven development.

### 5. AI-Assisted Development with Cursor
   - **Goal:** To actively develop the application by providing instructions, context, and tasks to an AI agent integrated within an IDE.
   - **Tool:** **Cursor** (AI-powered IDE/Agent)
   - **Process:**
     1.  Open the Venture Discovery project in Cursor.
     2.  When initiating a new development task or prompt:
         -   Provide the latest `repomix-output.xml` for codebase context.
         -   Use clear, specific prompts (refer to examples in the `scripts/` folder).
         -   Leverage tasks defined in Task Master if applicable.
   - **Outcome:** Code generation, modification, and problem-solving facilitated by the AI agent within the development environment.

### 6. Iterate and Commit Frequently
   - **Best Practice:** Commit changes to Git (e.g., GitHub) after each significant, successfully completed task or feature implemented by the AI.
   - **Rationale:**
     -   **Version Control:** AI-generated code can sometimes be unpredictable or require refinement. Frequent commits allow for easy rollback if the AI produces undesirable results or gets stuck.
     -   **Progress Tracking:** Commits mark milestones in the AI-assisted development process.
     -   **Resilience:** Protects against data loss due to issues like internet connectivity problems or AI agent errors that are difficult to resolve directly.
   - **Outcome:** A robust development history that supports iterative refinement and risk mitigation.

## Scripts

The `scripts/` directory contains:
-   `google-ai-prompt.txt`: The prompt used with Google AI Studio to generate the initial PRD.
-   *(Potentially other example prompts for Cursor or other AI interactions in the future.)*

# Database Seeders

This directory contains seeders for populating the Venture Discovery application with sample data for development and testing.

## Overview

The seeding process creates realistic sample data across multiple models:

- **Admin Users** (`users` table) - For admin panel access
- **App Users** (`accounts` table) - Entrepreneurs using the app
- **Projects** - Startup ideas and business concepts
- **Generated Content** - AI-generated Lean Canvas sections

## Seeders

### 1. UserSeeder
Creates admin users for accessing the Filament admin panel.

**Creates:**
- 4 specific admin users with known credentials
- 3 additional random admin users via factory

**Admin Credentials:**
- `<EMAIL>` / `password` (Main admin)
- `<EMAIL>` / `password` (Super admin)
- `<EMAIL>` / `password` (System admin)
- `<EMAIL>` / `password` (Demo admin)

### 2. AccountSeeder
Creates app users (entrepreneurs) who use the main application.

**Creates:**
- 1 admin account (for testing)
- 4 demo entrepreneur accounts with known credentials
- 1 blocked account (for testing)
- 15 additional random accounts via factory

**Demo User Credentials:**
- `<EMAIL>` / `password`
- `<EMAIL>` / `password`
- `<EMAIL>` / `password`
- `<EMAIL>` / `password`

### 3. ProjectSeeder
Creates realistic startup project ideas with various statuses.

**Creates:**
- 10 specific projects with realistic startup concepts:
  - EcoTrack (carbon footprint tracking)
  - SkillSwap (peer-to-peer learning)
  - LocalFresh (local farmer subscription)
  - MindfulWork (workplace wellness)
  - RetireEasy (gig economy retirement planning)
  - PetConnect (pet care platform)
  - StudyBuddy (AI study companion)
  - HomeHelper (home service provider)
  - FitFlex (personalized fitness)
  - CommunityShare (neighborhood resource sharing)
- 10 additional random projects via factory

**Project Statuses:**
- `completed` - Projects with full generated content
- `processing` - Projects currently being processed
- `pending` - Projects waiting to be processed

### 4. GeneratedContentSeeder
Creates AI-generated Lean Canvas content for projects.

**Creates:**
- Complete Lean Canvas sections for "completed" projects
- Problem sections for all applicable projects
- Realistic content for EcoTrack, SkillSwap, and LocalFresh
- Generic content for other projects

**Content Types:**
- `lean_canvas_problem` - Problem identification
- `lean_canvas_solution` - Solution description
- `lean_canvas_uvp` - Unique Value Proposition
- `lean_canvas_advantage` - Unfair Advantage
- `lean_canvas_customers` - Customer Segments
- `lean_canvas_alternatives` - Existing Alternatives
- `lean_canvas_metrics` - Key Metrics
- `lean_canvas_channels` - Channels
- `lean_canvas_costs` - Cost Structure
- `lean_canvas_revenue` - Revenue Streams

## Running Seeders

### Option 1: Artisan Command (Recommended)
```bash
# Fresh migration with all seeders
php artisan db:reseed

# Or with force flag to skip confirmation
php artisan db:reseed --force
```

### Option 2: Shell Script
```bash
# Make executable (first time only)
chmod +x scripts/reseed.sh

# Run the script
./scripts/reseed.sh
```

### Option 3: Standard Laravel Commands
```bash
# Fresh migration with seeders
php artisan migrate:fresh --seed

# Or run specific seeder
php artisan db:seed --class=UserSeeder
```

## Data Summary

After running all seeders, you'll have:
- **7 admin users** (4 specific + 3 random)
- **21 app users** (6 specific + 15 random)
- **20 projects** (10 specific + 10 random)
- **23+ generated content items** (varies by project status)

## Authentication

### Admin Panel Access
Use any of the admin user credentials to access the Filament admin panel at `/admin`.

### App Access
Use any of the demo account credentials to access the main application.

## Development Notes

- All passwords are set to `password` for development convenience
- Email verification is pre-completed for all accounts
- Projects have realistic prompts and varied statuses
- Generated content includes metadata like token counts and generation timestamps
- The seeding process respects foreign key relationships and dependencies

## Customization

To modify the seed data:

1. **Add new admin users**: Edit `UserSeeder.php`
2. **Add new app users**: Edit `AccountSeeder.php`
3. **Add new projects**: Edit `ProjectSeeder.php`
4. **Modify generated content**: Edit `GeneratedContentSeeder.php`

Remember to update the summary counts in `DatabaseSeeder.php` if you change the number of records created. 

# Venture Discovery Platform - Deployment Scripts

This directory contains automated deployment scripts for setting up the Venture Discovery Platform on different operating systems.

## Available Scripts

### 1. macOS Deployment (`deploy-macos.sh`)

Automated deployment script for macOS systems using Homebrew.

**Prerequisites:**
- macOS 10.15 (Catalina) or later
- Command Line Tools for Xcode
- Administrator privileges

**What it installs:**
- Homebrew (if not already installed)
- PHP 8.2 with required extensions
- Composer
- Node.js 20
- MySQL 8.0
- Redis
- Nginx
- Supervisor (for queue management)
- Additional development tools

**Usage:**
```bash
# Make the script executable (if not already)
chmod +x scripts/deploy-macos.sh

# Run the deployment script
./scripts/deploy-macos.sh
```

### 2. Ubuntu Deployment (`deploy-ubuntu.sh`)

Automated deployment script for Ubuntu systems (20.04+).

**Prerequisites:**
- Ubuntu 20.04 LTS or later
- User account with sudo privileges
- Internet connection

**What it installs:**
- PHP 8.2 with FPM and required extensions
- Composer
- Node.js 20
- MySQL 8.0
- Redis
- Nginx
- Supervisor (for queue management)
- Certbot (for SSL certificates)
- Fail2ban (for security)
- UFW Firewall
- Monitoring and backup scripts

**Usage:**
```bash
# Make the script executable (if not already)
chmod +x scripts/deploy-ubuntu.sh

# Run the deployment script
./scripts/deploy-ubuntu.sh
```

## What the Scripts Do

### System Setup
1. **Package Management**: Updates system packages and installs required repositories
2. **Runtime Installation**: Installs PHP, Node.js, and required extensions
3. **Database Setup**: Installs and configures MySQL with a dedicated database
4. **Caching**: Installs and configures Redis for session and cache storage
5. **Web Server**: Configures Nginx with optimized settings for Laravel

### Application Setup
1. **Project Directory**: Creates and configures the project directory with proper permissions
2. **Dependencies**: Installs PHP and Node.js dependencies
3. **Environment**: Sets up environment configuration and generates application key
4. **Database**: Runs migrations and optionally seeds the database
5. **Assets**: Builds frontend assets for production

### Production Features
1. **Queue Workers**: Configures Supervisor to manage Laravel queue workers
2. **SSL Certificates**: Optional SSL setup (Let's Encrypt for Ubuntu, mkcert for macOS)
3. **Security**: Firewall configuration and security headers
4. **Monitoring**: Basic service monitoring and health checks
5. **Backups**: Automated daily backup scripts (Ubuntu only)
6. **Log Rotation**: Configures log rotation to prevent disk space issues

## Post-Deployment Configuration

After running either script, you'll need to:

### 1. Update Environment Configuration

Edit the `.env` file with your specific settings:

```bash
# For macOS
nano /usr/local/var/www/venture-discovery/.env

# For Ubuntu
sudo nano /var/www/venture-discovery/.env
```

**Required configurations:**
- `OPENAI_API_KEY`: Your OpenAI API key for AI-powered features
- `MAIL_*`: Email configuration for notifications
- `DB_PASSWORD`: Update with the secure password you set

### 2. Secure MySQL Installation

Run the MySQL security script:

```bash
# For both macOS and Ubuntu
sudo mysql_secure_installation
```

### 3. Update Database Credentials

Update the `.env` file with your MySQL credentials:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=venture_discovery
DB_USERNAME=venture_user
DB_PASSWORD=your_secure_password
```

### 4. Test the Installation

Visit your application:
- **macOS**: http://venture-discovery.local
- **Ubuntu**: http://venture-discovery.local (or your domain if configured)

## Service Management

### macOS (Homebrew Services)

```bash
# Start all services
brew services start nginx mysql redis supervisor

# Stop all services
brew services stop nginx mysql redis supervisor

# Restart a specific service
brew services restart nginx
```

### Ubuntu (Systemd)

```bash
# Start all services
sudo systemctl start nginx mysql redis-server php8.2-fpm supervisor

# Stop all services
sudo systemctl stop nginx mysql redis-server php8.2-fpm supervisor

# Restart a specific service
sudo systemctl restart nginx

# Check service status
sudo systemctl status nginx
```

## Monitoring and Maintenance

### Log Files

**Application Logs:**
- macOS: `/usr/local/var/www/venture-discovery/storage/logs/laravel.log`
- Ubuntu: `/var/www/venture-discovery/storage/logs/laravel.log`

**Web Server Logs:**
- macOS: `/opt/homebrew/var/log/nginx/`
- Ubuntu: `/var/log/nginx/`

**Queue Worker Logs:**
- macOS: `/usr/local/var/www/venture-discovery/storage/logs/worker.log`
- Ubuntu: `/var/www/venture-discovery/storage/logs/worker.log`

### Queue Worker Management

```bash
# Check queue worker status
sudo supervisorctl status

# Restart queue workers
sudo supervisorctl restart venture-discovery-worker:*

# View queue worker logs
sudo supervisorctl tail venture-discovery-worker:venture-discovery-worker_00
```

### Backup Management (Ubuntu Only)

The Ubuntu script sets up automated daily backups:

```bash
# Manual backup
sudo /usr/local/bin/venture-discovery-backup.sh

# View backup files
ls -la /var/backups/venture-discovery/

# Restore from backup
mysql -u venture_user -p venture_discovery < /var/backups/venture-discovery/database_YYYYMMDD_HHMMSS.sql
```

## SSL Configuration

### macOS (Development SSL with mkcert)

The script optionally installs mkcert for local SSL certificates:

```bash
# Create SSL certificate
mkcert venture-discovery.local

# Update Nginx configuration manually for HTTPS
```

### Ubuntu (Production SSL with Let's Encrypt)

The script can automatically configure SSL with Let's Encrypt:

```bash
# Manual SSL setup
sudo certbot --nginx -d yourdomain.com

# Check certificate renewal
sudo certbot renew --dry-run
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure proper file permissions
   ```bash
   # macOS
   sudo chown -R $(whoami):staff /usr/local/var/www/venture-discovery
   
   # Ubuntu
   sudo chown -R $USER:www-data /var/www/venture-discovery
   ```

2. **Database Connection Issues**: Verify MySQL is running and credentials are correct
   ```bash
   # Test database connection
   mysql -u venture_user -p venture_discovery
   ```

3. **Queue Workers Not Processing**: Check Supervisor status
   ```bash
   sudo supervisorctl status
   sudo supervisorctl restart venture-discovery-worker:*
   ```

4. **Nginx Configuration Errors**: Test configuration
   ```bash
   sudo nginx -t
   ```

### Getting Help

1. Check the application logs for specific error messages
2. Verify all services are running
3. Ensure environment configuration is correct
4. Check file permissions and ownership

## Security Considerations

### macOS Development Environment
- Uses local development certificates
- Basic firewall configuration
- Suitable for development and testing

### Ubuntu Production Environment
- Includes comprehensive security measures
- Firewall configuration with UFW
- Fail2ban for intrusion prevention
- SSL certificate automation
- Regular security updates recommended

## Customization

Both scripts can be customized by modifying the configuration variables at the top:

```bash
# Configuration
PROJECT_NAME="venture-discovery"
PHP_VERSION="8.2"
NODE_VERSION="20"
MYSQL_VERSION="8.0"
```

You can also modify the repository URL and other settings as needed for your specific deployment requirements.

## Support

For issues specific to the deployment scripts, please check:
1. The script output for error messages
2. System logs for service-specific issues
3. The main project documentation for application-specific problems

Remember to keep your system and dependencies updated for security and performance. 