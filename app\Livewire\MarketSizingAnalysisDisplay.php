<?php

namespace App\Livewire;

use App\Jobs\GenerateMarketSizingAnalysisJob;
use App\Models\GeneratedContent;
use App\Models\Project;
use App\Services\ContentGenerationService;
use Exception;
use Livewire\Component;

class MarketSizingAnalysisDisplay extends Component
{
    public ?Project $project = null;

    public array $analyses = [];

    public array $loadingStates = [
        'tam' => false,
        'sam' => false,
        'som' => false,
        'all' => false,
    ];

    public array $errorMessages = [];

    public bool $showSuccessMessage = false;

    public string $successMessage = '';

    protected ?ContentGenerationService $contentService = null;

    public function mount(?Project $project = null): void
    {
        $this->project = $project;
        $this->loadExistingAnalyses();
    }

    public function getProject(): Project
    {
        if (! $this->project) {
            throw new Exception('Project not available');
        }

        return $this->project;
    }

    protected function getContentService(): ContentGenerationService
    {
        if (! $this->contentService) {
            $this->contentService = app(ContentGenerationService::class);
        }

        return $this->contentService;
    }

    public function loadExistingAnalyses(): void
    {
        try {
            $project = $this->getProject();
            $this->analyses = $this->getContentService()->getMarketSizingAnalysis($project);
            $this->clearErrors();
        } catch (Exception $e) {
            $this->setError('general', 'Failed to load existing analyses: '.$e->getMessage());
        }
    }

    public function generateAllAnalyses(): void
    {
        $this->loadingStates['all'] = true;
        $this->clearErrors();
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateMarketSizingAnalysisJob::dispatch($project);

            $this->showSuccess('Market sizing analysis generation started! This may take a few moments. Page will update automatically when complete.');

            // Immediately trigger polling
            $this->dispatch('start-polling');

        } catch (Exception $e) {
            $this->setError('all', 'Failed to start generation: '.$e->getMessage());
        } finally {
            $this->loadingStates['all'] = false;
        }
    }

    public function generateAnalysis(string $type): void
    {
        if (! in_array($type, ['tam', 'sam', 'som'])) {
            $this->setError($type, 'Invalid analysis type');
            return;
        }

        $this->loadingStates[$type] = true;
        $this->clearError($type);
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateMarketSizingAnalysisJob::dispatch($project, $type, false);

            $this->showSuccess(strtoupper($type).' analysis generation started!');
        } catch (Exception $e) {
            $this->setError($type, 'Failed to start generation: '.$e->getMessage());
        } finally {
            $this->loadingStates[$type] = false;
        }
    }

    public function regenerateAnalysis(string $type): void
    {
        if (! in_array($type, ['tam', 'sam', 'som'])) {
            $this->setError($type, 'Invalid analysis type');
            return;
        }

        $this->loadingStates[$type] = true;
        $this->clearError($type);
        $this->showSuccessMessage = false;

        try {
            $project = $this->getProject();

            // Dispatch job for background processing
            GenerateMarketSizingAnalysisJob::dispatch($project, $type, true);

            $this->showSuccess(strtoupper($type).' analysis regeneration started!');
        } catch (Exception $e) {
            $this->setError($type, 'Failed to start regeneration: '.$e->getMessage());
        } finally {
            $this->loadingStates[$type] = false;
        }
    }

    public function hasAnalysis(string $type): bool
    {
        return isset($this->analyses[$type]) && $this->analyses[$type] instanceof GeneratedContent;
    }

    public function hasAllAnalyses(): bool
    {
        return $this->hasAnalysis('tam') &&
               $this->hasAnalysis('sam') &&
               $this->hasAnalysis('som');
    }

    public function hasAnyAnalyses(): bool
    {
        return $this->hasAnalysis('tam') ||
               $this->hasAnalysis('sam') ||
               $this->hasAnalysis('som');
    }

    public function pollForUpdates()
    {
        $this->loadExistingAnalyses();
        
        // Stop polling if all analyses are generated
        if ($this->hasAllAnalyses()) {
            $this->dispatch('stop-polling');
        }
    }

    public function getAnalysisData(string $type): ?array
    {
        if (! $this->hasAnalysis($type)) {
            return null;
        }

        return $this->analyses[$type]->content_data ?? [];
    }

    public function getAnalysesList(): array
    {
        $list = [];

        foreach (['tam', 'sam', 'som'] as $type) {
            if ($this->hasAnalysis($type)) {
                $data = $this->getAnalysisData($type);
                $list[] = [
                    'type' => $type,
                    'title' => $this->getAnalysisTitle($type),
                    'description' => $this->getAnalysisDescription($type),
                    'market_size_value' => $data['market_size_value'] ?? 'N/A',
                    'market_size_currency' => $data['market_size_currency'] ?? 'USD',
                    'calculation_method' => $data['calculation_method'] ?? 'N/A',
                    'confidence_level' => $data['confidence_level'] ?? 'Medium',
                    'growth_rate' => $data['growth_rate'] ?? 'N/A',
                    'time_frame' => $data['time_frame'] ?? 'N/A',
                    'key_insights' => $data['key_insights'] ?? [],
                    'market_description' => $data['market_description'] ?? 'N/A',
                    'timestamp' => $this->getAnalysisTimestamp($type),
                ];
            }
        }

        return $list;
    }

    public function getAnalysisTitle(string $type): string
    {
        return match ($type) {
            'tam' => 'Total Addressable Market',
            'sam' => 'Serviceable Addressable Market',
            'som' => 'Serviceable Obtainable Market',
            default => strtoupper($type),
        };
    }

    public function getAnalysisDescription(string $type): string
    {
        return match ($type) {
            'tam' => 'The total revenue opportunity available if the product achieved 100% market share',
            'sam' => 'The portion of TAM that your business model can realistically target',
            'som' => 'The portion of SAM that you can realistically capture given your resources and competition',
            default => '',
        };
    }

    public function getAnalysisTimestamp(string $type): ?string
    {
        if (! $this->hasAnalysis($type)) {
            return null;
        }

        $data = $this->getAnalysisData($type);
        return $data['generated_at'] ?? null;
    }

    public function getConfidenceLevelColor(string $confidenceLevel): string
    {
        return match (strtolower($confidenceLevel)) {
            'high' => 'text-green-600 dark:text-green-400',
            'medium' => 'text-yellow-600 dark:text-yellow-400',
            'low' => 'text-red-600 dark:text-red-400',
            default => 'text-gray-600 dark:text-gray-400',
        };
    }

    public function getAnalysisTypeIcon(string $type): string
    {
        return match ($type) {
            'tam' => 'heroicon-o-globe-alt',
            'sam' => 'heroicon-o-map',
            'som' => 'heroicon-o-target',
            default => 'heroicon-o-chart-bar',
        };
    }

    public function formatMarketSize(string $value, string $currency = 'USD'): string
    {
        // Handle different formats of market size values
        if (preg_match('/\$?(\d+(?:\.\d+)?)\s*(trillion|billion|million|thousand|B|M|K|T)/i', $value, $matches)) {
            $number = floatval($matches[1]);
            $unit = strtolower($matches[2]);
            
            $unitMap = [
                'trillion' => 'T',
                'billion' => 'B',
                'million' => 'M', 
                'thousand' => 'K',
                't' => 'T',
                'b' => 'B',
                'm' => 'M',
                'k' => 'K',
            ];
            
            $shortUnit = $unitMap[$unit] ?? $unit;
            return $currency === 'USD' ? '$' . $number . $shortUnit : $number . $shortUnit . ' ' . $currency;
        }
        
        // Handle values that already have currency symbols
        if (preg_match('/^\$[\d,.]+(?: (trillion|billion|million|T|B|M))?/i', $value)) {
            return $value;
        }
        
        // Handle raw numbers (assume millions if no unit specified and number is large)
        if (is_numeric(str_replace(['$', ','], '', $value))) {
            $numericValue = floatval(str_replace(['$', ','], '', $value));
            if ($numericValue >= 1000000000000) {
                return '$' . round($numericValue / 1000000000000, 1) . 'T';
            } elseif ($numericValue >= 1000000000) {
                return '$' . round($numericValue / 1000000000, 1) . 'B';
            } elseif ($numericValue >= 1000000) {
                return '$' . round($numericValue / 1000000, 1) . 'M';
            }
        }
        
        return $value;
    }

    private function setError(string $key, string $message): void
    {
        $this->errorMessages[$key] = $message;
    }

    private function clearError(string $key): void
    {
        unset($this->errorMessages[$key]);
    }

    private function clearErrors(): void
    {
        $this->errorMessages = [];
    }

    private function showSuccess(string $message): void
    {
        $this->successMessage = $message;
        $this->showSuccessMessage = true;
        
        // Auto-hide after 5 seconds
        $this->dispatch('hide-success-after-delay');
    }

    public function hideSuccessMessage(): void
    {
        $this->showSuccessMessage = false;
        $this->successMessage = '';
    }

    public function render()
    {
        return view('livewire.market-sizing-analysis-display');
    }
} 