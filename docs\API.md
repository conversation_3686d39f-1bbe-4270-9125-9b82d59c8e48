# API Documentation

## Overview

The Venture Discovery Platform provides RESTful APIs for managing projects, generating AI content, and integrating with external services. The API follows Laravel conventions and uses JSON for data exchange.

## Authentication

### API Token Authentication
```php
// Generate API token for user
$token = $user->createToken('api-token')->plainTextToken;

// Use token in requests
Authorization: Bearer {token}
```

### Session Authentication
For web-based requests, standard Laravel session authentication is used.

## Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:8000/api
```

## Content Types
- Request: `application/json`
- Response: `application/json`

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/login
Login user and get API token.

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "name": "<PERSON>",
            "email": "<EMAIL>"
        },
        "token": "1|abc123..."
    }
}
```

#### POST /api/auth/logout
Logout user and revoke token.

**Response:**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

### Account Endpoints

#### GET /api/accounts
List user's accounts.

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "My Company",
            "slug": "my-company",
            "description": "Company description",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

#### POST /api/accounts
Create new account.

**Request:**
```json
{
    "name": "New Company",
    "description": "Company description"
}
```

#### GET /api/accounts/{id}
Get account details.

#### PUT /api/accounts/{id}
Update account.

#### DELETE /api/accounts/{id}
Delete account.

### Project Endpoints

#### GET /api/accounts/{accountId}/projects
List account projects.

**Query Parameters:**
- `status` - Filter by status (draft, active, completed, archived)
- `page` - Page number for pagination
- `per_page` - Items per page (default: 15)

**Response:**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "My Startup Idea",
                "description": "Revolutionary app concept",
                "status": "active",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "meta": {
            "current_page": 1,
            "total": 10,
            "per_page": 15
        }
    }
}
```

#### POST /api/accounts/{accountId}/projects
Create new project.

**Request:**
```json
{
    "name": "Project Name",
    "description": "Project description",
    "status": "draft"
}
```

#### GET /api/projects/{id}
Get project details with generated content.

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "My Project",
        "description": "Project description",
        "status": "active",
        "generated_content": [
            {
                "id": 1,
                "type": "lean_canvas",
                "section": "problem",
                "content": {...},
                "status": "completed"
            }
        ]
    }
}
```

#### PUT /api/projects/{id}
Update project.

#### DELETE /api/projects/{id}
Delete project.

### Content Generation Endpoints

#### POST /api/projects/{id}/generate/lean-canvas
Generate lean canvas section.

**Request:**
```json
{
    "section": "problem",
    "context": "Additional context for generation"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "type": "lean_canvas",
        "section": "problem",
        "status": "generating",
        "job_id": "abc123"
    }
}
```

#### POST /api/projects/{id}/generate/hypotheses
Generate critical hypotheses.

**Request:**
```json
{
    "context": "Business context and assumptions"
}
```

#### POST /api/projects/{id}/generate/questionnaire
Generate interview questionnaire.

**Request:**
```json
{
    "target_audience": "Early adopters",
    "focus_areas": ["problem", "solution", "market"]
}
```

#### POST /api/projects/{id}/generate/storytelling
Generate storytelling content.

**Request:**
```json
{
    "type": "brand_wheel",
    "context": "Brand positioning context"
}
```

#### GET /api/generated-content/{id}
Get generated content details.

#### PUT /api/generated-content/{id}
Update generated content.

#### DELETE /api/generated-content/{id}
Delete generated content.

### Job Status Endpoints

#### GET /api/jobs/{jobId}/status
Check generation job status.

**Response:**
```json
{
    "success": true,
    "data": {
        "job_id": "abc123",
        "status": "completed",
        "progress": 100,
        "result": {
            "content_id": 1,
            "generated_at": "2024-01-01T00:00:00Z"
        }
    }
}
```

## WebSocket Events

### Real-time Updates
The platform uses Laravel Reverb for real-time updates.

#### Content Generation Events
```javascript
// Listen for content generation completion
Echo.private(`project.${projectId}`)
    .listen('ContentGenerated', (e) => {
        console.log('Content generated:', e.content);
    });
```

#### Project Updates
```javascript
// Listen for project updates
Echo.private(`account.${accountId}`)
    .listen('ProjectUpdated', (e) => {
        console.log('Project updated:', e.project);
    });
```

## Error Handling

### Error Response Format
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    },
    "code": "ERROR_CODE"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Server Error

### Common Error Codes
- `VALIDATION_ERROR` - Request validation failed
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `RATE_LIMITED` - Too many requests
- `GENERATION_FAILED` - AI content generation failed

## Rate Limiting

### Default Limits
- Authenticated users: 60 requests per minute
- Content generation: 10 requests per minute
- Guest users: 20 requests per minute

### Rate Limit Headers
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## Pagination

### Query Parameters
- `page` - Page number (default: 1)
- `per_page` - Items per page (default: 15, max: 100)

### Response Format
```json
{
    "data": [...],
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "per_page": 15,
        "to": 15,
        "total": 75
    },
    "links": {
        "first": "http://example.com/api/resource?page=1",
        "last": "http://example.com/api/resource?page=5",
        "prev": null,
        "next": "http://example.com/api/resource?page=2"
    }
}
```

## SDK and Integration

### JavaScript SDK Example
```javascript
class VentureDiscoveryAPI {
    constructor(baseURL, token) {
        this.baseURL = baseURL;
        this.token = token;
    }
    
    async request(method, endpoint, data = null) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: data ? JSON.stringify(data) : null
        });
        
        return response.json();
    }
    
    async generateLeanCanvas(projectId, section, context) {
        return this.request('POST', `/projects/${projectId}/generate/lean-canvas`, {
            section,
            context
        });
    }
}
```

### PHP SDK Example
```php
class VentureDiscoveryClient
{
    private $baseUrl;
    private $token;
    
    public function __construct(string $baseUrl, string $token)
    {
        $this->baseUrl = $baseUrl;
        $this->token = $token;
    }
    
    public function generateLeanCanvas(int $projectId, string $section, string $context): array
    {
        return $this->request('POST', "/projects/{$projectId}/generate/lean-canvas", [
            'section' => $section,
            'context' => $context
        ]);
    }
    
    private function request(string $method, string $endpoint, array $data = []): array
    {
        // Implementation using Guzzle or similar HTTP client
    }
}
```

## Testing

### API Testing
```bash
# Run API tests
php artisan test --filter=ApiTest

# Test specific endpoint
php artisan test tests/Feature/Api/ProjectApiTest.php
```

### Example Test
```php
public function test_can_create_project()
{
    $user = User::factory()->create();
    $account = Account::factory()->create();
    $account->users()->attach($user);
    
    $response = $this->actingAs($user)
        ->postJson("/api/accounts/{$account->id}/projects", [
            'name' => 'Test Project',
            'description' => 'Test Description'
        ]);
    
    $response->assertStatus(201)
        ->assertJsonStructure([
            'success',
            'data' => ['id', 'name', 'description']
        ]);
}
``` 