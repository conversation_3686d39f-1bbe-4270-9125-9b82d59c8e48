<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use OpenAI\Client;
use OpenAI\Exceptions\ErrorException;
use OpenAI\Exceptions\TransporterException;

class OpenAiService
{
    protected Client $client;

    protected array $config;

    public function __construct()
    {
        $this->config = config('openai');

        if (empty($this->config['api_key'])) {
            throw new Exception('OpenAI API key is not configured. Please set OPENAI_API_KEY in your environment.');
        }

        $this->client = \OpenAI::client($this->config['api_key'], $this->config['organization'] ?? null);
    }

    /**
     * Generate content using OpenAI's chat completion API
     *
     * @param  string  $prompt  The user prompt
     * @param  string|null  $systemPrompt  Optional system prompt
     * @param  string|null  $model  Model to use (defaults to config)
     * @param  array  $options  Additional options for the API call
     * @return string Generated content
     *
     * @throws Exception
     */
    public function generateContent(
        string $prompt,
        ?string $systemPrompt = null,
        ?string $model = null,
        array $options = []
    ): string {
        $model = $model ?? $this->config['models']['chat'];
        $cacheKey = $this->getCacheKey($prompt, $systemPrompt, $model, $options);

        if ($this->config['cache']['enabled'] && Cache::has($cacheKey)) {
            $this->log('info', 'Retrieved cached response', ['cache_key' => $cacheKey]);

            return Cache::get($cacheKey);
        }

        $messages = [];

        if ($systemPrompt) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }

        $messages[] = ['role' => 'user', 'content' => $prompt];

        $requestData = array_merge([
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $this->config['limits']['max_tokens'],
            'temperature' => $this->config['limits']['temperature'],
            'top_p' => $this->config['limits']['top_p'],
            'frequency_penalty' => $this->config['limits']['frequency_penalty'],
            'presence_penalty' => $this->config['limits']['presence_penalty'],
        ], $options);

        $response = $this->executeWithRetry(function () use ($requestData) {
            return $this->client->chat()->create($requestData);
        });

        $content = $response->choices[0]->message->content;

        // Track costs if enabled
        if ($this->config['cost_tracking']['enabled']) {
            $this->trackCost($response, $model);
        }

        // Cache the response if enabled
        if ($this->config['cache']['enabled']) {
            Cache::put($cacheKey, $content, $this->config['cache']['ttl']);
        }

        $this->log('info', 'Generated content successfully', [
            'model' => $model,
            'prompt_length' => strlen($prompt),
            'response_length' => strlen($content),
            'tokens_used' => $response->usage->totalTokens ?? 0,
        ]);

        return $content;
    }

    /**
     * Generate structured content based on a template
     *
     * @param  string  $prompt  The user prompt
     * @param  array  $structure  Expected structure/schema
     * @param  string|null  $model  Model to use
     * @return array Structured response
     *
     * @throws Exception
     */
    public function generateStructuredContent(
        string $prompt,
        array $structure,
        ?string $model = null
    ): array {
        $systemPrompt = $this->buildStructuredPrompt($structure);
        $content = $this->generateContent($prompt, $systemPrompt, $model);

        // Extract JSON from markdown code blocks if present
        $jsonContent = $this->extractJsonFromMarkdown($content);

        // Try to parse as JSON
        $decoded = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log('warning', 'Failed to parse structured response as JSON', [
                'original_content' => $content,
                'extracted_json' => $jsonContent,
                'json_error' => json_last_error_msg(),
            ]);

            // Fallback: return content as text in the first field of structure
            $firstKey = array_key_first($structure);

            return [$firstKey => $content];
        }

        return $decoded;
    }

    /**
     * Count tokens in a text string (approximate)
     *
     * @return int Approximate token count
     */
    public function countTokens(string $text): int
    {
        // Rough approximation: 1 token ≈ 4 characters for English text
        return (int) ceil(strlen($text) / 4);
    }

    /**
     * Execute a function with retry logic
     *
     * @return mixed
     *
     * @throws Exception
     */
    protected function executeWithRetry(callable $function)
    {
        $maxAttempts = $this->config['retry']['max_attempts'];
        $delay = $this->config['retry']['delay_ms'];
        $backoffMultiplier = $this->config['retry']['backoff_multiplier'];

        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                return $function();
            } catch (ErrorException $e) {
                $this->log('error', 'OpenAI API error', [
                    'attempt' => $attempt,
                    'error' => $e->getMessage(),
                    'error_type' => $e->getErrorType(),
                ]);

                if ($attempt === $maxAttempts || ! $this->isRetryableError($e)) {
                    throw $e;
                }

                $this->sleep($delay);
                $delay = (int) ($delay * $backoffMultiplier);
            } catch (TransporterException $e) {
                $this->log('error', 'OpenAI transport error', [
                    'attempt' => $attempt,
                    'error' => $e->getMessage(),
                ]);

                if ($attempt === $maxAttempts) {
                    throw $e;
                }

                $this->sleep($delay);
                $delay = (int) ($delay * $backoffMultiplier);
            }
        }

        throw new Exception('Max retry attempts exceeded');
    }

    /**
     * Check if an error is retryable
     */
    protected function isRetryableError(ErrorException $e): bool
    {
        $retryableTypes = ['rate_limit_exceeded', 'server_error', 'timeout'];

        return in_array($e->getErrorType(), $retryableTypes);
    }

    /**
     * Extract JSON content from markdown code blocks
     *
     * @param  string  $content  The content that may contain JSON in markdown blocks
     * @return string The extracted JSON or original content if no blocks found
     */
    protected function extractJsonFromMarkdown(string $content): string
    {
        // Pattern to match JSON code blocks: ```json ... ``` or ``` ... ```
        $patterns = [
            '/```json\s*\n(.*?)\n```/s',  // ```json ... ```
            '/```\s*\n(\{.*?\})\s*\n```/s',  // ``` { ... } ```
            '/```json\s*(.*?)```/s',  // ```json...``` (without newlines)
            '/```\s*(\{.*?\})\s*```/s',  // ```{...}``` (without newlines)
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $extracted = trim($matches[1]);
                // Additional cleanup - remove any leading/trailing whitespace and newlines
                $extracted = preg_replace('/^\s+|\s+$/m', '', $extracted);

                return $extracted;
            }
        }

        // If no markdown blocks found, return original content
        return trim($content);
    }

    /**
     * Build a system prompt for structured content generation
     */
    protected function buildStructuredPrompt(array $structure): string
    {
        $prompt = 'You are a business strategist and startup advisor. ';
        $prompt .= "Please respond with a valid JSON object that follows this exact structure:\n\n";
        $prompt .= json_encode($structure, JSON_PRETTY_PRINT);
        $prompt .= "\n\nIMPORTANT: Respond with ONLY the raw JSON object. ";
        $prompt .= 'Do NOT wrap the JSON in markdown code blocks (```json). ';
        $prompt .= 'Do NOT include any explanatory text before or after the JSON. ';
        $prompt .= 'Return only valid JSON that can be parsed directly.';

        return $prompt;
    }

    /**
     * Generate cache key for a request
     */
    protected function getCacheKey(string $prompt, ?string $systemPrompt, string $model, array $options): string
    {
        $data = [
            'prompt' => $prompt,
            'system_prompt' => $systemPrompt,
            'model' => $model,
            'options' => $options,
        ];

        return $this->config['cache']['prefix'].':'.md5(serialize($data));
    }

    /**
     * Track API costs
     *
     * @param  mixed  $response
     */
    protected function trackCost($response, string $model): void
    {
        if (! isset($response->usage) || ! isset($this->config['cost_tracking']['pricing'][$model])) {
            return;
        }

        $pricing = $this->config['cost_tracking']['pricing'][$model];
        $inputTokens = $response->usage->promptTokens ?? 0;
        $outputTokens = $response->usage->completionTokens ?? 0;

        $inputCost = ($inputTokens / 1000) * $pricing['input'];
        $outputCost = ($outputTokens / 1000) * $pricing['output'];
        $totalCost = $inputCost + $outputCost;

        $this->log('info', 'API cost tracked', [
            'model' => $model,
            'input_tokens' => $inputTokens,
            'output_tokens' => $outputTokens,
            'total_tokens' => $inputTokens + $outputTokens,
            'input_cost' => $inputCost,
            'output_cost' => $outputCost,
            'total_cost' => $totalCost,
        ]);
    }

    /**
     * Log a message if logging is enabled
     */
    protected function log(string $level, string $message, array $context = []): void
    {
        if (! $this->config['logging']['enabled']) {
            return;
        }

        Log::channel($this->config['logging']['channel'])->log($level, $message, $context);
    }

    /**
     * Sleep for the specified number of milliseconds
     */
    protected function sleep(int $milliseconds): void
    {
        usleep($milliseconds * 1000);
    }
}
