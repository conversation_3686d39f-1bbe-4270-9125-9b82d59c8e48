<?php

namespace App\Filament\App\Pages;

use App\Filament\App\Widgets\ProjectStatsOverview;
use App\Filament\App\Widgets\RecentProjectsTableWidget;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $title = 'Venture Discovery Dashboard';

    public function getWidgets(): array
    {
        return [
            ProjectStatsOverview::class,
            RecentProjectsTableWidget::class,
        ];
    }

    public function getColumns(): int|string|array
    {
        return 2;
    }
}
