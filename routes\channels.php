<?php

use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('project.{projectId}', function ($user, $projectId) {
    // Allow access if user owns the project or has access to it
    return \App\Models\Project::where('id', $projectId)
        ->where('account_id', $user->id)
        ->exists();
});
