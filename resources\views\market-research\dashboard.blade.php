<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Market Research Dashboard - {{ config('app.name', 'Venture Discovery') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-slate-50 antialiased">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ url('/') }}" class="text-xl font-bold text-gray-900">
                            {{ config('app.name', 'Venture Discovery') }}
                        </a>
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="{{ route('market-research') }}" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                                Market Research
                            </a>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-700">{{ auth()->user()->name ?? 'User' }}</span>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-sm text-gray-500 hover:text-gray-700">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Dashboard Container -->
                <div id="dashboard-container" class="bg-slate-50 min-h-screen">
                    <!-- User Input Section -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="industry-input" class="block text-sm font-medium text-gray-700 mb-2">
                                    Target Industry
                                </label>
                                <input 
                                    type="text" 
                                    id="industry-input" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="e.g., Renewable Energy, FinTech, Healthcare"
                                >
                            </div>
                            <div>
                                <label for="region-input" class="block text-sm font-medium text-gray-700 mb-2">
                                    Target Region/Country
                                </label>
                                <input 
                                    type="text" 
                                    id="region-input" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="e.g., United States, Europe, Southeast Asia"
                                >
                            </div>
                        </div>
                        <div class="flex space-x-4">
                            <button 
                                id="research-button" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200 flex items-center"
                            >
                                <span class="mr-2">🚀</span>
                                Research Market
                            </button>
                            <button 
                                id="reset-button" 
                                class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200 flex items-center"
                            >
                                <span class="mr-2">🔄</span>
                                Reset Dashboard
                            </button>
                        </div>
                    </div>

                    <!-- Dashboard Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg p-8 mb-8 text-white">
                        <h1 class="text-3xl font-bold mb-2">Market Research Dashboard</h1>
                        <p class="text-blue-100">
                            Focus: <span id="display-industry">[Enter Industry]</span> in <span id="display-region">[Enter Region]</span>
                        </p>
                    </div>

                    <!-- KPI Row -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <!-- Market Attractiveness Score -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Overall Market Attractiveness Score ✨</h3>
                            <div id="attractiveness-content" class="text-center">
                                <div class="text-gray-500">Enter industry/region to generate score.</div>
                            </div>
                        </div>

                        <!-- Market Size -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Conceptual Market Size ✨</h3>
                            <div id="market-size-content" class="text-center">
                                <div class="text-gray-500">N/A</div>
                            </div>
                        </div>

                        <!-- Opportunity Zones -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Opportunity Zones Identified ✨</h3>
                            <div id="opportunity-zones-content" class="text-center">
                                <div class="text-4xl font-bold text-blue-600 mb-2">0</div>
                                <div class="text-gray-500">Enter industry/region to identify zones.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Insight Cards Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Research Scope Highlights -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Research Scope Highlights ✨</h3>
                            <div id="research-scope-content" class="text-gray-500">
                                Enter industry/region and click 'Research Market' to generate insights here.
                            </div>
                        </div>

                        <!-- Strategic Implications -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Key Strategic Implications ✨</h3>
                            <div id="strategic-implications-content" class="text-gray-500">
                                Enter industry/region and click 'Research Market' to generate insights here.
                            </div>
                        </div>

                        <!-- Customer Pain Points -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Customer Pain Points / Unmet Needs ✨</h3>
                            <div id="pain-points-content" class="text-gray-500">
                                Enter industry/region and click 'Research Market' to generate insights here.
                            </div>
                        </div>

                        <!-- Opportunity Zones Details -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Highlighted Opportunity Zones ✨</h3>
                            <div id="opportunity-zones-details-content" class="text-gray-500">
                                Enter industry/region and click 'Research Market' to generate insights here.
                            </div>
                        </div>
                    </div>

                    <!-- Competitive Landscape -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Simplified Competitive Landscape ✨</h3>
                        <div id="competitive-landscape-content" class="text-gray-500">
                            Enter industry/region and click 'Research Market' to generate competitive analysis here.
                        </div>
                    </div>

                    <!-- Enablers & Barriers -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Key Enablers & Barriers ✨</h3>
                        <div id="enablers-barriers-content" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-green-700 mb-2">Enablers</h4>
                                <div class="text-gray-500">Enter industry/region to generate enablers.</div>
                            </div>
                            <div>
                                <h4 class="font-medium text-red-700 mb-2">Barriers</h4>
                                <div class="text-gray-500">Enter industry/region to generate barriers.</div>
                            </div>
                        </div>
                    </div>

                    <!-- SWOT Analysis -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Market SWOT Analysis ✨</h3>
                        <div id="swot-content" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="border border-green-200 rounded-lg p-4">
                                <h4 class="font-medium text-green-700 mb-2">Strengths</h4>
                                <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                            </div>
                            <div class="border border-red-200 rounded-lg p-4">
                                <h4 class="font-medium text-red-700 mb-2">Weaknesses</h4>
                                <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                            </div>
                            <div class="border border-blue-200 rounded-lg p-4">
                                <h4 class="font-medium text-blue-700 mb-2">Opportunities</h4>
                                <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                            </div>
                            <div class="border border-yellow-200 rounded-lg p-4">
                                <h4 class="font-medium text-yellow-700 mb-2">Threats</h4>
                                <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="text-center text-gray-500 text-sm py-8">
                        <p>Dashboard generated on: <span id="generation-date">{{ date('F j, Y') }}</span></p>
                        <p class="mt-2">This data is AI-generated for illustrative purposes and should not be considered as professional market research advice.</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal for Gemini Insights -->
    <div id="insights-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Insights</h3>
                        <button id="modal-close" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="modal-body" class="text-gray-700">
                        <!-- Modal content will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Basic JavaScript functionality for the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            const industryInput = document.getElementById('industry-input');
            const regionInput = document.getElementById('region-input');
            const researchButton = document.getElementById('research-button');
            const resetButton = document.getElementById('reset-button');
            const displayIndustry = document.getElementById('display-industry');
            const displayRegion = document.getElementById('display-region');
            const modal = document.getElementById('insights-modal');
            const modalClose = document.getElementById('modal-close');

            // Research Market button functionality
            researchButton.addEventListener('click', function() {
                const industry = industryInput.value.trim();
                const region = regionInput.value.trim();

                if (!industry || !region) {
                    alert('Please enter both industry and region before researching.');
                    return;
                }

                // Update header
                displayIndustry.textContent = industry;
                displayRegion.textContent = region;

                // Show loading state
                researchButton.disabled = true;
                researchButton.innerHTML = '<span class="mr-2">⏳</span>Updating...';

                // TODO: Implement API call to generate insights
                // For now, just simulate loading
                setTimeout(() => {
                    researchButton.disabled = false;
                    researchButton.innerHTML = '<span class="mr-2">🚀</span>Research Market';
                    alert('Market research functionality will be implemented in the next tasks!');
                }, 2000);
            });

            // Reset Dashboard button functionality
            resetButton.addEventListener('click', function() {
                industryInput.value = '';
                regionInput.value = '';
                displayIndustry.textContent = '[Enter Industry]';
                displayRegion.textContent = '[Enter Region]';

                // Reset all content areas to placeholder state
                document.getElementById('attractiveness-content').innerHTML = '<div class="text-gray-500">Enter industry/region to generate score.</div>';
                document.getElementById('market-size-content').innerHTML = '<div class="text-gray-500">N/A</div>';
                document.getElementById('opportunity-zones-content').innerHTML = '<div class="text-4xl font-bold text-blue-600 mb-2">0</div><div class="text-gray-500">Enter industry/region to identify zones.</div>';
                
                // Reset other content areas
                const contentAreas = [
                    'research-scope-content',
                    'strategic-implications-content', 
                    'pain-points-content',
                    'opportunity-zones-details-content',
                    'competitive-landscape-content'
                ];
                
                contentAreas.forEach(id => {
                    document.getElementById(id).innerHTML = '<div class="text-gray-500">Enter industry/region and click \'Research Market\' to generate insights here.</div>';
                });

                // Reset enablers & barriers
                document.getElementById('enablers-barriers-content').innerHTML = `
                    <div>
                        <h4 class="font-medium text-green-700 mb-2">Enablers</h4>
                        <div class="text-gray-500">Enter industry/region to generate enablers.</div>
                    </div>
                    <div>
                        <h4 class="font-medium text-red-700 mb-2">Barriers</h4>
                        <div class="text-gray-500">Enter industry/region to generate barriers.</div>
                    </div>
                `;

                // Reset SWOT
                document.getElementById('swot-content').innerHTML = `
                    <div class="border border-green-200 rounded-lg p-4">
                        <h4 class="font-medium text-green-700 mb-2">Strengths</h4>
                        <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                    </div>
                    <div class="border border-red-200 rounded-lg p-4">
                        <h4 class="font-medium text-red-700 mb-2">Weaknesses</h4>
                        <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                    </div>
                    <div class="border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-700 mb-2">Opportunities</h4>
                        <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                    </div>
                    <div class="border border-yellow-200 rounded-lg p-4">
                        <h4 class="font-medium text-yellow-700 mb-2">Threats</h4>
                        <div class="text-gray-500">Enter industry/region to generate SWOT analysis.</div>
                    </div>
                `;
            });

            // Modal functionality
            modalClose.addEventListener('click', function() {
                modal.classList.add('hidden');
            });

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html> 